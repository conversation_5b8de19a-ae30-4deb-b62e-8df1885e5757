﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="QMOV2Service\bvjlksrg.13e~" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AlibabaCloud.SDK.Dingtalk" Version="2.0.75" />
    <PackageReference Include="AlibabaCloud.SDK.Facebody20191230" Version="4.1.2" />
    <PackageReference Include="DocumentFormat.OpenXml" Version="3.3.0" />
    <PackageReference Include="HtmlAgilityPack" Version="1.11.61" />
    <PackageReference Include="LinqKit" Version="1.2.2" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
    <PackageReference Include="MimeKit" Version="4.7.1" />
    <PackageReference Include="Minio" Version="4.0.7" />
    <PackageReference Include="MongoDB.Driver" Version="2.25.0" />
    <PackageReference Include="NETCore.MailKit" Version="2.1.0" />
    <PackageReference Include="NPOI" Version="2.5.6" />
    <PackageReference Include="Npoi.Mapper" Version="4.1.0" />
    <PackageReference Include="System.IO.Compression.ZipFile" Version="4.3.0" />
    <PackageReference Include="topsdk-netcore" Version="1.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Common.Model\Common.Model.csproj" />
    <ProjectReference Include="..\Common.Repository\Common.Repository.csproj" />
    <ProjectReference Include="..\Common.Service\Common.Service.csproj" />
    <ProjectReference Include="..\QMD.DAL\QMD.DAL.csproj" />
    <ProjectReference Include="..\QMD.Env\QMD.Env.csproj" />
    <ProjectReference Include="..\QMD.Model\QMD.Model.csproj" />
    <ProjectReference Include="..\QMD.Repository\QMD.Repository.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="QMOV2Service\AI\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-CDN-V3.0-20240802 %281%29.docx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="QMOV2Service\AI\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-IPRAN-V3.2-20250527.docx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="QMOV2Service\AI\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PON-V3.0-20240802.docx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="QMOV2Service\AI\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PTN&amp;SPN-V3.0-20240802.docx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="QMOV2Service\AI\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-传输网管-V3.0-20240802.docx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="QMOV2Service\AI\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-服务器存储-V3.0-20240802.docx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="QMOV2Service\AI\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-终端-V3.0-20240802 %281%29.docx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="QMOV2Service\AI\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-路由器交换机-V3.0-20240802 %281%29.docx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="QMOV2Service\AI\XX月XX日0：00-04：00XX网络XX操作网络变更操作技术方案（模板）-OTN-V3.0-20240802.docx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
