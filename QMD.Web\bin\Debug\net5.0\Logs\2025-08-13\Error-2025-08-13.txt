2025-08-13 15:05:02.4120 | Job qmd_itr_jobgroup.syncItrOrder<PERSON><PERSON><PERSON><PERSON> threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:05:02.4120 | Job qmd_itr_jobgroup.syncItrOrder<PERSON><PERSON><PERSON><PERSON> threw an exception. <PERSON> threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:13:03.2442 | Job qmd_qmo_jobgroup.escalatedTaskR<PERSON><PERSON><PERSON><PERSON> threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:13:03.2442 | Job qmd_qmo_jobgroup.syncExpire<PERSON>ask<PERSON><PERSON> threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:13:03.2518 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:13:03.2518 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:13:03.3119 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:13:03.3119 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:13:03.3289 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:13:03.3289 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:13:03.3634 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:13:03.3634 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:13:03.3990 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:13:03.3990 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:13:03.4096 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:13:03.4096 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:13:03.4653 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 15:13:04.2640 | 从docx文件提取文本失败: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-服务器存储-V3.0-20240802.docx A shared part is referenced by multiple source parts with a different relationship type. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__35.MoveNext => AIServices.ExtractTextFromDocx 

2025-08-13 15:13:04.4007 | 从docx文件提取文本失败: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-路由器交换机-V3.0-20240802 (1).docx A shared part is referenced by multiple source parts with a different relationship type. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__35.MoveNext => AIServices.ExtractTextFromDocx 

2025-08-13 15:13:04.6106 | 从docx文件提取文本失败: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-终端-V3.0-20240802 (1).docx A shared part is referenced by multiple source parts with a different relationship type. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__35.MoveNext => AIServices.ExtractTextFromDocx 

2025-08-13 15:13:04.7970 | 从docx文件提取文本失败: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-CDN-V3.0-20240802 (1).docx A shared part is referenced by multiple source parts with a different relationship type. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__35.MoveNext => AIServices.ExtractTextFromDocx 

2025-08-13 15:13:05.0755 | 从docx文件提取文本失败: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PON-V3.0-20240802.docx A shared part is referenced by multiple source parts with a different relationship type. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__35.MoveNext => AIServices.ExtractTextFromDocx 

2025-08-13 15:13:05.2451 | 从docx文件提取文本失败: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PTN&SPN-V3.0-20240802.docx A shared part is referenced by multiple source parts with a different relationship type. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__35.MoveNext => AIServices.ExtractTextFromDocx 

2025-08-13 15:13:05.4108 | 从docx文件提取文本失败: D:\Tempfiles\XX月XX日0：00-04：00XX网络XX操作网络变更操作技术方案（模板）-OTN-V3.0-20240802.docx A shared part is referenced by multiple source parts with a different relationship type. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__35.MoveNext => AIServices.ExtractTextFromDocx 

2025-08-13 15:14:00.0310 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:14:00.0310 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:14:00.0310 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:14:00.0310 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:15:00.0345 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:15:00.0345 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:16:00.0307 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:16:00.0307 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:16:00.0307 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:16:00.0307 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:18:00.0369 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:18:00.0637 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:18:00.0637 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:18:00.0637 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:20:00.0330 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:20:00.0510 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:20:00.0825 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:20:00.0825 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:20:00.0950 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:20:00.0950 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:20:00.0950 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:20:00.0950 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:20:00.0950 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:20:00.0950 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:20:00.1899 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 15:22:00.0185 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:22:00.0185 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:22:00.0315 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:22:00.0315 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:24:00.0401 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:24:00.0401 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:24:00.0401 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:24:00.0401 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:25:00.0354 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:25:00.0354 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:26:03.7545 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:26:03.7994 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:26:03.8132 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:26:03.8132 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:28:00.0375 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:28:00.0375 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:28:00.0375 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:28:00.0375 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:30:00.0517 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:30:00.0517 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:30:00.1214 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeRecordJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:30:00.1701 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:30:00.1701 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:30:00.1701 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:30:00.1902 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:30:00.1902 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:30:00.1902 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:30:00.2421 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:30:00.2421 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:30:00.2421 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:30:00.2867 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 15:32:00.0374 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:32:00.0374 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:32:00.0495 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:32:00.0495 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:34:00.0593 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:34:00.0593 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:34:00.0607 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:34:00.0607 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:35:00.0321 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:35:00.0321 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:36:00.0616 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:36:00.0956 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:36:00.0956 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:36:00.0956 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:38:00.0308 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:38:00.0308 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:38:00.0308 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:38:00.0459 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:40:00.0349 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:40:00.0938 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:40:00.1527 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:40:00.1527 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:40:00.1527 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:40:00.1527 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:40:00.1540 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:40:00.1540 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:40:00.1540 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:40:00.1540 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:40:00.2472 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 15:42:00.0447 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:42:00.0447 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:42:00.0447 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:42:00.0447 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:44:00.0432 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:44:00.0502 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:44:00.0502 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:44:00.0502 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:45:00.0299 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:45:00.0299 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:48:22.4434 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:48:22.4434 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:48:22.4434 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:48:22.4434 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:50:00.1027 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:50:00.1027 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:50:00.1088 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:50:00.1088 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:50:00.1088 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:50:00.1088 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:50:00.1088 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:50:00.1088 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:50:00.1088 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:50:00.1088 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:50:00.1797 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 15:52:00.0420 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:52:00.0420 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:52:00.0420 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:52:00.0420 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 15:54:07.7109 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 15:54:07.7109 | Job qmd_itr_jobgroup.syncDingtalkUserJob threw an unhandled Exception:  No service for type 'QMD.Job.Dingtalk.SyncDingtalkUserJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 15:54:07.7109 | Job qmd_qmo_jobgroup.syncNoticeTaskTodoJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskTodoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 15:54:07.7109 | Job qmd_qmo_jobgroup.syncNoticeTaskResultJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskResultJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 15:54:07.7109 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an unhandled Exception:  No service for type 'QMD.Job.Nmosp.SyncNmospUserInfoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 15:54:07.7109 | Job qmd_qa_jobgroup.syncQANetJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncQaInfoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 15:54:07.7109 | Job qmd_itr_jobgroup.syncDingtalkUserJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:54:07.7109 | Job qmd_qmo_jobgroup.syncNoticeTaskTodoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:54:07.7109 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:54:07.7109 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:54:07.7109 | Job qmd_qa_jobgroup.syncQANetJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:54:07.7109 | Job qmd_qmo_jobgroup.syncNoticeTaskResultJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:54:08.9094 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 15:55:00.0282 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 15:55:00.0297 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:56:00.0456 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 15:56:00.0456 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 15:56:00.0456 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:56:00.0456 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:58:00.0303 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:58:00.0303 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:58:00.0303 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:58:00.0303 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:00:00.0376 | Job qmd_itr_jobgroup.syncItrOrderInfoJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderInfoJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:00:00.0776 | Job qmd_itr_jobgroup.syncItrOrderInfoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:00:00.1277 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:00:00.1277 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:00:00.1912 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:00:00.1912 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an unhandled Exception:  No service for type 'QMD.Job.Nmosp.SyncNmospUserInfoJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:00:00.1912 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeRecordJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:00:00.1912 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:00:00.1912 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:00:00.1912 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:00:00.2283 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:00:00.2283 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:00:00.2283 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:00:00.2377 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:00:00.2732 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:00:00.2732 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:00:00.3608 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 16:02:00.0454 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:02:00.0454 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:02:00.0454 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:02:00.0454 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:04:00.0466 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:04:00.0466 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:04:00.0466 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:04:00.0466 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:05:00.0318 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:05:00.0318 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:06:00.0367 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:06:00.0367 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:06:00.0367 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:06:00.0367 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:08:00.0310 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:08:00.0670 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:08:00.0670 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:08:00.0670 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:10:00.0421 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:10:00.0489 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:10:00.1214 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:10:00.1250 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:10:00.1250 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:10:00.1250 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:10:00.1250 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:10:00.1250 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:10:00.1250 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:10:00.1250 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:10:00.2106 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 16:12:00.0468 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:12:00.0468 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:12:00.0468 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:12:00.0468 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 16:14:00.0283 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:14:00.0332 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:14:00.0332 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:14:00.0332 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:15:00.0313 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:15:00.0313 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:16:00.0480 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:16:00.0480 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:16:00.0480 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:16:00.0480 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:18:00.0366 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:18:00.0366 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:18:00.0366 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:18:00.0366 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:20:00.0446 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:20:00.0572 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:20:00.1180 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:20:00.1180 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:20:00.1235 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:20:00.1235 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:20:00.1235 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:20:00.1235 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:20:00.1235 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:20:00.1235 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:20:00.1913 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 16:22:00.0353 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:22:00.0353 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:22:00.0475 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:22:00.0475 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:24:00.0261 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:24:00.0310 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:24:00.0310 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:24:00.0310 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:25:00.0264 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:25:00.0264 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:26:00.0372 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:26:00.0372 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:26:00.0457 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:26:00.0457 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:28:00.0292 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:28:00.0350 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:28:00.0350 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:28:00.0350 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:30:00.0337 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:30:00.0837 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:30:00.1245 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:30:00.1245 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeRecordJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:30:00.1245 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:30:00.1775 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:30:00.2099 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:30:00.2164 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:30:00.2164 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:30:00.2164 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:30:00.2164 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:30:00.2531 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:30:00.3036 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 16:32:00.0327 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:32:00.0327 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:32:00.0452 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:32:00.0452 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:34:00.0324 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:34:00.0324 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:34:00.0324 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:34:00.0324 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:35:00.0302 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:35:00.0302 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:36:00.0423 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:36:00.0471 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:36:00.0471 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:36:00.0471 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:38:00.0301 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:38:00.0301 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:38:00.0301 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:38:00.0301 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:40:00.0296 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:40:00.0296 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:40:00.0937 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:40:00.0937 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:40:00.0937 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:40:00.0937 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:40:00.0937 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:40:00.0937 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:40:00.1132 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:40:00.1132 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:40:00.1789 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 16:42:00.0288 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:42:00.0364 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:42:00.0733 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:42:00.0733 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:44:00.0385 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:44:00.0385 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:44:00.0385 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:44:00.0385 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:45:00.0188 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:45:00.0188 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:46:00.0424 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:46:00.0450 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:46:00.0450 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:46:00.0450 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:48:00.0339 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:48:00.0339 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:48:00.0339 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:48:00.0339 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:50:00.0500 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:50:00.0884 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:50:00.1517 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:50:00.1612 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:50:00.1612 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:50:00.1612 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:50:00.1612 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:50:00.1612 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:50:00.1612 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:50:00.1852 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:50:00.2392 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 16:52:00.0450 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:52:00.0450 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:52:00.0450 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:52:00.0450 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:54:00.0407 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:54:00.0457 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:54:00.0457 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:54:00.0457 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:55:00.0333 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:55:00.0333 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:56:00.0174 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:56:00.0174 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:56:00.0174 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:56:00.0300 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:58:00.0358 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:58:00.0358 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 16:58:00.0358 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 16:58:00.0358 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 17:00:00.0442 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 17:00:00.0569 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 17:00:00.0724 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeRecordJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 17:00:00.0876 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an unhandled Exception:  No service for type 'QMD.Job.Nmosp.SyncNmospUserInfoJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 17:00:00.0876 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 17:00:00.1450 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 17:00:00.1450 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 17:00:00.1450 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 17:00:00.1450 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 17:00:00.1450 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 17:00:00.1450 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 17:00:00.1450 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 17:00:00.1816 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 17:00:00.1816 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 17:00:00.2412 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 17:02:00.0287 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 17:02:00.0344 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 17:02:00.0344 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 17:02:00.0344 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 17:04:00.3448 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 17:04:00.9218 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 17:04:00.9218 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 17:04:00.9218 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 17:13:14.1070 | Job qmd_itr_jobgroup.syncDingtalkUserJob threw an unhandled Exception:  No service for type 'QMD.Job.Dingtalk.SyncDingtalkUserJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 17:13:14.1070 | Job qmd_qmo_jobgroup.syncNoticeTaskResultJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskResultJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 17:13:14.1070 | Job qmd_qmo_jobgroup.syncNoticeTaskTodoJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskTodoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 17:13:14.1070 | Job qmd_qa_jobgroup.syncQANetJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncQaInfoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 17:13:14.1070 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 17:13:14.1070 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an unhandled Exception:  No service for type 'QMD.Job.Nmosp.SyncNmospUserInfoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 17:13:14.1107 | Job qmd_itr_jobgroup.syncDingtalkUserJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 17:13:14.1107 | Job qmd_qa_jobgroup.syncQANetJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 17:13:14.1107 | Job qmd_qmo_jobgroup.syncNoticeTaskResultJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 17:13:14.1107 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 17:13:14.1107 | Job qmd_qmo_jobgroup.syncNoticeTaskTodoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 17:13:14.1107 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 17:13:15.2680 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 17:20:51.7638 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 17:20:51.8116 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 17:20:51.8499 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 17:20:51.8853 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 17:20:51.8853 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 17:20:52.8600 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 17:20:52.8600 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 17:20:52.8912 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 17:20:53.4203 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 17:20:54.3865 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 17:20:54.3865 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 17:20:54.3893 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 17:20:55.5099 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 17:27:23.2824 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 17:27:23.3173 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 17:27:23.3173 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 17:27:23.3173 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 17:27:23.3173 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 17:27:23.9439 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 17:28:03.1399 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 17:28:03.1747 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 17:28:03.1747 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 17:28:03.2021 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

