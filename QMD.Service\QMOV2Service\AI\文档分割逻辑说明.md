# Word文档智能分割逻辑说明

## 📋 概述

本文档详细说明了AI服务中Word文档分割功能的实现逻辑，确保代码实现与文档描述完全一致。

---

## 🎯 分割目标

将Word文档按照特定的标识符分割成**4个部分**：

1. **开头到变更操作概述** - 文档开始到【变更操作概述】标识符之前的内容
2. **变更操作概述** - 【变更操作概述】标识符及其后的内容，到【变更操作准备】之前
3. **变更操作准备** - 【变更操作准备】标识符及其后的内容，到【操作实施步骤】之前
4. **操作实施步骤** - 【操作实施步骤】标识符及其后的内容，到文档结尾

---

## 🔧 核心实现方法

### 1. 主入口方法：`SplitDocumentIntoSections()`

**方法签名：**
```csharp
public (bool Success, List<DocumentSection> Sections, string ErrorMessage) SplitDocumentIntoSections(string filePath)
```

**执行流程：**
1. **文档文本提取**：使用 `Common.Utility.WordHelper.ExtractTextFromWord()` 提取完整文档文本
2. **内容验证**：检查提取的文本是否为空
3. **分割处理**：调用 `ExtractSectionsSimple()` 进行实际分割
4. **结果返回**：返回分割成功状态、分割结果列表和错误信息

**容错处理：**
- 文档无法提取时返回错误信息
- 异常情况下记录日志并返回错误状态

### 2. 核心分割方法：`ExtractSectionsSimple()`

**方法签名：**
```csharp
private List<DocumentSection> ExtractSectionsSimple(string fullText)
```

**执行步骤：**

#### 步骤1：查找标识符位置
```csharp
var overviewIndex = FindSectionIndex(fullText, "变更操作概述");
var preparationIndex = FindSectionIndex(fullText, "变更操作准备");  
var implementationIndex = FindSectionIndex(fullText, "操作实施步骤");
```

#### 步骤2：按位置分割内容
- **第一部分**：从文档开始(索引0) 到 `overviewIndex`
- **第二部分**：从 `overviewIndex` 到 `preparationIndex`
- **第三部分**：从 `preparationIndex` 到 `implementationIndex`
- **第四部分**：从 `implementationIndex` 到文档结尾

#### 步骤3：创建分割结果对象
每个部分创建一个 `DocumentSection` 对象，包含：
- `SectionName`：部分名称
- `Content`：提取的文本内容
- `IsFound`：是否成功找到对应标识符
- `MatchMethod`：匹配方式（"正则匹配"或"未找到"）

### 3. 标识符查找方法：`FindSectionIndex()`

**方法签名：**
```csharp
private int FindSectionIndex(string text, string sectionName)
```

**匹配策略（优化版 - 精确优先、严格边界）：**

#### 第一阶段：精确匹配
1. **标准格式**：`【变更操作概述】`
   - 最常见的格式，精确匹配
2. **方括号格式**：`[变更操作概述]`
   - 兼容方括号格式，精确匹配
3. **带空格标准格式**：`【 变更操作概述 】`
   - 容忍标识符内部空格
4. **带空格方括号格式**：`[ 变更操作概述 ]`
   - 容忍方括号内部空格

#### 第二阶段：严格模糊匹配
只有精确匹配失败时才启用：
1. **模糊标准格式**：`【xxx变更操作概述xxx】`
   - 限制：额外字符不超过10个
   - 避免匹配过长内容造成误分割
2. **模糊方括号格式**：`[xxx变更操作概述xxx]`
   - 限制：额外字符不超过10个

#### 边界验证机制
每个匹配都要求：
- **前边界**：匹配前一字符必须是空白、标点、换行或文档开头
- **后边界**：匹配后一字符必须是空白、标点、换行或文档结尾
- **完整性**：确保不会匹配到其他词语的一部分

**返回值：**
- 成功：返回标识符在文档中的字符索引位置
- 失败：返回 -1

### 4. 文本提取方法：`ExtractTextByIndex()`

**方法签名：**
```csharp
private string ExtractTextByIndex(string text, int startIndex, int endIndex)
```

**处理逻辑：**
1. **边界处理**：
   - `startIndex < 0` 时，设置为 0
   - `endIndex < 0` 或超过文本长度时，设置为文本长度
   - `startIndex >= endIndex` 时，返回空字符串

2. **内容提取**：
   - 使用 `text.Substring(startIndex, endIndex - startIndex)`
   - 对结果执行 `Trim()` 去除首尾空白

3. **异常处理**：
   - 捕获提取异常并记录日志
   - 异常时返回空字符串

---

## 📊 数据结构

### DocumentSection 类

```csharp
public class DocumentSection
{
    public string SectionName { get; set; } = string.Empty;    // 部分名称
    public string Content { get; set; } = string.Empty;       // 文本内容
    public int StartIndex { get; set; } = -1;                 // 起始索引（未使用）
    public int EndIndex { get; set; } = -1;                   // 结束索引（未使用）
    public bool IsFound { get; set; } = false;                // 是否找到标识符
    public string MatchMethod { get; set; } = string.Empty;   // 匹配方式
}
```

---

## 🧪 测试方法

### TestDocumentSplittingAsync()

**方法签名：**
```csharp
public async Task<string> TestDocumentSplittingAsync(string testFilePath = null)
```

**测试流程：**
1. **文件选择**：使用指定文件或默认模板文件
2. **分割执行**：调用主分割方法
3. **报告生成**：创建详细的测试报告
4. **结果保存**：将报告保存到 `D:\Tempfiles\简洁分割测试结果.txt`

**报告内容：**
- 测试基本信息（文件名、时间、结果状态）
- 每个部分的详细信息（状态、字符数、内容预览）

---

## ✅ 容错特性

### 1. 标识符查找容错
- **多种格式支持**：标准格式、方括号、模糊匹配、纯文本
- **优先级机制**：从精确到模糊依次尝试
- **异常处理**：单个模式失败不影响其他模式

### 2. 内容提取容错
- **边界安全**：自动处理索引越界情况
- **空内容处理**：标识符未找到时返回空字符串而非异常
- **任何获取不出来不影响总结果**：符合需求规定

### 3. 整体流程容错
- **文档解析失败**：返回错误信息而非崩溃
- **部分标识符缺失**：不影响其他部分的正常提取
- **详细日志记录**：便于问题定位和调试

---

## 🔍 匹配示例

### 成功匹配的标识符格式：
- `【变更操作概述】` ✅
- `[变更操作概述]` ✅  
- `【1、变更操作概述】` ✅
- `【变更操作概述说明】` ✅
- `变更操作概述` ✅

### 实际分割结果：
```
第一部分：开头到变更操作概述
├─ 内容：文档标题、背景信息等
├─ 状态：始终为找到（因为是从开头开始）

第二部分：变更操作概述  
├─ 内容：【变更操作概述】标识符及其后续内容
├─ 状态：取决于是否找到"变更操作概述"标识符

第三部分：变更操作准备
├─ 内容：【变更操作准备】标识符及其后续内容  
├─ 状态：取决于是否找到"变更操作准备"标识符

第四部分：操作实施步骤
├─ 内容：【操作实施步骤】标识符及其后续内容
├─ 状态：取决于是否找到"操作实施步骤"标识符
```

---

## 📝 使用说明

### 代码调用示例：

```csharp
// 直接调用分割方法
var result = aiServices.SplitDocumentIntoSections("D:\\test.docx");
if (result.Success)
{
    foreach (var section in result.Sections)
    {
        Console.WriteLine($"{section.SectionName}: {section.Content.Length} 字符");
    }
}

// 测试方法调用
var testResult = await aiServices.TestDocumentSplittingAsync();
Console.WriteLine(testResult);
```

### API接口调用：

```
GET /api/qmov2/AI/TestDocumentSplitting
GET /api/qmov2/AI/TestDocumentSplitting?testFilePath=D:\test.docx
```

---

## 📋 总结

本分割逻辑采用**简洁而稳健**的设计：
- **简单易维护**：仅3个核心方法，逻辑清晰
- **容错性强**：多层匹配策略，异常处理完善
- **性能优良**：直接文本操作，避免复杂解析
- **功能完整**：满足四部分分割需求，支持多种文档格式

代码实现与本文档描述完全一致，确保了文档的准确性和可维护性。
