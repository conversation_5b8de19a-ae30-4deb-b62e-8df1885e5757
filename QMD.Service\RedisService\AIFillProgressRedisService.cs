using Common.DAL.RedisStore;
using Newtonsoft.Json;
using QMD.Env;
using QMD.Service.QMOV2Service.AI;
using System;

namespace QMD.Service.RedisService
{
    /// <summary>
    /// AI填报进度Redis服务
    /// </summary>
    public class AIFillProgressRedisService
    {
        #region 【 Data 】
        private static readonly string STORE_NAME = ConfigEnvValues.IsTestSite ? "QMOAIFillProgressStore_TEST" : "QMOAIFillProgressStore";
        private static string _address = ConfigEnvValues.RedisConnection;
        private static int _dbIndex = ConfigEnvValues.RedisDbIndex;
        
        private static readonly JsonSerializerSettings SerializerSettings = new JsonSerializerSettings
        {
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            Formatting = Formatting.None,
            DateFormatHandling = DateFormatHandling.IsoDateFormat
        };
        #endregion

        #region 【 Progress Management 】
        
        /// <summary>
        /// 保存AI填报进度
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="progress">进度信息</param>
        public static void SaveProgress(string taskId, AIFillProgressDto progress)
        {
            try
            {
                var key = $"progress:{taskId}";
                var data = JsonConvert.SerializeObject(progress, SerializerSettings);
                CommonHashStore.Cache(_address, _dbIndex, STORE_NAME, key, data);
                
                // 设置30分钟过期时间
                SetExpireTime(key);
            }
            catch (Exception ex)
            {
                // 记录错误但不影响主流程
                Console.WriteLine($"保存AI填报进度失败: {taskId}, 错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 获取AI填报进度
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>进度信息</returns>
        public static AIFillProgressDto GetProgress(string taskId)
        {
            try
            {
                var key = $"progress:{taskId}";
                var data = CommonHashStore.Get(_address, _dbIndex, STORE_NAME, key);
                
                if (string.IsNullOrEmpty(data))
                    return null;
                    
                return JsonConvert.DeserializeObject<AIFillProgressDto>(data, SerializerSettings);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取AI填报进度失败: {taskId}, 错误: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 删除AI填报进度
        /// </summary>
        /// <param name="taskId">任务ID</param>
        public static void RemoveProgress(string taskId)
        {
            try
            {
                var key = $"progress:{taskId}";
                CommonHashStore.Remove(_address, _dbIndex, STORE_NAME, key);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除AI填报进度失败: {taskId}, 错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 检查进度是否存在
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>是否存在</returns>
        public static bool ProgressExists(string taskId)
        {
            try
            {
                var key = $"progress:{taskId}";
                return CommonHashStore.Exist(_address, _dbIndex, STORE_NAME, key);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查AI填报进度存在性失败: {taskId}, 错误: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 设置进度过期时间（30分钟）
        /// </summary>
        /// <param name="key">键名</param>
        private static void SetExpireTime(string key)
        {
            try
            {
                using (var rs = new RedisStore(_address, _dbIndex))
                {
                    var hashKey = $"{STORE_NAME}";
                    // 为整个Hash设置过期时间，如果不存在则创建
                    rs.RedisCache.KeyExpire(hashKey, TimeSpan.FromMinutes(30));
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置AI填报进度过期时间失败: {key}, 错误: {ex.Message}");
            }
        }
        
        #endregion
    }
}
