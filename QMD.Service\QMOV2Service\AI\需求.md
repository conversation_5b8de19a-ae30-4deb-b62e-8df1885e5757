AIServices.cs的逻辑需要优化改进

前提：目前的逻辑完全正确，不得改变原有的业务逻辑。

你需要做的是两点：

1.AIFillFormAsync这个接口时间有点慢，需要优化。



你目前的测试方法已经将文档正确分成了四个部分
你现在需要优化AIFillFormAsync方法

假定docx文档已经被分成了四部分，一，二，三，四部分。

AIFillFormAsync 方法的第一阶段：文档内容不用整个，用分割的第一部分
AIFillFormAsync 方法的第二阶段：保持不变
AIFillFormAsync 方法的第三阶段这样优化：
    现在你要分四个线程，每个线程去调用不同的提示词

    提示词分为两类，工单是升级类型或其他类型

    当工单类型是升级时，参照upgrade_template.md内容：
    docx文档的第一部分解析的字段是：6，8，9
    docx文档的第二部分解析的字段是：1，2，3，7，10，11，12，13，19
    docx文档的第三部分解析的字段是：14，15，16，17，18，20
    docx文档的第四部分解析的字段是：4，5，

    当工单类型是非升级时，参照common_template.md：
    docx文档的第一部分解析的字段是：6，7
    docx文档的第二部分解析的字段是：1，2，3，8，9，10，16
    docx文档的第三部分解析的字段是：11，12，13，14，15，17
    docx文档的第四部分解析的字段是：4，5

    你需要重新创建新的提示词，确保每个文档块用不同的提示，全部完成后需要组装成对应的格式（不变）

注意：你需要在关键的节点写入日志信息，因为我后续会做进度查询
      严格参考upgrade_template.md，common_template.md的提示词，只做切分，不得改变






   