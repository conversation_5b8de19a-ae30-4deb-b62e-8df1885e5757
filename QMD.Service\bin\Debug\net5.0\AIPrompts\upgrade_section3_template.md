请根据以下文档内容，提取QMO工单报备所需的详细信息。

基础信息：
- 工单类型：升级
- 处理部分：第三部分（变更操作准备）

请从文档中提取以下信息：
14. 默认客户信息
  **注意**：根据文档中的【变更操作准备】-【人员安排】-【客户变更小组名单】部分的表格进行提取（不要从客户审核的表格中查找）：
    - 查找包含"姓名"、"电话"、"职责"等列的客户人员表格
    - 选取表格中的第一行客户数据（排除表头）
    - 提取该客户的姓名、电话、邮箱信息
    - 如果表格中没有邮箱列或邮箱为空，则邮箱字段设为空字符串
    - 如果电话（或者相近的字段，比如手机号，联系方式等）为空，则电话字段设为空字符串
    - 绝对不能编造任何数据
    - 返回格式：{"email":"客户邮箱或空","nickName":"客户姓名","phoneNumber":"客户电话或空"}
15. 默认客户邮箱
  **注意**：从第14项提取的默认客户信息中获取邮箱字段，如果没有邮箱信息则返回空字符串（结果和14项中的email结果保持一致）。

16. 默认客户电话
  **注意**：从第14项提取的默认客户信息中获取电话字段，如果没有电话信息则返回空字符串（结果和14项中的phoneNumber结果保持一致）。
17. 实施人信息（支持多个）
  据文档中的操作对象，烽火现场实施小组名单中表格进行提取，当职责为实施负责人，负责本次操作实施
    **姓名**：
      - 即实施负责人姓名。
    **电话**：
      - 即实施负责人电话。
18. 审核人信息（支持多个）
  据文档中的操作对象，烽火现场实施小组名单中表格进行提取，当职责为负责审核实施操作步骤过程
    **姓名**：
      - 即审核人姓名。
    **电话**：
      - 即审核人电话。
20. 所有客户信息
**注意**：根据文档中的 人员安排-客户变更小组名单 下方的第一个表， 这个表中所有的客户信息（客户姓名，客户邮箱，客户电话）。
    - 是一个数组对象，格式如下[{"invNickName":"","invPhone":,"invEmail":""}]

文档内容：
{docContent}

### 所有提取的内容不能自己编造，只能从文档中提取，宁缺勿错的原则！

### 请严格按照以下JSON格式返回提取结果,JSON里的举例不能作为结果返回，如果没有置为空(注意，格式里的具体内容和文档无关，请你只提取文档里的内容)：
{
  "customName": {"email":"","nickName":"xiaoshaoqian","phoneNumber":"12324567131"},  // 14.默认客户信息（数据不能捏造和使用示例数据）
  "customEmail": "<EMAIL>",  // 15.客户邮箱（结果和14项中的email字段的结果完全保持一致）
  "customPhone": "12324567131",  // 16.客户电话（结果和14项中的phoneNumber字段的结果完全保持一致）

   "taskUsers": [{"nickName":"朱浩","userPhone":"13787031546"}],  // 17.实施人信息（绝对不能使用示例数据或者捏造数据）

   "checkUsers": [{"nickName":"肖绍谦","userPhone":"18627827716"}],  // 18.审核人（绝对不能使用示例数据或者捏造数据）
   "customUsers":[{"invNickName":"肖绍谦","invPhone":12324567131,"invEmail":"<EMAIL>"}] //20.所有客户信息（绝对不能使用示例数据或者捏造数据）
}
