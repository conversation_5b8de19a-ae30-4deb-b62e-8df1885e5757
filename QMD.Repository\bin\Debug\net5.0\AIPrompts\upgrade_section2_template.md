请根据以下文档内容，提取QMO工单报备所需的详细信息。

基础信息：
- 工单类型：升级
- 处理部分：第二部分（变更操作概述）

请从文档中提取以下信息：
1. 风险级别
  必须严格从以下标准列表中匹配：{riskLevels}
2. 是否重保
  文档中无相关字段
    - bool类型
    - 直接赋值false
3. 产品线附加信息
7. 升级原因
  文档中无相关字段
    - 直接赋值空的数组
10. 升级模式
  根据文档中的版本信息表格，判断升级模式的逻辑如下：
  **设备升级**：
    - 条件：在"版本信息-设备版本信息"中，存在目标版本的表头且有对应值。
    - 同时，在"版本信息-网管版本"中，目标版本表头不存在，或者存在但值为空
  **网管升级**：
    - 条件：在"版本信息-设备版本信息"中，目标版本表头不存在，或者存在但值为空。
    - 同时，在"版本信息-网管版本"中，存在目标版本的表头且有对应值。
  **设备网管双升级**：
    - 条件：在"版本信息-设备版本信息"中，存在目标版本的表头且有对应值。
    - 同时，在"版本信息-网管版本"中，存在目标版本的表头且有对应值.
11. 网管信息集合
  据文档中的版本信息，网管版本信息表格提取，当升级模式不同时，提取的内容有增减
  **设备升级**：
    - 仅提取当前版本下的网管系列、网管版本和网管补丁。
  **网管升级**和**设备网管双升级**：
    - 除了提取当前版本的网管系列、网管版本和网管补丁外，还需提取目标版本的网管系列、网管版本和网管补丁。
    - 网管系列应包含网管系统的名称，主版本即为网管版本，补丁信息为网管补丁。
  **注意**：在提取时需关注相近词汇的识别，以确保信息的准确性。
12. 设备信息集合
  据文档中的版本信息，设备版本信息表格提取，当升级模式不同时，提取的内容有增减
  **设备升级**和**设备网管双升级**：
    - 设备型号
    - 板卡名称
    - 板卡物料号（注意：硬件版本即为板卡物料号）
    - 当前软件版本
    - 当前软件编译日期（格式为YYYY-MM-DD）
    - 目标软件版本
    - 目标软件编译日期（格式为YYYY-MM-DD）
  **网管升级**：
    - 设备型号
    - 板卡名称
    - 板卡物料号（注意：硬件版本即为板卡物料号）
    - 当前软件版本
    - 当前软件编译日期（格式为YYYY-MM-DD）
    - 目标软件版本不适用。
    - 目标软件编译日期不适用。
  **注意**：有些表格中，软件版本和编译日期可能写在一起，需要进行分离。
13. 变更工作量集合
  据文档中的操作对象，进行提取
    **实施对象**：
      - 即表格中的设备类型。
    **本次实施数量**：
      - 即表格中的数量，数字格式。
    **注意**：在提取时需关注相近词汇的识别，以确保信息的准确性。
19. 工单内容
  根据文档中的内容，总结该文档的目的和原因

文档内容：
{docContent}

### 所有提取的内容不能自己编造，只能从文档中提取，宁缺勿错的原则！

### 请严格按照以下JSON格式返回提取结果,JSON里的举例不能作为结果返回，如果没有置为空(注意，格式里的具体内容和文档无关，请你只提取文档里的内容)：
{
  "riskLevel": "一级",  // 1.风险级别
  "isReinstated": false,  // 2.是否重保
  "lineRemark": "产品附加信息",  // 3.产品线附加信息
  upgradeReason:["基线版本"], //7.升级原因
  upgradeMode:2, // 10.升级模式
  "involveNmpinfos": [{"id":null,"orderKind":3,"nmpSeries":"网管系列（当前版本）","nmpVersion":"网管版本（当前版本）","nmpPatches":"网管补丁（当前版本）","tarNmpSeries":"网管系列（目标版本）","tarNmpVersion":"网管版本（目标版本）","tarNmpPatch":"网管补丁（目标版本）"}],  // 11.网管信息集合（绝对不能使用示例数据或者捏造数据）
  "involveDevices": [{"id":null,"orderKind":3,"invDeviceType":"对象-设备型号","invBoardName":"对象-板卡名称","invHardVersion":"对象-板卡物料号","invSoftVersion":"软件版本-当前","compileTime":"软件编译日期-当前版本","tarInvSoftVersion":"软件版本-目标版本","tarCompileTime":"软件编译日期-目标版本"}],  // 12.设备信息集合（绝对不能使用示例数据或者捏造数据）
  "involveProducts": [{"id":null,"orderKind":3,"objectCount":12,"objectName":"实施对象"}],  // 13.变更工作量集合（绝对不能使用示例数据或者捏造数据）
  "taskContent": "",  // 19.任务内容
}
