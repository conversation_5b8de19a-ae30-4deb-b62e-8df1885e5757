<?xml version="1.0"?>
<doc>
    <assembly>
        <name>QMD.Web</name>
    </assembly>
    <members>
        <member name="T:QMD.Web.ActionFilters.CustomAuthorizeMiddleware">
            <summary>
            中间件
            </summary>
        </member>
        <member name="F:QMD.Web.ActionFilters.SignatureManager.DefaultTimeoutMinutes">
            <summary>
            默认五分钟
            </summary>
        </member>
        <member name="M:QMD.Web.ActionFilters.SignatureManager.VerifyExternalSign(System.String)">
            <summary>
            验证sign是否合法
            </summary>
            <param name="url">原始请求的路径，其实只需要包含?后面的QueryString部分</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.ActionFilters.SignatureManager.CreateSign(System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            创建Sign参数
            </summary>
            <param name="appSecret"></param>
            <param name="parameters"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.ActionFilters.SignatureManager.CheckTimestamp(System.Int64)">
            <summary>
            检查时间是否过期
            </summary>
            <param name="ticks"></param>
            <returns></returns>
        </member>
        <member name="F:QMD.Web.ActionFilters.SignatureMessages.NoApiId">
            <summary>
            API ID未注册
            </summary>
        </member>
        <member name="F:QMD.Web.ActionFilters.SignatureMessages.UrlNotValid">
            <summary>
            请确保url查询条件中包含timestamp，sign以及appid
            </summary>
        </member>
        <member name="F:QMD.Web.ActionFilters.SignatureMessages.SignNotMatch">
            <summary>
            验证失败
            </summary>
        </member>
        <member name="F:QMD.Web.ActionFilters.SignatureMessages.Timeout">
            <summary>
            timestamp过期
            </summary>
        </member>
        <member name="F:QMD.Web.ActionFilters.SignatureMessages.Success">
            <summary>
            验证成功
            </summary>
        </member>
        <member name="T:QMD.Web.Config.DateTimeConverter">
            <summary>
            时间转换器
            </summary>
        </member>
        <member name="T:QMD.Web.Controllers.ActionRecordController">
            <summary>
            审批历史记录相关接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.ActionRecordController.QueryLogRecordsList(System.String,QMD.Model.ActionRecord.LogRecordReqDto,Common.DAL.Methods.PageCriteria)">
            <summary>
            操作记录日志列表
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.ActionRecordController.ExportLogRecordReport(System.String,QMD.Model.ActionRecord.LogRecordReqDto)">
            <summary>
            导出操作记录日志
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.ActionRecordController.ExportActivityReport(System.String,QMD.Model.ActionRecord.LogRecordReqDto)">
            <summary>
            导出用户活跃度
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.ActionRecordController.GetActionType">
            <summary>
            获取操作类型
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.ActionRecordController.InitActionRecordProjectID">
            <summary>
            补全日志所属项目(projectid)
            </summary>
            <returns></returns>
        </member>
        <member name="T:QMD.Web.Controllers.ApprovalController">
            <summary>
            审批和查询审批数据的接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.ApprovalController.ApprovalFiles(System.Collections.Generic.List{QMD.Model.FileApprovalReqDto})">
            <summary>
            审批文件，并且创建审批记录
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.ApprovalController.ApprovalSteps(System.Collections.Generic.List{QMD.Model.StepApprovalReqDto})">
            <summary>
            审批工序，并且创建审批记录
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.ApprovalController.ForceRectify(QMD.Model.ForceRectifyInputDto)">
            <summary>
            整改派发
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.ApprovalController.QueryTaskItemGroupApprovalStatus(System.String,System.String,System.String)">
            <summary>
            查询分组下的工序和文件审批状态，没有就是待审核状态
            </summary>
            <param name="taskItemID">基础数据ID</param>
            <param name="groupID">分组ID</param> 
            <param name="triggerDay">周期触发时间</param>
        </member>
        <member name="M:QMD.Web.Controllers.ApprovalController.QueryTaskItemStepApprovalStatus(System.String,System.String,System.String)">
            <summary>
            查询工序的审批状态，没有就是待审核状态
            </summary>
            <param name="taskItemID">基础数据ID</param>
            <param name="stepID">工序ID</param> 
            <param name="triggerDay">周期触发时间</param>
        </member>
        <member name="M:QMD.Web.Controllers.ApprovalController.QueryTaskItemApprovalStatus(System.String,System.String,System.String)">
            <summary>
            查询某一个基础数据在特定任务类型下的所有审批状态，没有就是待审核状态
            </summary>
            <param name="taskItemID">基础数据ID</param>
            <param name="tplID">任务类型ID</param> 
            <param name="triggerDay">周期触发时间</param>
        </member>
        <member name="M:QMD.Web.Controllers.ApprovalController.QueryFileApprovalStatusByStepID(System.String,System.String,System.String)">
            <summary>
            查询某一个基础数据的某一个工序下的所有文件的审批状态，没有就是待审核状态
            </summary>
            <param name="taskItemID">基础数据ID</param>
            <param name="stepID">工序</param>
            <param name="triggerDay">周期触发时间</param> 
        </member>
        <member name="T:QMD.Web.Controllers.AppSolutionController">
            <summary>
            解决方案管理接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.AppSolutionController.Insert(QMD.Model.AppUserPositionDto)">
            <summary>
            新增
            </summary>
            <param name="solutionDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.AppSolutionController.Update(QMD.Model.AppUserPositionDto)">
            <summary>
            更新
            </summary>
            <param name="solutionDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.AppSolutionController.Delete(QMD.Model.AppUserPositionDto)">
            <summary>
            删除
            </summary>
            <param name="solutionDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.AppSolutionController.QueryList(QMD.Model.AppUserPositionReqDto,Common.DAL.Methods.PageCriteria)">
            <summary>
            查询列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.AppSolutionController.Export(QMD.Model.AppUserPositionReqDto)">
            <summary>
            导出数据
            </summary>
            <param name="solutionDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.AppSolutionController.Import(Microsoft.AspNetCore.Http.IFormCollection)">
            <summary>
            导入数据
            </summary>
            <param name="formInfo"></param>
            <returns></returns>
        </member>
        <member name="T:QMD.Web.Controllers.DingDing.DingDingAuthController">
            <summary>
            审批历史记录相关接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.DingDing.DingDingAuthController.GetNmospTokenByDingCode(System.String,System.String,System.Int32)">
            <summary>
            根据钉钉的Code换取Nmosp的Token
            </summary>
            <param name="dingCode"></param>
            <param name="corpId"></param>
            <param name="authType">授权类型：默认是0-获取的nmosptoken，1-自定义的token</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.DingDing.DingDingAuthController.GetDingAccessToken(System.String)">
            <summary>
            获取钉钉的access_token
            </summary>
            <param name="corpId"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.DingDing.DingDingAuthController.JsAuthorization(QMD.Model.DingDing.JsAuthorizationDto)">
            <summary>
            客户端js鉴权
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="T:QMD.Web.Controllers.ExportController">
             <summary>
            导出文件相关接口
             </summary>
        </member>
        <member name="M:QMD.Web.Controllers.ExportController.GetProjectReportList">
            <summary>
            获取项目报表的CDN下载地址列表，Key为日期，Value为CDN地址
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.ExportController.ExportImportTaskItemsSample">
            <summary>
            导出基础数据导入模板（Sample）文件
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.ExportController.ExportImportTaskItemsSampleItr">
            <summary>
            导出基础数据导入模板（Sample）文件（ITR版本）
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.ExportController.ExportTaskItemFilesByTplID(System.String,System.String,System.String)">
            <summary>
            导出站点在某一个任务类型下的全部文件成压缩包
            </summary>
            <param name="taskItemID">站点ID</param>
            <param name="tplID">任务类型ID</param>
            <param name="triggerDay">出发日期</param>
        </member>
        <member name="M:QMD.Web.Controllers.ExportController.ExportTaskItems(System.String,System.String)">
            <summary>
            导出基础数据
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.ExportController.ExportTaskItemsReadyApproval(System.String,System.Int32)">
            <summary>
            审批页面导出基础数据，包含各个关联模块的进度，必填项未填的项目，是否需要审批等等
            </summary>
            <param name="projectID">项目ID</param> 
            <param name="level">审核级别，目前只支持一级审核查询</param>
        </member>
        <member name="M:QMD.Web.Controllers.ExportController.ExportTaskItemPreviewsOld(System.String,System.String,System.String,System.String,System.String,System.Nullable{System.Boolean},System.Nullable{System.Boolean})">
            <summary>
            导出基础数据预览信息，包含各个关联模块的进度，必填项未填的项目等等
            </summary> 
            <param name="projectID">项目ID</param>
            <param name="siteID">站点ID，对应数据库的Code字段</param>
            <param name="siteName">站点名称，对应数据库DisplayName字段</param>
            <param name="region">区域</param>
            <param name="contractor">分包商</param>
            <param name="hasImg">是否包含图片文件</param>
            <param name="hasNotApproved">是否包含被打回的</param>
        </member>
        <member name="M:QMD.Web.Controllers.ExportController.ExportTaskItemPreviews(QMD.Model.ReqTaskItemDto)">
            <summary>
            导出基础数据预览信息，包含各个关联模块的进度，必填项未填的项目等等
            </summary> 
            <param name="reqTaskItemDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.ExportController.ExportContractorUserTemplate">
            <summary>
            导出操作组用户导入模板
            </summary>
        </member>
        <member name="T:QMD.Web.Controllers.ExternalController">
            <summary>
            用户登录权限接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.ExternalController.ActiveQmdStep(QMD.Model.ExternalDto)">
            <summary>
            根据加密的RSA数据获取工单的上传百分比和审核百分比
            </summary>
            <param name="externalDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.ExternalController.GetTaskPercent(QMD.Model.ExternalDto)">
            <summary>
            获取ITR工单或者QA网络模板实施的百分比
            </summary>
            <param name="externalDto"></param>
            <returns></returns>
        </member>
        <member name="T:QMD.Web.Controllers.ImportController">
            <summary>
            导入相关接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.ImportController.ImportTaskItems(QMD.Model.ImportExport.ImportTaskItemReqDto)">
            <summary>
            导入基础数据
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.ImportController.ImportItrTaskItems(QMD.Model.ImportExport.ImportTaskItemReqDto)">
            <summary>
            导入ITR工单基础数据
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.ImportController.ImportContractorUser(QMD.Model.ImportExport.ImportContractorUserReqDto)">
            <summary>
            导入操作组用户数据
            </summary> 
        </member>
        <member name="T:QMD.Web.Controllers.LangController">
            <summary>
            多语言的相关接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.LangController.GetRegionLangs">
            <summary>
            获取区域相关的所有多语言数据
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.LangController.GetServiceLineLangs">
            <summary>
            获取服务线相关的所有多语言数据
            </summary> 
        </member>
        <member name="T:QMD.Web.Controllers.MyController">
            <summary>
            我的待办相关接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.MyController.QueryMyTasks(QMD.Model.My.MyTaskType,System.Nullable{System.Int32},System.Boolean,System.String,Common.DAL.Methods.PageCriteria)">
            <summary>
            查询我的待办
            </summary>
            <param name="taskType">我的待办查询类型，0：预览，1是待审核</param>
            <param name="level">TaskType为1的时候，表示需要传递审批级别，只支持1和2</param>
            <param name="isAll">是否查询全部，否对于预览就是查询存在被打回的，对于审批就是需要自己审核的</param>
            <param name="fuzzy">模糊查询字符串，目前是模糊查询Code和DisplayName</param>
            <param name="pageCriteria">分页条件</param>
        </member>
        <member name="M:QMD.Web.Controllers.MyController.QueryMyProjectTasks(QMD.Model.MyAppRequestDto)">
            <summary>
            查询项目列表和我的待办
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="T:QMD.Web.Controllers.ObjTypeLevelController">
            <summary>
            对象级别管理接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.ObjTypeLevelController.Insert(QMD.Model.ObjTypeLevelDto)">
            <summary>
            新增
            </summary>
            <param name="reqDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.ObjTypeLevelController.Update(QMD.Model.ObjTypeLevelDto)">
            <summary>
            更新
            </summary>
            <param name="reqDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.ObjTypeLevelController.Delete(QMD.Model.ObjTypeLevelDto)">
            <summary>
            删除
            </summary>
            <param name="reqDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.ObjTypeLevelController.QueryList(QMD.Model.ObjTypeLevelReqDto,Common.DAL.Methods.PageCriteria)">
            <summary>
            查询列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.ObjTypeLevelController.Export(QMD.Model.ObjTypeLevelReqDto)">
            <summary>
            导出数据
            </summary>
            <param name="reqDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.ObjTypeLevelController.Import(Microsoft.AspNetCore.Http.IFormCollection)">
            <summary>
            导入数据
            </summary>
            <param name="formInfo"></param>
            <returns></returns>
        </member>
        <member name="T:QMD.Web.Controllers.PageIndexController">
            <summary>
            工单首页汇总接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.PageIndexController.GetPercentByStatus(QMD.Model.ReqPageIndexDto)">
            <summary>
            获取各种状态下的工单的数量以及百分比/QMD工单/ITR工单执行状态分布
            </summary> 
            <param name="reqDto"></param> 
        </member>
        <member name="M:QMD.Web.Controllers.PageIndexController.GetSpeOrderCountByMonth(QMD.Model.ReqPageIndexDto)">
            <summary>
            QMD工单/ITR工单每月新增数量
            </summary> 
            <param name="reqDto"></param> 
        </member>
        <member name="M:QMD.Web.Controllers.PageIndexController.GetStatusCountBySpe(QMD.Model.ReqPageIndexDto)">
            <summary>
            QMD工单/ITR工单个专业执行关闭状态情况
            </summary> 
            <param name="reqDto"></param> 
        </member>
        <member name="M:QMD.Web.Controllers.PageIndexController.GetCacheSettings">
            <summary>
            获取缓存中的周期设置信息
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.PageIndexController.GetCacheData(QMD.Model.RedisDto.RdsRequestDto)">
            <summary>
            获取缓存数据
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.PageIndexController.GetFullTables(QMD.Model.RedisDto.RdsRequestDto)">
            <summary>
            获取全表（月度全表，季度全表，年度全表）
            </summary>
            <param name="requestDto">仅仅需要传入TbType参数（1月度，2季度，3年度）</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.PageIndexController.GetMonthTrend(QMD.Model.RedisDto.RdsRequestDto)">
            <summary>
            获取月度趋势表
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.PageIndexController.GetMonthAtomsConditions">
            <summary>
            获取qa看板条件
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.PageIndexController.GetMonthAtomsTable(QMD.Model.QA.QaReportRequestDto)">
            <summary>
            获取qa看板数据
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.PageIndexController.ExportMonthAtomsTable(QMD.Model.QA.QaReportRequestDto)">
            <summary>
            导出qa看板数据
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.PageIndexController.ReportDimensionMonthAtoms(QMD.Model.QA.QaReportRequestDto)">
            <summary>
            片区维度、专业维度报表数据
            </summary>
            <param name="requestDto">必要参数： DimensionType，TriggerDay或者StartTime</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.PageIndexController.ReportDimensionMonthCondition">
            <summary>
            片区维度、专业维度报表条件
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.PageIndexController.ExportReportDimensionMonthAtoms(QMD.Model.QA.QaReportRequestDto)">
            <summary>
            片区维度、专业维度报表数据导出
            </summary>
            <param name="requestDto">必要参数： DimensionType，TriggerDay或者StartTime</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.PageIndexController.ExtItrBoardCondition">
            <summary>
            ITR看板条件
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.PageIndexController.ExtItrBoardOrder(QMD.Model.ExtItrBoardRequestDto)">
            <summary>
            获取ITR工单看板数据
            </summary> 
            <param name="reqDto"></param> 
        </member>
        <member name="M:QMD.Web.Controllers.PageIndexController.ExportExtItrBoardOrder(QMD.Model.ExtItrBoardRequestDto)">
            <summary>
            导出ITR工单看板数据
            </summary> 
            <param name="reqDto"></param> 
        </member>
        <member name="M:QMD.Web.Controllers.PageIndexController.testTransValue(QMD.Model.ExtItrBoardRequestDto)">
            <summary>
            导出ITR工单看板数据
            </summary> 
            <param name="reqDto"></param> 
        </member>
        <member name="M:QMD.Web.Controllers.PageIndexController.GetHelperDoc">
            <summary>
            获取qa看板数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:QMD.Web.Controllers.PermissionController">
            <summary>
            站点级授权相关接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.QueryNmospUsers(System.String)">
            <summary>
            模糊查询NMOSP用户系统中的用户数据
            </summary>
            <param name="name">模糊查询用户名</param> 
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.GetSamplingRate(System.String)">
            <summary>
            获取抽检比率，如果为空则自动往数据库添加默认比率
            </summary>
            <param name="projectID">项目ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.SetSamplingRate(QMD.Model.Permission.SetSamplingRateDto)">
            <summary>
            设置某个项目的抽检比率
            </summary>
            <param name="req">项目ID,rate抽检比率</param> 
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.StartSampling(System.String)">
            <summary>
            开始抽检
            </summary>
            <param name="projectID">项目ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.AssignContractors(QMD.Model.Permission.AssignContractorDto)">
            <summary>
            为站点的某个任务类型授予一个承包商
            </summary>
            <param name="req">站点ID列表、模板ID和承包商列表,Rewrite表示是否替换，如果为true会把之前所有和承包商的关系删除再重新添加</param> 
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.RemoveAllContractors(QMD.Model.Permission.RemoveAllContractorDto)">
            <summary>
            清空某些站点的某一个任务的所有承包商
            </summary>
            <param name="req">站点ID列表、模板ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.RemoveContractorRel(System.String,System.String,System.String)">
            <summary>
            移除站点的某个任务类型的承包商权限
            </summary>
            <param name="taskItemID">站点ID</param>
            <param name="tplID">任务类型ID</param>
            <param name="contractorID">承包商ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.RemoveContractor(System.String)">
            <summary>
            删除一个承包商和这个承包商所有的关联
            </summary>
            <param name="contractorID">承包商ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.AddOrUpdateContractor(QMD.Model.Permission.ContractorDto)">
            <summary>
            更新或者新增一个承包商，包含NMOSP用户列表数据
            </summary>
            <param name="req">必填DisplayName，ProjectID</param>
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.ChangeContractor(System.String,System.String,System.String,System.String)">
            <summary>
            变更站点的某个任务类型的承包商权限
            </summary>
            <param name="taskItemID">站点ID</param>
            <param name="tplID">任务类型ID</param>
            <param name="oldContractorID">原先的承包商ID</param>
            <param name="newContractorID">想要变更为的承包商ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.GetContractors(System.String,System.String)">
            <summary>
            获取所有承包商的列表数据
            </summary>
            <param name="projectID">项目ID</param>
            <param name="name">模糊搜索承包商名称和Code</param> 
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.GetContractorsByTaskItemTpl(System.String,System.String)">
            <summary>
            获取指定站点模板承包商数据
            </summary>
            <param name="taskItemID">站点ID</param>
            <param name="tplID">任务类型ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.QueryApprovers(System.String,System.String,System.String,System.Nullable{System.Int32},Common.DAL.Methods.PageCriteria)">
            <summary>
            分页查询审批人员信息，同一个用户的不同审批级别当做两条记录存储，参考UCD设计
            </summary>
            <param name="projectID">项目ID</param>
            <param name="taskItemId">任务ID</param>
            <param name="name">模糊搜索审批人员的名称</param>
            <param name="level">审批级别,1-2</param>
            <param name="pageCriteria">分页查询条件</param> 
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.GetApprovers(System.String,System.String,System.Int32)">
            <summary>
            查询审批人员列表
            </summary>
            <param name="projectID">项目ID</param>
            <param name="name">模糊搜索审批人员的名称</param>
            <param name="level">审批级别，1-2</param>
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.SetApprovers(QMD.Model.Permission.SetApproversDto)">
            <summary>
            设置站点的某几个任务类型的审批人员
            </summary>
            <param name="req">审批级别只允许1-2</param>
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.GetTaskItemApprovers(System.String,System.String,System.Int32)">
            <summary>
            查询一个站点的某一个任务类型的审批人员
            </summary>
            <param name="taskItemID">站点ID</param>
            <param name="tplID">任务类型ID</param>
            <param name="level">可以指定1-2，-1的时候返回所有</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.QueryContractors(System.String,System.String,System.String,Common.DAL.Methods.PageCriteria)">
            <summary>
            分页查询承包商信息，包含每个承包商下的用户姓名和邮箱
            </summary>
            <param name="projectID">项目ID</param>
            <param name="contractorID">承包商ID</param>
            <param name="userName">模糊搜索用户姓名或者邮箱</param>
            <param name="pageCriteria">分页查询条件</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.ContractorAddUser(QMD.Model.Permission.ContractorUserReqDto)">
            <summary>
            为某一个承包商添加用户
            </summary>
            <param name="req">承包商ID，用户姓名和用户邮箱</param> 
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.ContractorRemoveUser(QMD.Model.Permission.ContractorUserReqDto)">
            <summary>
            为某一个承包商添加用户
            </summary>
            <param name="req">承包商ID，用户邮箱</param> 
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.AddApprover(QMD.Model.Permission.ApproverDto)">
            <summary>
            添加审批人员
            </summary>
            <param name="req">审批人员信息，从NMOSP平台中获取，Level必须为1-2之间</param> 
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.BatchSetApprover(QMD.Model.Permission.BatchSetApproverDto)">
            <summary>
            批量授权
            </summary>
            <param name="req">用户列表和需要授权的审批级别列表，审批级别只允许1-2</param> 
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.DeleteApprover(System.String)">
            <summary>
            根据ID删除一个用户审批授权
            </summary>
            <param name="approverID">审批授权的ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.DeleteApprovers(System.Collections.Generic.List{System.String})">
            <summary>
            根据ID删除一个用户审批授权
            </summary>
            <param name="approverIDs">审批授权的ID列表</param> 
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.QueryPermissionTable(System.String,System.String,System.String,System.String,System.String,System.String,System.Collections.Generic.List{System.String},System.String,System.String,Common.DAL.Methods.PageCriteria)">
            <summary>
            分页查询承包商的授权矩阵
            </summary>
            <param name="projectID">项目ID，不可为空</param>
            <param name="siteID">模糊查询站点Code</param>
            <param name="siteName">模糊查询站点名称</param>
            <param name="region">站点区域region</param>
            <param name="contractorName">模糊查询承包商名称</param>
            <param name="contractorID">精确查询承包商ID,"-1"为“待定”的，即没有承包商绑定关系</param>
            <param name="tplIDs">模板的ID</param>
            <param name="remark">站点备注</param>
            <param name="siteContractor">站点分包商</param>
            <param name="pageCriteria">分页查询条件</param> 
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.QueryProjectPermission(System.String)">
            <summary>
            查询项目权限
            </summary>
            <param name="projectID"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.PermissionController.SaveProjectPermission(QMD.Model.Permission.ProjectPermissionReqDto)">
            <summary>
            保存项目权限
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="T:QMD.Web.Controllers.ProjectController">
            <summary>
            项目的相关接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.ProjectController.GetAll(System.String)">
            <summary>
            获取所有项目
            </summary> 
            <param name="culture">多语言编码</param> 
        </member>
        <member name="M:QMD.Web.Controllers.ProjectController.QueryByProjectID(System.String,System.Boolean,System.String)">
            <summary>
            根据项目ID获取项目
            </summary>
            <param name="projectID"></param>
            <param name="detailFlag"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.ProjectController.QueryByServiceLine(System.String,System.String,System.String)">
            <summary>
            根据服务线ID查询项目
            </summary>
            <param name="serviceLineID">服务线ID</param> 
            <param name="culture">多语言编码</param> 
            <param name="name">项目名称</param>
        </member>
        <member name="M:QMD.Web.Controllers.ProjectController.GetProjectTypes(System.String)">
            <summary>
            获取项目类型列表
            </summary>
            <param name="culture">多语言编码</param> 
        </member>
        <member name="M:QMD.Web.Controllers.ProjectController.QueryByServiceLineAndRegion(System.String,System.String,System.String,System.String)">
            <summary>
            根据服务线ID和区域编码查询项目
            </summary>
            <param name="serviceLineID">服务线ID</param> 
            <param name="regionCode">区域编码</param>
            <param name="culture">多语言编码</param> 
            <param name="name">项目名称</param>
        </member>
        <member name="M:QMD.Web.Controllers.ProjectController.QueryByServiceLineAndRegionForMobile(System.String,System.String)">
            <summary>
            根据服务线ID和区域编码查询项目
            </summary>
            <param name="culture">多语言编码</param> 
            <param name="name">项目名称</param>
        </member>
        <member name="M:QMD.Web.Controllers.ProjectController.QueryByServiceLineAndRegion(System.String,System.String,System.String,Common.DAL.Methods.PageCriteria,System.Boolean,System.String)">
            <summary>
            根据服务线ID和区域编码查询项目
            </summary>
            <param name="serviceLineID">服务线ID</param> 
            <param name="regionCode">区域编码</param>
            <param name="pageCriteria">分页查询条件</param>
            <param name="name">模糊查询Code和DIsplayName</param>
            <param name="culture">多语言编码</param> 
            <param name="hasAssign">是否拥有权限分配</param> 
        </member>
        <member name="M:QMD.Web.Controllers.ProjectController.Insert(QMD.Model.ProjectDto)">
            <summary>
            新增项目
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.ProjectController.Update(QMD.Model.ProjectDto)">
            <summary>
            更新项目
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.ProjectController.Delete(System.String)">
            <summary>
            删除项目
            </summary> 
        </member>
        <member name="T:QMD.Web.Controllers.Provider.ExtOrderController">
            <summary>
            查询ITR实施工单和QA工单接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.Provider.ExtOrderController.ExtGetItrOrderCondition(QMD.Model.ExtItrOrderRequestDto)">
            <summary>
            ITR实施工单：获取查询条件
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.Provider.ExtOrderController.ExtGetItrOrder(QMD.Model.ExtItrOrderRequestDto)">
            <summary>
            获取ITR工单记录
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.Provider.ExtOrderController.ExtGetItrOrderDetail(QMD.Model.ExtItrOrderRequestDto)">
            <summary>
            获取ITR工单详情
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.Provider.ExtOrderController.ExportItrOrder(QMD.Model.ExtItrOrderRequestDto)">
            <summary>
            导出ITR工单记录
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.Provider.ExtOrderController.ExtGetQAOrderCondition(QMD.Model.ExtItrOrderRequestDto)">
            <summary>
            QA工单：获取查询条件
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.Provider.ExtOrderController.ExtGetQAOrder(QMD.Model.ExtItrOrderRequestDto)">
            <summary>
            获取QA工单列表
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.Provider.ExtOrderController.ExtGetQAOrderDetail(QMD.Model.ExtItrOrderRequestDto)">
            <summary>
            获取QA工单详情
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.Provider.ExtOrderController.ExportQAOrder(QMD.Model.ExtItrOrderRequestDto)">
            <summary>
            导出QA实施工单记录
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.Provider.ExtOrderController.ExportQAOrderReportDapper(QMD.Model.ExtItrOrderRequestDto)">
            <summary>
            导出QA实施工单统计记录
            </summary> 
        </member>
        <member name="T:QMD.Web.Controllers.Provider.OpenDataController">
            <summary>
            提供给第三方数据接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.Provider.OpenDataController.GetIndentityToken(QMD.Model.emt.ForThirdSystem.IdentityTokenDto)">
            <summary>
            提供给第三方（shr，资管系统[负责人：卢茉莉，开发责任人：万刘辉]）的实施任务数据
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.Provider.OpenDataController.GetExecuteTasksForShr(QMD.Model.emt.ForThirdSystem.ExecuteTaskForShrReqDto)">
            <summary>
            提供给第三方（shr，资管系统[负责人：卢茉莉，开发责任人：万刘辉]）的实施任务数据
            </summary> 
        </member>
        <member name="T:QMD.Web.Controllers.Provider.ThirdSystemController">
            <summary>
            类似以前的SiteInfo基础表，任务的目标对象基础信息相关接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.Provider.ThirdSystemController.ReceiveQaDataToQMO(QMD.Model.QA.PushFromQaDto)">
            <summary>
            从QA接收数据,需要传入参数{ BigDataId->大数据ID,QaTaskId->本次巡检任务ID,QaTaskType->本次巡检任务类型（0：全网（全网元）巡检，1：部分（自定义）网元巡检） }
            QaTaskType目前取值为0（其他工具巡检）和2（PPIT巡检）
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.Provider.ThirdSystemController.ReceiveQaDataToQMOBatch(System.Collections.Generic.List{QMD.Model.QA.PushFromQaDto})">
            <summary>
            从QA接收数据,需要传入参数{ BigDataId->大数据ID,QaTaskId->本次巡检任务ID,QaTaskType->本次巡检任务类型（0：全网（全网元）巡检，1：部分（自定义）网元巡检） }
            QaTaskType目前取值为0（其他工具巡检）和2（PPIT巡检）
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.Provider.ThirdSystemController.GetDepartmentAtomsTable(QMD.Model.QA.QaReportForWhyDto)">
            <summary>
            获取技服中心统计数据接口
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.Provider.ThirdSystemController.GetNetproviderAtomsTable(QMD.Model.QA.QaReportForWhyDto)">
            <summary>
            获取网络统计数据接口
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.Provider.ThirdSystemController.GetTaskStepAtomsTable(QMD.Model.QA.QaReportForWhyDto)">
            <summary>
            获取巡检项统计数据接口
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.Provider.ThirdSystemController.getorderbymonth(QMD.Model.QA.QaReportForItrDto)">
            <summary>
            根据月份获取各个网络的重要工单问题数
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.Provider.ThirdSystemController.getorderdetailbymonth(QMD.Model.QA.QaReportForItrDto)">
            <summary>
            根据月份获取各个网络的重要工单问题详细信息
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.Provider.ThirdSystemController.getnetorderdetailbymonth(QMD.Model.QA.QaNetReportForItrDto)">
            <summary>
            根据月份获取各个网络的重要工单问题详细信息
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.Provider.ThirdSystemController.GetDisNetOrders(QMD.Model.emt.ForThirdSystem.DisThirdRequestDto)">
            <summary>
            获取派发子工单工单列表，提供给第三方
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.Provider.ThirdSystemController.GetReportEvents(QMD.Model.emt.ForThirdSystem.ReportThirdRequestDto)">
            <summary>
            获取报备工单列表，提供给第三方
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.Provider.ThirdSystemController.GetReportEventDetail(QMD.Model.emt.ForThirdSystem.ReportThirdDetRequestDto)">
            <summary>
            获取实施工单列表，提供给第三方
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="T:QMD.Web.Controllers.QaImportantObjectController">
            <summary>
            重点对象管理接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QaImportantObjectController.Insert(QMD.Model.QA.QaImportantObjectDto)">
            <summary>
            新增
            </summary>
            <param name="reqDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QaImportantObjectController.Update(QMD.Model.QA.QaImportantObjectDto)">
            <summary>
            更新
            </summary>
            <param name="reqDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QaImportantObjectController.Delete(QMD.Model.QA.QaImportantObjectDto)">
            <summary>
            删除
            </summary>
            <param name="reqDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QaImportantObjectController.QueryList(QMD.Model.QA.QaImportantObjectDto,Common.DAL.Methods.PageCriteria)">
            <summary>
            查询列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QaImportantObjectController.Export(QMD.Model.QA.QaImportantObjectDto)">
            <summary>
            导出数据
            </summary>
            <param name="reqDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QaImportantObjectController.Import(Microsoft.AspNetCore.Http.IFormCollection)">
            <summary>
            导入数据
            </summary>
            <param name="formInfo"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QaImportantObjectController.GetAllConfig">
            <summary>
            获取所有配置信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QaImportantObjectController.GetInspcetItemDeviceTypes(QMD.Model.QA.QaInspectConfigDto)">
            <summary>
            获取app下对应的巡检项的设备数据源
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QaImportantObjectController.SaveInspcetConfig(QMD.Model.QA.QaInspectConfigSaveDto)">
            <summary>
            保存巡检问题配置
            </summary>
            <returns></returns>
        </member>
        <member name="T:QMD.Web.Controllers.QMOV2.AIController">
            <summary>
            AI智能填报控制器
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.AIController.AIFillForm(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            AI智能填报QMO工单（同步接口，保持原有逻辑）
            </summary>
            <returns>AI解析结果</returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.AIController.StartParseDocxTask(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            启动AI智能解析docx文档任务（异步接口）
            </summary>
            <param name="file">要解析的docx文件</param>
            <returns>任务启动结果</returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.AIController.GetParseDocxProgress(System.String)">
            <summary>
            查询AI解析docx文档进度
            </summary>
            <param name="taskId">任务ID</param>
            <returns>进度信息</returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.AIController.GetParseDocxResult(System.String)">
            <summary>
            获取AI解析docx文档结果
            </summary>
            <param name="taskId">任务ID</param>
            <returns>解析结果</returns>
        </member>
        <member name="T:QMD.Web.Controllers.QMOV2.ConfigController">
            <summary>
            定时任务接口
            </summary>
        </member>
        <member name="T:QMD.Web.Controllers.QMOV2.DifyController">
            <summary>
            Dify AI控制器
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DifyController.WriteToKnowledgeBase(System.Collections.Generic.List{System.String})">
            <summary>
            批量写入知识库
            </summary>
            <param name="orderNoList">工单号列表</param>
            <returns>写入结果</returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DifyController.UploadFileToDify(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            上传文件到Dify
            </summary>
            <returns>上传结果</returns>
        </member>
        <member name="T:QMD.Web.Controllers.QMOV2.DispatchOrderController">
            <summary>
            维护任务接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetPersonDisOrdersCondition">
            <summary>
            获取派发工单查询条件,参数：无
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetDispatchOrderFormData(QMD.Model.emt.RequestDto.DispatchRequestDto)">
            <summary>
            获取派发工单表单填报初始化数据，参数：无
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.EmtDeviceModuleConfigs">
            <summary>
            下载设备导入模板
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.ImportDevices(QMD.Model.DispatchordersFormDataDto)">
            <summary>
            导入设备数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.DownloadDevices(QMD.Model.emt.RequestDto.DispatchRequestDto)">
            <summary>
            下载派发工单设备数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetDisDevices(QMD.Model.emt.RequestDto.DispatchRequestDto)">
            <summary>
            获取派发工单设备数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetDisNmpInfos(QMD.Model.emt.RequestDto.DispatchRequestDto)">
            <summary>
            获取派发工单网管数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.AddDispatchOrder(QMD.Model.DispatchordersFormDataDto)">
            <summary>
            新增派发任务，参数：全属性；ExtPropValue结构化属性使用字典的JSON字符串传入，Files上传的文件
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.UpdateDispatchOrder(QMD.Model.DispatchordersFormDataDto)">
            <summary>
            更新派发任务，全属性；；ExtPropValue结构化属性使用字典的JSON字符串传入，Files上传的文件；原来需要保留的附件按照输出的格式输入
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetFormDataNet(QMD.Model.DispatchordersDto)">
            <summary>
            表单中根据工单选择的条件获取网络数据，参数：NetType，Operator，Region
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetDisOrderNetPageList(QMD.Model.emt.RequestDto.DispatchRequestDto)">
            <summary>
            获取派发工单下网络分页列表，必传参数：派发工单号OrderNo，CPageIndex,CPageSize,是否待办IsDealt
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetPersonOnExecutingDisOrders">
            <summary>
            获取当前用户权限内正在执行的派发工单数据，参数：无
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetPersonDisOrders(QMD.Model.emt.RequestDto.DispatchRequestDto)">
            <summary>
            获取派发工单查询数据列表（参数:IsDealt是否待办，OutputLines产出线，ProductLines产品线，OrderKinds工单类别，OrderTypes工单类型，RiskLevels风险级别，Title工单标题，OrderNo工单编号，SortOrder排序字段，SearchContent模糊查询，PageIndex分页，PageSize页数，CPageIndex子表分页，CPageSize子表页数）
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetApprovers(QMD.Model.emt.RequestDto.DispatchRequestDto)">
            <summary>
            获取审核人列表，由用户指定，参数：OrderNo派发工单号(必传)
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetCurUserPermission(QMD.Model.emt.RequestDto.DispatchRequestDto)">
            <summary>
            获取当前用户权限信息，提交时提示的信息，点提交按钮前调用，参数：OrderNo派发工单号(必传)
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetDispatchOrder(QMD.Model.DispatchordersDto)">
            <summary>
            获取单个派发任务的详细信息，包含网络子工单信息,参数：ID主键ID或者OrderNo工单编号（必传）
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.SubmitDispatchOrder(QMD.Model.DispatchordersDto)">
            <summary>
            提交派发任务，参数：ID主键ID或者OrderNo工单号，Approver二线审核人信息（如果需要二线审核必传）；requestDto全数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.ValidDispatchOrder(QMD.Model.DisValidDto)">
            <summary>
            派发工单，一线接口人审批和网络选择的前置验证
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetCurrentUserInfo">
            <summary>
            获取当前用户的基本信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.ApprovalDispatchOrder(QMD.Model.DispatchordersDto)">
            <summary>
            审批派发任务，参数：ID主键ID（必传）,ApprovalStatus审批状态（1通过，2不通过，3切换审核人），OrderNo派发工单编号
            Approver二线审核人信息，Netproviderrels派发网络子工单集合，NetType网络类型，remark备注(审核)信息，Operator网络线；requestDto全数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.RevokeDispatchOrder(QMD.Model.DispatchordersDto)">
            <summary>
            撤销派发任务，参数：OrderNo派发工单编号（必传），只允许单选
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.DelayNetproviderRels(QMD.Model.emt.RequestDto.DispatchRequestDto)">
            <summary>
            延期申请列表，参数：OrderNo派发任务工单号，PageIndex页号，PageSize页数
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.DelayNetproviderApproval(QMD.Model.NetproviderrelDto)">
            <summary>
            延期申请审批，参数：ApprovalOrderNos需要审批的网络子工单号，DelayStatus延期审批状态（2不通过，3通过），DelayApprContent审批意见/内容
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.RecordDispatchOrder(QMD.Model.emt.RequestDto.DispatchRequestDto)">
            <summary>
            派发任务操作日志，参数：OrderNo派发任务工单号，PageIndex页号，PageSize页数
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.ImportPLanInfoDevices(QMD.Model.DispatchordersFormDataDto)">
            <summary>
            导入计划的设备数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.DownPLanInfoDevices(QMD.Model.NetproviderrelDto)">
            <summary>
            下载计划的设备数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.ImportProcessDevices(QMD.Model.DispatchordersFormDataDto)">
            <summary>
            导入派发子工单进度填报数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.DownProcessDevices(QMD.Model.NetproviderrelDto)">
            <summary>
            下载派发子工单进度填报模板数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.SetNetPlanInfo(QMD.Model.NetproviderrelDto)">
            <summary>
            填报派发任务网络计划任务信息，根据网络负责人或者接口人填报的计划时间判断是否需要延期
            参数：OrderNo网络子工单号，Files附件信息，FileBelongKeys附件归属（计划附件/延期附件与Files一一对应），其他全属性
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetNetPlanInfo(QMD.Model.NetproviderrelDto)">
            <summary>
            获取派发任务网络子工单信息,参数：OrderNo派发任务子工单号
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetNetProcess(QMD.Model.emt.RequestDto.DispatchRequestDto)">
            <summary>
            获取派发任务进度列表，参数：OrderNo派发任务工单号
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.ProcessDispatchOrder(QMD.Model.emt.RequestDto.DispatchRequestDto)">
            <summary>
            派发任务网络子工单进度填报，参数：OrderNo网络子工单号，ProcessDtos进度信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetTaskModule">
            <summary>
            实施计划表模板
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.NoNeedImpDispatchOrder(QMD.Model.NetproviderrelDto)">
            <summary>
            派发任务无需实施相关信息填报，参数：OrderNo网络子工单号，Files子工单附件，Remark备注信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.StartReportOrder(QMD.Model.emt.RequestDto.DispatchRequestDto)">
            <summary>
            发起报备，参数：OrderNo网络子工单号
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetExtProperties(QMD.Model.emt.RequestDto.DispatchRequestDto)">
            <summary>
            获取扩展属性，结构化数据，参数：OrderKinds工单类别（派发/变更）只需要传一个元素
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.ExportDispatchOrder(QMD.Model.emt.RequestDto.DispatchRequestDto)">
            <summary>
            导出派发工单
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetNetProductLine(QMD.Model.emt.RequestDto.ReportRequestDto)">
            <summary>
            新增派发工单表单内的数据源-产线信息（产出线，产品线，产品线LMT）,
            产品信息（产品型号，单盘型号，硬件版本，软件版本），网管信息（网管系列，网管版本，网管补丁）
            参数：NeedBoard是否需要单盘数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetWhyNetProviderByCondition(QMD.Model.emt.RequestDto.WhySearchNetFormReqDto)">
            <summary>
            智能推荐获取网络接口
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetDispatchOrderFlow(QMD.Model.emt.RequestDto.DispatchRequestDto)">
            <summary>
            获取某个工单的流程信息，参数：OderNo派发工单编号
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.DeleteDispatchOrder(QMD.Model.emt.RequestDto.DispatchRequestDto)">
            <summary>
            删除单个派发工单信息，参数：OderNo派发工单编号
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetRecNetProductLine(QMD.Model.emt.NetRecommandDto)">
            <summary>
            智能推荐-产线级联查询数据
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetRecNetSearchDevice(QMD.Model.emt.NetRecommandDto)">
            <summary>
            智能推荐-设备单盘级联查询数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetRecNetSearchNmp(QMD.Model.emt.NetRecommandDto)">
            <summary>
            智能推荐-网管级联查询数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetNetOperators">
            <summary>
            智能推荐-获取运营商数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.QueryTransferOutDispatchOrders(System.String,QMD.DAL.Table.emt.EnumInvolveUserType)">
            <summary>
            转出子工单列表查询，参数：disOrderNo工单编号，involveUserType涉及人员类型
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.TransferOutDispatchOrders(QMD.Model.emt.RequestDto.DispatchOrderTransferDto)">
            <summary>
            派发工单转出，参数：Code工单编号，OrderNo子工单号，InvolveUserType涉及人员类型，NewInvUserEmail新用户邮箱
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.QueryTransferInDispatchOrders">
            <summary>
            转入子工单列表查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.TransferInDispatchOrders(QMD.Model.emt.RequestDto.DispatchOrderNoAndUserTypesDto)">
            <summary>
            派发工单转入，参数：OrderNo子工单号，InvolveUserType涉及人员类型，OrgInvUserEmail原用户邮箱
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetSecFormData(QMD.Model.DispatchordersDto)">
            <summary>
            获取派发工单表单填报初始化数据，必传参数：NetType,IsNmpInfo
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.AddDispatchOrderNew(QMD.Model.DispatchordersFormDataDto)">
            <summary>
            新增派发任务，参数：全属性；ExtPropValue结构化属性使用字典的JSON字符串传入，Files上传的文件
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.UpdateDispatchOrderNew(QMD.Model.DispatchordersFormDataDto)">
            <summary>
            更新派发任务，全属性；；ExtPropValue结构化属性使用字典的JSON字符串传入，Files上传的文件；原来需要保留的附件按照输出的格式输入
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.SubmitDispatchOrderNew(QMD.Model.DispatchordersDto)">
            <summary>
            提交派发任务，参数：ID主键ID或者OrderNo工单号，Approver二线审核人信息（如果需要二线审核必传）；requestDto全数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.ApprovalDispatchOrderNew(QMD.Model.DispatchordersDto)">
            <summary>
            审批派发任务，参数：ID主键ID（必传）,ApprovalStatus审批状态（1通过，2不通过，3切换审核人），OrderNo派发工单编号
            Approver二线审核人信息，Netproviderrels派发网络子工单集合，NetType网络类型，remark备注(审核)信息，Operator网络线；requestDto全数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.GetDispatchOrderNew(QMD.Model.DispatchordersDto)">
            <summary>
            获取单个派发任务的详细信息，包含网络子工单信息,参数：ID主键ID或者OrderNo工单编号（必传）
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.V3_CopyDispatchOrderNew(QMD.Model.DispatchordersDto)">
            <summary>
            复制派发工单，复制派发工单基本信息，包括：申请人，抄送人，扩展属性，涉及设备，涉及网管
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.V3_GetDelayApprover(QMD.Model.DispatchordersDto)">
            <summary>
            获取
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.DispatchOrderController.RollBackNetproviderRel(QMD.Model.NetproviderrelDto)">
            <summary>
            打回接口，参数：OrderNo，派发子工单号
            </summary>
            <returns></returns>
        </member>
        <member name="T:QMD.Web.Controllers.QMOV2.ExecuteJobController">
            <summary>
            定时任务接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ExecuteJobController.GetJobs">
            <summary>
            获取所有的定时任务信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ExecuteJobController.RunJob(QMD.Model.emt.ExecuteJobDto)">
            <summary>
            立即执行定时任务，必传参数：任务编码(JobCode),任务分组(JobGroup)
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="T:QMD.Web.Controllers.QMOV2.ExecuteTaskController">
            <summary>
            实施任务接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ExecuteTaskController.QueryExecuteTasksByOrder(System.String,System.String)">
            <summary>
            查询变更工单下的实施任务集合（根据变更工单），并给出建议的名单
            </summary>
            <param name="orderNo">报备工单号</param>
            <param name="taskNo">实施工单号（不给就查全部）</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ExecuteTaskController.QueryExecuteTaskTypeSelect(System.String)">
            <summary>
            查询实施类型的下拉框(orderNo为空查询所有的实施类型，不为空分国际和非国际的类型)
            </summary>
            <param name="orderNo"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ExecuteTaskController.QueryObjectUnitsSelect">
            <summary>
            获取实施任务对象单位的下拉列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ExecuteTaskController.EditAndAddExecuteTasks(QMD.Model.emt.RequestDto.ExecuteTaskSubmitFormRequestDto)">
            <summary>
            批量新增和修改实施任务
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ExecuteTaskController.GetTplByOperType(System.String,QMD.DAL.Table.EnumDisOrderType)">
            <summary>
            根据实施类型获取模板
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ExecuteTaskController.JudgeDeviceCount(QMD.Model.emt.RequestDto.TaskNoAndOrderNoRequestDto)">
            <summary>
            判断设备数量是否超标（提交时判断）
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ExecuteTaskController.CopyTask(QMD.Model.emt.RequestDto.TaskNoAndOrderNoRequestDto)">
            <summary>
            复制实施任务
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ExecuteTaskController.QueryTaskDetail(System.String,System.String)">
            <summary>
            获取单个实施任务详情
            </summary>
            <param name="taskNo">实施任务单号</param>
            <param name="orderNo">变更任务单号</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ExecuteTaskController.QueryAuthorizationAttachments(System.String,System.String)">
            <summary>
            获取实施工单的授权文件
            </summary>
            <param name="taskNo"></param>
            <param name="orderNo"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ExecuteTaskController.SubmitTaskAthorizeAttachments(QMD.Model.emt.RequestDto.TaskAthorizeAttachmentsSubmitRequestDto)">
            <summary>
            授权附件的提交
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ExecuteTaskController.GetManagerApprover(System.String)">
            <summary>
            获取主管审批人(提交实任务时指定审批人弹窗下拉框)
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ExecuteTaskController.SubmitExecuteTask(System.Collections.Generic.List{QMD.Model.emt.RequestDto.TasksWithOrder})">
            <summary>
            提交实施任务
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ExecuteTaskController.GetTaskProductInfos(System.String)">
            <summary>
            完成实施任务:获取实施对象填报信息(弹窗数据)
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ExecuteTaskController.FinishExecuteTask(QMD.Model.emt.RequestDto.ExecuteTaskFinishRequestDto)">
            <summary>
            完成实施任务
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ExecuteTaskController.DeleteExecuteTask(System.Collections.Generic.List{QMD.Model.emt.RequestDto.TasksWithOrder})">
            <summary>
            删除实施任务
            </summary>
            <param name="taskWithOrderList"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ExecuteTaskController.UrgeTaskByEmail(QMD.Model.emt.RequestDto.CustomerConfirmRequestDto)">
            <summary>
            催促客户（弃用）
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ExecuteTaskController.TaskApprovel(QMD.Model.emt.RequestDto.TaskApprovelReqDto)">
            <summary>
            实施任务审批(内部主管审批)
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ExecuteTaskController.TaskCustomerConfirm">
            <summary>
            客户确认接口
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ExecuteTaskController.QueryTplsByTaskNo(QMD.Model.emt.RequestDto.CustomerConfirmRequestDto)">
            <summary>
            实施里的模板
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ExecuteTaskController.GetTaskTplValues(QMD.Model.emt.RequestDto.TaskTplValuesRequestDto)">
            <summary>
            实施工单里获取打点的详情
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="T:QMD.Web.Controllers.QMOV2.FaceController">
            <summary>
            人脸识别
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.FaceController.GetNetRegion(System.String)">
            <summary>
            获取网络是否为国际网络(国际网络不需要人脸认证)
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.FaceController.GetFaceEntity(System.String,System.String)">
            <summary>
            获取人脸样本数据：用以判断是否已经录过人脸
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.FaceController.RegisterFace(QMD.Model.AliyunFace.FaceEntity.FaceReq)">
            <summary>
            录入人脸数据(建议：图像大小不超过 5 MB，大于 32×32 像素，小于 4096×4096 像素，人脸占比不低于 64×64 像素)
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.FaceController.CompareFace(QMD.Model.AliyunFace.FaceEntity.FaceReq)">
            <summary>
            人脸比对校验
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.FaceController.QueryFaceRecogniton(System.String,System.String,QMD.DAL.Table.Source)">
            <summary>
            获取人脸注册/校验结果【web端使用】
            </summary>
            <param name="email"></param>
            <param name="TimestampFlag"></param>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="T:QMD.Web.Controllers.QMOV2.FenghuotongController">
            <summary>
            定时任务接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.FenghuotongController.GetReportOrder(QMD.Model.ReportordersDto)">
            <summary>
            获取单个变更工单的详细信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.FenghuotongController.ApprovalReportOrder(QMD.Model.ReportordersDto)">
            <summary>
            审批变更工单；一线审批，Tac审批，三线审批，LMT审批，一线主管授权，一线经理授权，客户授权
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.FenghuotongController.GetInvolveDisOrders(QMD.Model.emt.RequestDto.DispatchRequestDto)">
            <summary>
            根据产品型号（DeviceTypes）获取涉及风险的派发工单
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.OutsourcingEmployeeController.GetReportFilterOptions">
            <summary>
            获取外包员工的查询条件
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.OutsourcingEmployeeController.GetEmployees(QMD.Model.emt.RequestDto.OutsourcingEmployeeRequestDto)">
            <summary>
            查询外包员工
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.OutsourcingEmployeeController.AddEmployees(QMD.Model.emt.RequestDto.ManageEmployeesRequestDto)">
            <summary>
            添加外包员工
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.OutsourcingEmployeeController.UpdateEmployees(QMD.Model.emt.RequestDto.ManageEmployeesRequestDto)">
            <summary>
            更新外包员工
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.OutsourcingEmployeeController.DeleteEmployees(QMD.Model.emt.RequestDto.ManageEmployeesRequestDto)">
            <summary>
            删除外包员工
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.OutsourcingEmployeeController.SearchOutsourcingUserByEmailOrName(System.String)">
            <summary>
            搜索合作方员工信息，用来添加合作方信息
            </summary>
            <param name="searchContent"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.OutsourcingEmployeeController.GetUserType">
            <summary>
            获取用户类型（ Admin,FirstLineSupervisor,Other）
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.OutsourcingEmployeeController.GetServiceCenter">
            <summary>
            获取技服中心列表
            </summary>
            <returns></returns>
        </member>
        <member name="T:QMD.Web.Controllers.QMOV2.PermissionV2Controller">
            <summary>
            QMO_V2权限接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.PermissionV2Controller.QueryRolesWithoutPagination(System.String)">
            <summary>
            获取角色列表(不分页)
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.PermissionV2Controller.AddOrUpdateRole(QMD.Model.emt.PermissionV2.RoleInfoDto)">
            <summary>
            增加或删除角色
            </summary>
            <param name="roleInfoDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.PermissionV2Controller.DeleteRoles(System.Collections.Generic.List{System.String})">
            <summary>
            删除角色
            </summary>
            <param name="roleIdList"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.PermissionV2Controller.QueryProductSpeSelect">
            <summary>
            查询角色的专业属性
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.PermissionV2Controller.QueryOperatorSelect">
            <summary>
            查询角色的运营商属性
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.PermissionV2Controller.QueryRoleApproveTypeSelect">
            <summary>
            查询角色的审批类型的属性下拉框数据(该属性确定角色是经理人,接口人,工程师或其他等,审批会用)
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.PermissionV2Controller.QueryRoleDataTypeSelect">
            <summary>
            查询角色的数据类型的属性下拉框数据(该属性确定角色是一线,二线或三线角色)
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.PermissionV2Controller.QueryFunctionsWithoutPagination(System.String)">
            <summary>
            获取功能列表不分页
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.PermissionV2Controller.QueryFunctions(Common.DAL.Methods.PageCriteria,System.String)">
            <summary>
            获取功能列表(分页)
            </summary>
            <param name="pageCriteria"></param>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.PermissionV2Controller.AddOrUpdateFunction(QMD.DAL.Table.FunctionInfo)">
            <summary>
            添加或修改功能（如果是根节点，不要传parentId或传空值）
            </summary>
            <param name="function"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.PermissionV2Controller.DeleteFunctions(System.Collections.Generic.List{System.String})">
            <summary>
            删除功能
            </summary>
            <param name="functionIds"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.PermissionV2Controller.QueryUserNetTypeSelect">
            <summary>
            查询用户网络类型（配置界面需要配置）
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.PermissionV2Controller.QueryFunctionAndDataByRole(System.String)">
            <summary>
            查询当前角色的功能和数据
            </summary>
            <param name="roleId"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.PermissionV2Controller.QueryUsersByRoleWithPagination(System.String,System.String,Common.DAL.Methods.PageCriteria)">
            <summary>
            查询当前角色的用户(分页)
            </summary>
            <param name="roleId"></param>
            <param name="search"></param>
            <param name="pageCriteria"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.PermissionV2Controller.QueryUsersByRoleWithoutPagination(System.String,System.String)">
            <summary>
            查询当前角色的用户(不分页)
            </summary>
            <param name="roleId"></param>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.PermissionV2Controller.QueryUsersWhenAddRoleWithPagination(System.String,System.String,Common.DAL.Methods.PageCriteria)">
            <summary>
            当前角色添加用户时的查询
            </summary>
            <param name="roleId"></param>
            <param name="search"></param>
            <param name="pageCriteria"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.PermissionV2Controller.SavePermissionByRole(QMD.Model.emt.PermissionV2.SavePermissionByRoleDto)">
            <summary>
            保存角色和数据,功能,用户的关系
            </summary>
            <param name="savePermissionByRoleDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.PermissionV2Controller.GetFunctionByModule(System.String)">
            <summary>
            获取当前用户的功能列表
            </summary>
            <param name="moduleName">报备，变更，实施（传值从dispatch-order， report-order，execute-task中选）</param>
            <returns></returns>
        </member>
        <member name="T:QMD.Web.Controllers.QMOV2.ReportOrderController">
            <summary>
            审批历史记录相关接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.GetPersonRepOrdersCondition">
            <summary>
            获取派发工单查询条件
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.GetPersonReportOrders(QMD.Model.emt.RequestDto.ReportRequestDto)">
            <summary>
            获取个人的报备工单
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.GetNetProductLine(QMD.Model.emt.RequestDto.ReportRequestDto)">
            <summary>
            新增变更工单表单内的数据源-产线信息（产出线，产品线，产品线LMT）,产品信息（产品型号，单盘型号，硬件版本，软件版本），网管信息（网管系列，网管版本，网管补丁）
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.GetReportOrderFormData_OLD">
            <summary>
            获取变更工单表单初始化数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.GetReportOrderFormData">
            <summary>
            获取变更工单表单初始化数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.GetExtProperties">
            <summary>
            获取变更工单的附加属性
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.GetNetproviderRelByDisOrder(QMD.Model.emt.RequestDto.ReportRequestDto)">
            <summary>
            根据派发任务单号获取用户权限内网络列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.GetChangeUsers(QMD.Model.emt.RequestDto.ReportRequestDto)">
            <summary>
            获取当前审批的变更审批人列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.SearchUserByEmailOrName(QMD.Model.emt.RequestDto.ReportRequestDto)">
            <summary>
            根据邮箱或者用户姓名获取人员信息
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.GetInvolveDisOrders(QMD.Model.emt.RequestDto.DispatchRequestDto)">
            <summary>
            根据产品型号（DeviceTypes）获取涉及风险的派发工单
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.GetReportOrder(QMD.Model.ReportordersDto)">
            <summary>
            获取单个变更工单的详细信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.GetApprovers(QMD.Model.emt.RequestDto.ReportRequestDto)">
            <summary>
            根据工单号获取工单信息，根据工单级别获取工单审批人信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.GetCustomers(QMD.Model.emt.RequestDto.GetCustomerRequestDto)">
            <summary>
            获取已填报过的用户信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.AddReportOrder(QMD.Model.ReportordersFormDto)">
            <summary>
            新增变更工单
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.GetDepartmentNets(QMD.Model.emt.RequestDto.ReportRequestDto)">
            <summary>
            获取所属技服中心的所有网络数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.UpdateReportOrder(QMD.Model.ReportordersFormDto)">
            <summary>
            编辑变更工单
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.CopyReportOrder(QMD.Model.ReportordersDto)">
            <summary>
            复制变更工单，除审核人、授权人和保障人外，其他信息同步拷贝，必传参数：OrderNo
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.SubmitReportOrder(QMD.Model.ReportordersDto)">
            <summary>
            提交变更工单
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.ApprovalReportOrder(QMD.Model.ReportordersDto)">
            <summary>
            审批变更工单；一线审批，Tac审批，三线审批，LMT审批，一线主管授权，一线经理授权，客户授权
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.GetCusAuthAttachInfo(QMD.Model.ReportordersDto)">
            <summary>
            获取工单的客户授权附件信息，如果没有则是长度为0的数组，示例：[],必传参数：OrderNo
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.UploadCusAuthAttachInfo(QMD.Model.emt.RequestDto.RepUploadFileRequestDto)">
            <summary>
            上传客户授权附件,参数OrderNo-报备工单号，Files-附件信息（允许上传多个文件）
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.ResendCustomerMail(QMD.Model.emt.RequestDto.ReportRequestDto)">
            <summary>
            重发邮件给客户授权
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.DeleteReportOrders(QMD.Model.emt.RequestDto.ReportRequestDto)">
            <summary>
            逻辑删除变更工单数据,只有工单申请人、一线接口人、一线主管才可以删除工单数据，参数：RepOrderNos
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.FinishReportOrders(QMD.Model.emt.RequestDto.ReportRequestDto)">
            <summary>
            完成变更工单，只能一线审核和授权该工单的接口人来完成工单,一次只能完成一个工单，参数：OrderNo
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.GetReportOrderFlow(QMD.Model.emt.RequestDto.ReportRequestDto)">
            <summary>
            获取工单流程图,参数OrderNo
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.GetReportOrderLog(QMD.Model.emt.RequestDto.ReportRequestDto)">
            <summary>
            获取工单流程日志,参数OrderNo
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.GetCountByDealtType">
            <summary>
            获取报备工单各个维度总数（用于角标）
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.GetOrderListByInvUser(QMD.Model.emt.RequestDto.ReportRequestDto)">
            <summary>
            查询用户涉及的工单数据,参数：OperateKind[工单类型：派发工单 = 1,变更工单 = 3]，OneClickSourceUserEmail[工单原执行人]
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.OneClickConversion(QMD.Model.emt.RequestDto.ReportRequestDto)">
            <summary>
            一键转单功能，用户选择工单进行一键转单，一键转单后，不管用户是什么角色都会切换成新用户,参数：OperateKind[工单类型：派发工单 = 1,变更工单 = 3]，OneClickSourceUserEmail[工单原执行人],OneClickDestUserEmail[工单新执行人]
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.ExportReportOrder(QMD.Model.emt.RequestDto.ReportRequestDto)">
            <summary>
            导出工单数据
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.ImpAngGrtOrderList(QMD.Model.emt.RequestDto.ReportRequestDto)">
            <summary>
            获取用户的工单信息,必传参数，报备工单号：OrderNo
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.TransferOrders(System.Collections.Generic.List{QMD.Model.emt.RequestDto.TransferRequestDto})">
            <summary>
            一键转单，必传参数：工单号[RepOrderNos]，工单原执行人[OneClickSourceUserEmail],工单新执行人[OneClickDestUserEmail],执行人类型[UserType]（保障人->1，实施人->2）
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.GetUserTransferList(QMD.Model.emt.RequestDto.ReportRequestDto)">
            <summary>
            获取用户的收单列表
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.ConfirmTransferOrders(QMD.Model.emt.RequestDto.ReportRequestDto)">
            <summary>
            确认收单,必传参数：TransferIds
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.GetHisNoticeTasks(QMD.Model.emt.EmtDingtalkParamReqDto)">
            <summary>
            获取烽火通预报通报数据
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.ExportHisNoticeTasks(QMD.Model.emt.EmtDingtalkParamReqDto)">
            <summary>
            导出烽火通预报通报数据
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.DoneDingTask(QMD.Model.emt.DoneDingTaskDto)">
            <summary>
            完成通知类烽火通待办
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.ReSendDingTask(QMD.Model.emt.EmtDingtalkTaskDto)">
            <summary>
            从新发送钉钉待办
            </summary>
            <param name="taskDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.ReportOrderController.EditReportOrderCustomer(QMD.Model.emt.InvolveUserDto)">
            <summary>
            只有客户授权阶段才可以修改客户信息
            </summary>
            <param name="customerDto"></param>
            <returns></returns>
        </member>
        <member name="T:QMD.Web.Controllers.QMOV2.StatisticsController">
            <summary>
            工单统计相关接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.StatisticsController.GetReportFilterOptions">
            <summary>
            获取报备工单筛选条件
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.StatisticsController.GetReportOrderStatistics(QMD.Model.emt.Statistics.ReportStatisticsRequestDto)">
            <summary>
            获取报备工单统计数据
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.StatisticsController.GetReportRegionStatistics(QMD.Model.emt.Statistics.ReportStatisticsRequestDto)">
            <summary>
            获取报备工单区域维度统计
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.StatisticsController.GetReportProfessionStatistics(QMD.Model.emt.Statistics.ReportStatisticsRequestDto)">
            <summary>
            获取报备工单专业维度统计
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.StatisticsController.GetReportProductMaintenanceStatistics(QMD.Model.emt.Statistics.ReportStatisticsRequestDto)">
            <summary>
            获取报备工单产品维护统计
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.StatisticsController.GetReportCustomerTypeStatistics(QMD.Model.emt.Statistics.ReportStatisticsRequestDto)">
            <summary>
            获取报备工单客户类型维护统计
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.StatisticsController.GetDispatchFilterOptions">
            <summary>
            获取派发工单筛选条件
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.StatisticsController.GetDispatchOrderStatistics(QMD.Model.emt.Statistics.DispatchStatisticsRequestDto)">
            <summary>
            获取派发工单统计数据
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.StatisticsController.GetDispatchOrderNoStatistics(QMD.Model.emt.Statistics.DispatchStatisticsRequestDto)">
            <summary>
            获取派发工单单号维度统计
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.StatisticsController.GetDispatchRegionStatistics(QMD.Model.emt.Statistics.DispatchStatisticsRequestDto)">
            <summary>
            获取派发工单区域维度统计
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.StatisticsController.GetDispatchProfessionStatistics(QMD.Model.emt.Statistics.DispatchStatisticsRequestDto)">
            <summary>
            获取派发工单专业维度统计
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.StatisticsController.GetDispatchProductStatistics(QMD.Model.emt.Statistics.DispatchStatisticsRequestDto)">
            <summary>
            获取派发工单产品维护统计
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.StatisticsController.GetDispatchRegionSubWorkOrderStatistics(QMD.Model.emt.Statistics.DispatchStatisticsRequestDto)">
            <summary>
            获取派发工单区域维度子工单状态统计
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.StatisticsController.GetDispatchProfessionSubWorkOrderStatistics(QMD.Model.emt.Statistics.DispatchStatisticsRequestDto)">
            <summary>
            获取派发工单专业维度子工单状态统计
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.StatisticsController.GetDispatchProductSubWorkOrderStatistics(QMD.Model.emt.Statistics.DispatchStatisticsRequestDto)">
            <summary>
            获取派发工单产品维度子工单状态统计
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.StatisticsController.GetDispatchCustomerTypeSubWorkOrderStatistics(QMD.Model.emt.Statistics.DispatchStatisticsRequestDto)">
            <summary>
            获取派发工单客户分类维度子工单状态统计
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.StatisticsController.GetDispatchOrderSubWorkOrderStatistics(QMD.Model.emt.Statistics.DispatchStatisticsRequestDto)">
            <summary>
            获取派发工单号维度子工单状态统计
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.StatisticsController.ExportReportStatistics(QMD.Model.emt.Statistics.StatisticsType,QMD.Model.emt.Statistics.ReportStatisticsRequestDto)">
            <summary>
            导出报备工单统计数据
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.StatisticsController.ExportDispatchStatistics(QMD.Model.emt.Statistics.StatisticsType,QMD.Model.emt.Statistics.DispatchStatisticsRequestDto)">
            <summary>
            导出派发工单统计数据
            </summary>
        </member>
        <member name="T:QMD.Web.Controllers.QMOV2.TraceBackOrderController">
            <summary>
            回溯工单
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.TraceBackOrderController.QueryConditionTracebackOrder">
            <summary>
            获取回溯工单查询条件
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.TraceBackOrderController.GetTracebackOrderFormOptions">
            <summary>
            回溯工单表单下拉项
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.TraceBackOrderController.GetTracebackOrderPageList(QMD.Model.tbo.QueryTracebackOrderDto)">
            <summary>
            回溯工单列表
            </summary>
            <param name="queryDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.TraceBackOrderController.AddOrUpdateTracebackOrder(QMD.Model.tbo.TraceBackOrderDto)">
            <summary>
            新增/编辑回溯工单
            </summary>
            <param name="traceBackOrderDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.TraceBackOrderController.GetTracebackOrderDetail(System.String,System.String)">
            <summary>
            回溯工单/子任务详情
            </summary>
            <param name="id">回溯工单Id</param>
            <param name="taskId">子任务Id</param>
            两个参数可传一个
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.TraceBackOrderController.GetTracebackTaskFormOptions">
            <summary>
            回溯工单子任务表单下拉项【改进原因】
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.TraceBackOrderController.AddTracebackTask(System.Collections.Generic.List{QMD.Model.tbo.TraceBackTaskDto})">
            <summary>
            新增回溯工单子任务（支持批量新增）
            </summary>
            <param name="reqDtos"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.TraceBackOrderController.UpdateTracebackTask(QMD.Model.tbo.TraceBackTaskDto)">
            <summary>
            编辑回溯工单子任务
            </summary>
            <param name="reqDto">回溯工单子任务Dto</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.TraceBackOrderController.GetTaskById(System.String)">
            <summary>
            填报详情页
            </summary>
            <param name="id">任务主键Id</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.TraceBackOrderController.DeleteTracebackOrder(System.String,System.String)">
            <summary>
            删除回溯工单/子任务
            </summary>
            <param name="id">回溯工单Id</param>
            <param name="taskId">子任务Id</param>
            两个参数可传一个
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.TraceBackOrderController.RevokeTrackbackOrder(System.String,System.String,System.String)">
            <summary>
            撤销子任务
            </summary>
            <param name="id">回溯工单ID</param>
            <param name="taskId">回溯工单子任务ID</param>
            <param name="revokeReason">撤销原因</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.TraceBackOrderController.SubmitTrackbackOrder(System.String,System.String)">
            <summary>
            提交任务
            </summary>
            <param name="id">回溯工单ID【批量提交子任务】</param>
            <param name="taskId">子任务ID【提交单个子任务】</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.TraceBackOrderController.GetTrackbackOrderLog(System.String)">
            <summary>
            获取回溯工单操作日志
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.TraceBackOrderController.ExcuteTracebackTask(QMD.Model.tbo.TraceBackExcuteDto)">
            <summary>
            填报任务
            </summary>
            <param name="reqDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.TraceBackOrderController.ApproveTracebackTask(QMD.Model.tbo.ApproveReqDto)">
            <summary>
            任务审核
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.TraceBackOrderController.ExportTrackbackOrder">
            <summary>
            导出数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:QMD.Web.Controllers.QMOV2.UserLevelController">
            <summary>
            用户级别资源处理接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.UserLevelController.GetUserLevelCondition(QMD.Model.emt.RequestDto.UserLevelRequestDto)">
            <summary>
            获取派发工单查询条件
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.UserLevelController.GetUserLevelPageList(QMD.Model.emt.RequestDto.UserLevelRequestDto)">
            <summary>
            获取用户级别信息分页数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.UserLevelController.QueryLevelOrPosition(QMD.Model.emt.RequestDto.PersonLevelDto)">
            <summary>
            根据用户信息获取用户的级别信息，一般用于填报备工单时，选了系统用户后，再关联级别信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.UserLevelController.RelatePersonLevel(QMD.Model.emt.RequestDto.PersonLevelDto)">
            <summary>
            关联人员级别信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.UserLevelController.UnBindRelatePersonLevel(QMD.Model.emt.RequestDto.PersonLevelDto)">
            <summary>
            解绑用户级别信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.UserLevelController.ExportOutSourcePerson">
            <summary>
            导出合作方人员
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.UserLevelController.ImportOutSourcePerson(Microsoft.AspNetCore.Http.IFormCollection)">
            <summary>
            导入合作方人员
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.UserLevelController.ExportOwnSourcePerson">
            <summary>
            导出自有人员
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.UserLevelController.ImportOwnSourcePerson(Microsoft.AspNetCore.Http.IFormCollection)">
            <summary>
            导入自有人员
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.UserLevelController.QueryPersonCommon(QMD.Model.emt.RequestDto.UserLevelRequestDto)">
            <summary>
            查询自有或合作方人员（通用，支持条件，合并分组分页）
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.UserLevelController.AddOrEditPerson(QMD.Model.emt.PersonQueryCommonDto)">
            <summary>
            编辑或新增自有或合作方人员（通用）
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.UserLevelController.DeletePerson(QMD.Service.QMOV2Service.DeletePersonRequest)">
            <summary>
            删除自有或合作方人员（通用，支持批量）
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.QMOV2.UserLevelController.Test">
            <summary>
            用来将已经同步好了的资管数据分别添加到自有和外包表中
            </summary>
            <returns></returns>
        </member>
        <member name="T:QMD.Web.Controllers.RegionController">
            <summary>
            区域的相关接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.RegionController.GetRegions(System.String,System.String,System.Boolean)">
            <summary>
            获取Region节点列表
            </summary>
            <param name="culture">多语言编码</param>
            <param name="parentCode">父级编码，可以为空，为空时理论上是返回国家级的区域信息</param>
            <param name="recur">是否递归获取所有子节点，为true时，会把所有子节点以及孙子节点等等全部整理成树状结构返回</param> 
        </member>
        <member name="M:QMD.Web.Controllers.RegionController.GetRegion(System.String,System.String,System.Boolean)">
            <summary>
            通过区域Code获取Region信息
            </summary>
            <param name="culture">多语言编码</param>
            <param name="code">区域编码</param> 
            <param name="recur">是否递归获取所有子节点，为true时，会把所有子节点以及孙子节点等等全部整理成树状结构返回</param> 
        </member>
        <member name="M:QMD.Web.Controllers.RegionController.SetRegionDisplayName(QMD.Model.SetLangDto)">
            <summary>
            设置区域的显示名称
            </summary>
            <param name="req">多语言设置实体，Key为多语言的Code</param> 
        </member>
        <member name="M:QMD.Web.Controllers.RegionController.GetRegionsForApp(System.String)">
            <summary>
            获取国家省份(国内获取到省，国际获取到国家)
            </summary>
            <param name="culture"></param>
        </member>
        <member name="T:QMD.Web.Controllers.ServiceLineController">
            <summary>
            服务线(销售平台)的相关接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.ServiceLineController.GetAll(System.String)">
            <summary>
            查询所有服务线
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.ServiceLineController.SetServiceLineNameByID(QMD.Model.SetLangDto)">
            <summary>
            设置服务线的显示名称
            </summary>
            <param name="req">多语言设置实体，Key为ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.ServiceLineController.SetServiceLineNameByCode(QMD.Model.SetLangDto)">
            <summary>
            设置服务线的显示名称
            </summary>
            <param name="req">多语言设置实体，Key为Code</param>  
        </member>
        <member name="M:QMD.Web.Controllers.ServiceLineController.Insert(QMD.Model.ServiceLineDto)">
            <summary>
            新增服务线
            </summary>
            <param name="req">服务线数据实体对象</param>  
        </member>
        <member name="M:QMD.Web.Controllers.ServiceLineController.Update(QMD.Model.ServiceLineDto)">
            <summary>
            更新服务线
            </summary> 
            <param name="req">服务线数据实体对象</param>  
        </member>
        <member name="M:QMD.Web.Controllers.ServiceLineController.Delete(System.String)">
            <summary>
            删除服务线
            </summary> 
            <param name="id">服务线数据实体ID</param>  
        </member>
        <member name="T:QMD.Web.Controllers.TaskDimValueController">
            <summary>
            各种附加信息的获取接口，存储包括审批、进度等状态，项目二阶段审批功能重点工作
            </summary>
        </member>
        <member name="T:QMD.Web.Controllers.TaskItemController">
            <summary>
            类似以前的SiteInfo基础表，任务的目标对象基础信息相关接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.TaskItemController.GetAll(System.String)">
            <summary>
            获取所有基础数据，慎用！
            </summary> 
            <param name="culture">多语言编码</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskItemController.Get(System.String)">
            <summary>
            查询单个基础数据的信息
            </summary>
            <param name="taskItemID">基础数据ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskItemController.GetTaskItemForMobile(System.String)">
            <summary>
            查询单个基础数据的信息
            </summary>
            <param name="taskItemID">基础数据ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskItemController.GetPreviewsList(System.String,System.String,System.String,System.String,System.String,System.Nullable{System.Boolean},System.String[],System.Nullable{System.Boolean},Common.DAL.Methods.PageCriteria)">
            <summary>7 6m
            预览页面列表查询
            </summary>
            <param name="projectID">项目ID</param>
            <param name="siteID">站点ID，对应数据库的Code字段</param>
            <param name="siteName">站点名称，对应数据库DisplayName字段</param>
            <param name="region">区域</param>
            <param name="contractor">分包商</param>
            <param name="hasImg">是否包含图片文件</param>
            <param name="unionItemIDs">客户端最近查看过的数据的ID</param>
            <param name="hasNotApproved">是否包含被打回的</param>
            <param name="pageCriteria">分页条件</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.TaskItemController.GetReadyApprovalList(System.String,System.String,System.String,System.String,System.String,System.Nullable{System.Boolean},System.Int32,System.String[],Common.DAL.Methods.PageCriteria)">
            <summary>
            待审核基础数据查询
            </summary>
            <param name="projectID">项目ID</param>
            <param name="siteID">站点ID，对应数据库的Code字段</param>
            <param name="siteName">点名称，对应数据库DisplayName字段</param>
            <param name="region">区域</param>
            <param name="contractor">分包商</param>
            <param name="needApproval">是否待审批，只会处理Null和true的情况,Null即为所有，true只返回待审核的</param>
            <param name="level">审核级别，1-2</param>
            <param name="unionItemIDs">客户端最近查看过的数据的ID</param>
            <param name="pageCriteria">分页条件</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.TaskItemController.QueryByProject(System.String,System.String,Common.DAL.Methods.PageCriteria,System.String)">
            <summary>
            根据项目ID查询带分页的基础数据
            </summary> 
            <param name="projectID">项目ID</param>
            <param name="search">搜索关键字,目前只支持DisplayName和code</param>
            <param name="pageCriteria">分页条件</param> 
            <param name="culture">多语言编码</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskItemController.GetTaskItemProgSelections(System.String)">
            <summary>
            获取某个站点的用户自定义工序进度可选项列表，树形结构返回
            </summary>
            <param name="taskItemID">基础数据的ID</param>
        </member>
        <member name="M:QMD.Web.Controllers.TaskItemController.SetStepProg(QMD.Model.StepProgReqDto)">
            <summary>
            设置某个站点的用户自定义工序进度
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.TaskItemController.QueryByServiceLine(System.String,Common.DAL.Methods.PageCriteria,System.String)">
            <summary>
            根据服务线ID查询基础数据
            </summary>
            <param name="serviceLineID">服务线ID</param>
            <param name="pageCriteria">分页条件</param> 
            <param name="culture">多语言编码</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskItemController.AssignTaskItemTpls(QMD.Model.TplsAssignReqDto)">
            <summary>
            给基础数据分配任务类型
            </summary>  
        </member>
        <member name="M:QMD.Web.Controllers.TaskItemController.RemoveTaskItemTpls(QMD.Model.TplsAssignReqDto)">
            <summary>
            给基础数据移除已分配的任务类型
            </summary>  
        </member>
        <member name="M:QMD.Web.Controllers.TaskItemController.Insert(QMD.Model.TaskItemDto)">
            <summary>
            插入一条基础数据
            </summary>
            <param name="req">基础数据实体</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskItemController.Update(QMD.Model.TaskItemDto)">
            <summary>
            更新一条基础数据
            </summary>
            <param name="req">基础数据实体</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskItemController.UpdateIncludeTpls(QMD.Model.TaskItemIncludeTplsReqDto)">
            <summary>
            更新一条基础数据，并且同时更新基础数据和任务类型的绑定关系
            </summary>
            <param name="req">基础数据实体，包括任务类型关系</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskItemController.InsertIncludeTpls(QMD.Model.TaskItemIncludeTplsReqDto)">
            <summary>
            插入一条基础数据，并且同时创建基础数据和任务类型的绑定关系
            </summary>
            <param name="req">基础数据实体，包括任务类型关系</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskItemController.Delete(System.String)">
            <summary>
            根据ID删除一条基础数据（因为设置了联合索引所以必须真删除）
            </summary>
            <param name="id">基础数据的ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskItemController.BatchDelete(System.Collections.Generic.List{System.String})">
            <summary>
            批量删除基础数据
            </summary>
            <param name="ids">基础数据的ID集合</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskItemController.EmailRemind(System.Collections.Generic.List{System.String},System.String)">
            <summary>
            整改派发邮件提醒
            </summary>
            <param name="ids">站点Ids</param>
            <param name="culture">语言</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.TaskItemController.GetPreviewsList(QMD.Model.ReqTaskItemDto,Common.DAL.Methods.PageCriteria)">
            <summary>
            预览页面列表查询
            </summary>
            <param name="taskItem">请求传入的参数:</param>
            <param name="pageCriteria">分页条件</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.TaskItemController.GetPreviewsCondition(QMD.Model.ReqTaskItemDto)">
            <summary>
            获取查询的条件数据
            </summary>
            <param name="reqTaskDto">请求传入的参数：ProjectId, Culture</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.TaskItemController.GetPreviewsList(QMD.Model.ReqTaskApprovalItemDto,Common.DAL.Methods.PageCriteria)">
            <summary>
            查询审批列表
            </summary>
            <param name="reqDto"></param>
            <param name="pageCriteria"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.TaskItemController.SubmitTaskItem(QMD.Model.ReqTaskItemDto)">
            <summary>
            ITR工单操作人上传完成所有文件后，需要手动提交一下任务，根据是否每步必填的工序都提交来判断是否可以提交
            </summary>
            <param name="reqTaskDto">请求传入的参数:TaskId,TplId,TriggerDay,如果是周期任务才需要TriggerDay</param>
            <returns></returns>
        </member>
        <member name="T:QMD.Web.Controllers.TaskStepValueController">
            <summary>
            工序值相关的接口，用来查看工序具体某条值数据
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueController.GetStepValues(System.String,System.String,System.String)">
            <summary>
            根据基础数据ID和工具ID获取单个工序值
            </summary>
            <param name="taskItemID">基础数据ID</param> 
            <param name="stepID">工具ID，因为通过工序可以查询到模板ID，所以这里不需要模板ID</param>  
            <param name="triggerDay">周期触发日期</param>
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueController.GetStepValueByID(System.String)">
            <summary>
            根据工序值ID获取工序的值
            </summary> 
            <param name="stepValueID">工序ID，因为通过工序可以查询到模板ID，所以这里不需要模板ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueController.GetTplValues_OLD(System.String,System.String,System.String,System.String,System.Int32)">
            <summary>
            获取某一个基础数据的某一个模板下的所有工序值
            </summary>
            <param name="taskItemID">基础数据ID</param>
            <param name="tplID">模板ID</param> 
            <param name="triggerDay">查询日期，格式2022-01-20</param>
            <param name="orderFilter">工单过滤条件（0全部，1新增，2遗留）</param>
            <param name="valueContent">子工单模糊查询</param>
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueController.GetTplValues(System.String,System.String,System.String,System.String,System.Int32,System.String,System.String)">
            <summary>
            获取某一个基础数据的某一个模板下的所有工序值
            </summary>
            <param name="taskItemID">基础数据ID</param>
            <param name="tplID">模板ID</param> 
            <param name="triggerDay">查询日期，格式2022-01-20</param>
            <param name="orderFilter">工单过滤条件（0全部，1新增，2遗留）</param>
            <param name="valueContent">子工单模糊查询</param>
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueController.GetTplValueForQaOrder(QMD.Model.ReqTaskStepValueForQaOrderNoDto)">
            <summary>
            获取某一个QA巡讲项下所有工序值
            </summary>
            <param name="reqDto">OrderNo:qa母工单号，SearchContent：[模糊查询]对象id/对象名称/设备类型，PageIndex：页码，PageSize：页数,orderFilter:工单过滤条件（0全部，1新增，2遗留,3待处理，4已处理）</param>
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueController.GetGroupValues(System.String,System.String,System.String)">
             <summary>
             获取某一个基础数据的某一个分组下的所有工序值
             </summary>
             <param name="taskItemID">基础数据ID</param>
             <param name="groupID">分组ID</param> 
            <param name="triggerDay">周期任务触发日期</param>
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueController.UpdateStepValue(QMD.Model.StepValueInputDto)">
            <summary>
            修改工序值
            ["taskItemID"]基础数据ID，["ValueID"]：工序值的ID，["value"]或者file提交数据
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueController.AddStepValue(QMD.Model.StepValueInputDto)">
            <summary>
            新增工序值
            ["taskItemID"]：基础数据ID，["stepID"]：工序ID，["value"]或者file提交数据，会自动过滤Order属性，最新文件添加到最后
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueController.SaveStepValues(System.Collections.Generic.List{QMD.Model.StepValueInputDto})">
            <summary>
            保存工序值信息
            </summary>
            <param name="reqs"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueController.GetSyncQaRecords(QMD.Model.QA.SyncQaRecordDto)">
            <summary>
            获取QA数据同步记录
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueController.RenameFilename(QMD.Model.RenameDto)">
            <summary>
            修改已上传的某个工序的文件名，如果重名会自动在文件名上加“_数字”,返回重命名以后的名称
            </summary> 
            <param name="req">ID：工序值ID，newName：新文件名</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueController.DeleteValueByStepValueID(System.String,System.String,System.Boolean)">
            <summary>
            根据工序值ID删除某个工序值
            </summary>
            <param name="taskItemID">基础数据ID</param>
            <param name="stepValueID">工序值ID</param> 
            <param name="force">直接删除，不考虑权限问题</param>
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueController.DeleteValuesByStepID(System.String,System.String,System.Boolean)">
            <summary>
            根据基础数据ID和工具ID删除某一个工序下所有的值
            </summary>
            <param name="taskItemID">基础数据ID</param>
            <param name="stepID">工序ID</param> 
            <param name="force">直接删除，不考虑权限问题</param>
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueController.DeleteForceRectifyValues(QMD.Model.DeleteStepValuesReqDto)">
            <summary>
            根据基础数据ID和整改派发元素值IDs删除元素
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueController.testWaterMark">
            <summary>
            测试生成缩略图
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueController.test1(System.Collections.Generic.List{System.String})">
            <summary>
            测试生成缩略图
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueController.test_BalanceLastMonth(QMD.Model.QA.QaReportRequestDto)">
            <summary>
            测试生成缩略图
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueController.test_HandlerMissData">
            <summary>
            测试生成缩略图
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueController.PreViewTaskDetail(QMD.Model.TaskItemDetailInputDto)">
            <summary>
            预览任务详情，下载PDF
            </summary>
            <param name="inputDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueController.IsQaAdmin">
            <summary>
            判断是否为QA管理员，只有是QA管理员才会显示“核减”按钮
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueController.ReduceQaOrder(QMD.Model.QA.ReduceOrderDto)">
            <summary>
            核减巡检问题工单：如果OrderNo不为空，则核减单个巡检项的工单，关闭当前巡检项下当前月份的所有巡检对象；
            如果ItemId和TplId不为空，则核减当前网络当前月份内的制定巡检工具（巡检模板）下的所有巡检工单。其他情况视为无效参数。核减成功后，提示信息并刷新页面
            </summary>
            <param name="requestDto"></param>
            <returns></returns>
        </member>
        <member name="T:QMD.Web.Controllers.TaskStepValueSampleController">
            <summary>
            工序样例的相关接口，主要针对文件类型的工序使用，当然也不排斥普通数值类型的样例，但是目前需求里面没提到
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueSampleController.GetStepValueSamples(System.String)">
            <summary>
            根据工序ID获取所有的样例
            </summary>
            <param name="stepID">工序ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueSampleController.ExportStepValueSamples(System.String)">
            <summary>
            获取某一个工序的样例文件集合，以zip压缩包的CDN下载地址为结果
            </summary>
            <param name="stepID">工序ID</param>
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueSampleController.GetStepValueSample(System.String)">
            <summary>
            根据样例ID获取某一条样例的值
            </summary>
            <param name="sampleID">样例的ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueSampleController.GetTplSampleValues(System.String)">
            <summary>
            根据模板获取下面所有的样例数据
            </summary>
            <param name="tplID">模板ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueSampleController.GetGroupSampleValues(System.String)">
            <summary>
            获取分组下面所有的样例数据
            </summary>
            <param name="groupID">模板ID</param>
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueSampleController.AddStepValueSample(QMD.Model.StepValueInputDto)">
            <summary>
            新增工序样例
            form["stepID"]：工序ID，file提交数据，忽略HybridOrder参数，样例的Hybrid组一般只需要一个
            </summary> 
            <param name="req">工序值对象，如果是文件类型则需要File的文件流（Value自动由文件名生成），如果不是需要填入Value</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueSampleController.UpdateStepValueSample(QMD.Model.StepValueInputDto)">
            <summary>
            更新工序样例
            form["ValueID"]：样例值的ID，file提交数据，忽略HybridOrder参数，样例的Hybrid组一般只需要一个
            </summary> 
            <param name="req">工序值对象，如果是文件类型则需要File的文件流（Value自动由文件名生成），如果不是需要填入Value</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueSampleController.DeleteStepValueSample(System.String)">
            <summary>
            根据样例ID删除某一特定样例
            </summary>
            <param name="sampleID">样例ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskStepValueSampleController.DeleteStepValueSamples(System.String)">
            <summary>
            根据工序ID删除某个工序下面所有的样例
            </summary>
            <param name="stepID">工序ID</param> 
        </member>
        <member name="T:QMD.Web.Controllers.TaskTplController">
            <summary>
            工序模板相关接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.GetMilestoneOptions">
            <summary>
            获取里程碑的可选项目
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.GetMultiType(System.String,System.String)">
            <summary>
            获取模板类型的下拉数据源
            </summary>
            <param name="culture"></param>
            <param name="projectId"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.GetFirstNode(System.String)">
            <summary>
            获取模板类型第一级的全部下拉选项
            </summary>
            <param name="culture">语言</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.GetFirstNode(System.String,System.String)">
            <summary>
            获取模板类型第一级下拉选项
            </summary>
            <param name="culture">语言</param>
            <param name="projectId">项目id</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.GetSecondNode(System.String)">
            <summary>
            获取模板类型第二级的全部下拉选项
            </summary>
            <param name="culture">语言</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.GetSecondNode(System.String,System.String,System.String)">
            <summary>
            获取模板类型第二级下拉选项
            </summary>
            <param name="culture">语言</param>
            <param name="projectId">项目id</param>
            <param name="firstNode">第一级编码</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.GetThirdNode(System.String)">
            <summary>
            获取模板类型第三级的全部下拉选项
            </summary>
            <param name="culture">语言</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.GetThirdNode(System.String,System.String,System.String,System.String)">
            <summary>
            获取模板类型第三级下拉选项
            </summary>
            <param name="culture">语言</param>
            <param name="projectId">项目Id</param>
            <param name="firstNode">第一级编码</param>
            <param name="secondNode">第二级编码</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.GetAll(System.String)">
            <summary>
            查询所有模板
            </summary> 
            <param name="culture">多语言编码</param>
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.GetTplById(System.String,System.Boolean,System.String)">
            <summary>
            根据模板ID查询模板的数据
            </summary> 
            <param name="isCulture">是否需要多语言</param>
            <param name="tplID">任务类型ID</param>
            <param name="culture">多语言编码</param>
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.GetTplsByTaskItemID(System.String,System.String,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Nullable{System.Int32})">
            <summary>
            根据基础数据的ID查询关联的任务类型
            </summary>
            <param name="taskItemID">基础数据ID</param>
            <param name="culture">多语言编码</param> 
            <param name="mustPublished">是否只查询已经发布的</param> 
            <param name="includeCycleInfo">返回值是否包含该基础数据的周期性任务执行数据</param>
            <param name="reqApproveRight">是否只返回有审批权限的模板</param>
            <param name="reqPreviewsRight">是否返回有预览权限的模板</param>
            <param name="level">审批级别，如果参数reqApproveRight为true的时候必填，只支持1-2</param>
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.GetTaskItemCycleDatesByTplID(System.String,System.String,System.Boolean)">
            <summary>
            根据基础数据和任务类型查询周期性任务的可选日期
            </summary>
            <param name="taskItemID">基础数据ID</param>
            <param name="tplID">任务类型ID</param>
            <param name="includeFuture">是否包含未来的日期，否代表最多只能选择今天</param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.QueryTplByProject(System.String,System.Boolean)">
            <summary>
            根据项目ID查询模板的分页数据
            </summary>
            <param name="projectID">项目ID</param>  
            <param name="mustPublished">是否只查询已经发布的</param>    
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.QueryByProject(System.String,Common.DAL.Methods.PageCriteria,System.Nullable{QMD.DAL.Table.CycleType},System.Boolean,System.String)">
            <summary>
            根据项目ID查询模板的分页数据
            </summary>
            <param name="projectID">项目ID</param> 
            <param name="pageCriteria">分页查询条件</param>  
            <param name="mustPublished">是否只查询已经发布的</param>  
            <param name="cycleType">周期类型</param>
            <param name="culture">多语言编码</param>
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.QueryByServiceLine(System.String,Common.DAL.Methods.PageCriteria,System.Boolean,System.String)">
            <summary>
            根据服务线ID查询模板的分页数据
            </summary>
            <param name="serviceLineID">服务线ID</param> 
            <param name="pageCriteria">分页查询条件</param>  
            <param name="mustPublished">是否只查询已经发布的</param>  
            <param name="culture">多语言编码</param>
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.QueryTpl(System.String,System.Int32,System.String,System.String,System.String,System.String,System.String,Common.DAL.Methods.PageCriteria,System.Boolean,System.String)">
            <summary>
            根据服务线ID查询模板的分页数据
            </summary>
            <param name="searchContent">模板名称</param> 
            <param name="projectType">项目类型</param> 
            <param name="serviceLineID">服务线ID</param> 
            <param name="objTypeLevelId">对象级别的ID</param> 
            <param name="regionCode">区域编码</param> 
            <param name="projectID">项目ID</param> 
            <param name="name">名称</param>
            <param name="pageCriteria">分页查询条件</param>  
            <param name="mustPublished">是否只查询已经发布的</param>  
            <param name="culture">多语言编码</param>
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.GetTplGroups(System.String,System.Boolean)">
            <summary>
            查询模板下的所有分组
            </summary>
            <param name="tplID">模板ID</param> 
            <param name="includeHide">是否包含隐藏的分组</param>
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.GetTplSteps(System.String,System.Boolean)">
            <summary>
            查询模板下的工序模板
            </summary>
            <param name="tplID">模板ID</param> 
            <param name="includeHide">是否包含隐藏的工序</param>
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.GetTplSummary(System.String,System.Boolean)">
            <summary>
            根据模板ID同时返回模板的分组信息，工序信息和样例信息
            </summary>
            <param name="tplID">模板ID</param> 
            <param name="includeHide">是否包含隐藏的工序</param>
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.GetGroupSteps(System.String,System.Boolean)">
            <summary>
            查询分组下的工序模板
            </summary>
            <param name="groupID">分组ID</param> 
            <param name="includeHide">是否包含隐藏的工序</param>
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.IsTplPublished(System.String)">
            <summary>
            模板是否已经发布
            </summary>
            <param name="tplID">模板ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.IsTplGroupPublished(System.String)">
            <summary>
            分组是否已经发布
            </summary>
            <param name="tplID">模板ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.IsTplStepPublished(System.String)">
            <summary>
            工序是否已经发布
            </summary>
            <param name="tplID">模板ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.PublishTpl(System.String)">
            <summary>
            发布模板
            </summary>
            <param name="tplID">模板ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.PublishStep(System.String)">
            <summary>
            发布模板工序
            </summary>
            <param name="tplID">模板ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.PublishGroup(System.String)">
            <summary>
            发布模板分组
            </summary>
            <param name="tplID">模板ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.CopyTpl(QMD.Model.RenameDto)">
            <summary>
            拷贝模板，复制模板的所有配置信息，包括分组和工序配置等等
            </summary>
            <param name="req">被拷贝的模板ID和新名称，拷贝后新模板的名称，如果为Null则会在复制源的名称上后缀带上"_copy",由用户在前端修改</param>  
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.RenameTpl(QMD.Model.RenameDto)">
            <summary>
            重命名模板名称
            </summary>
            <param name="req">被拷贝的模板ID和新名称，拷贝后新模板的名称，如果为Null则会在复制源的名称上后缀带上"_copy",由用户在前端修改</param>  
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.AddOrUpdateTpl(QMD.Model.TaskTplDto)">
            <summary>
            创建或者新增模板
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.AddOrUpdateTplGroup(QMD.Model.TaskGroupTplDto)">
            <summary>
            创建或者新增分组，会自动过滤Order属性
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.AddOrUpdateTplStep(QMD.Model.TaskStepTplDto)">
            <summary>
            创建或者新增工序，会自动过滤Order属性
            </summary> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.StepMoveNext(System.String)">
            <summary>
            工序配置向后移动，自动跳过删除和隐藏的
            </summary>
            <param name="stepID">工序ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.StepMovePrevious(System.String)">
            <summary>
            工序配置向前移动，自动跳过删除和隐藏的
            </summary>
            <param name="stepID">工序ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.GroupMoveNext(System.String)">
            <summary>
            工序配置向后移动，自动跳过删除和隐藏的
            </summary>
            <param name="groupID">工序ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.GroupMovePrevious(System.String)">
            <summary>
            工序配置向前移动，自动跳过删除和隐藏的
            </summary>
            <param name="groupID">工序ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.DeleteTplGroup(System.String)">
            <summary>
            删除分组以及分组下面的工序，注意：删除的时候不会对工序重新排序，只有新增和编辑才会重新对工序排序
            </summary>
            <param name="groupID">分组ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.DeleteTpl(System.String)">
            <summary>
            删除模板以及模板下面的工序和分组，注意：删除的时候不会对工序重新排序，只有新增和编辑才会重新对工序排序
            </summary>
            <param name="tplID">模板ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.DeleteTplStep(System.String)">
            <summary>
            删除工序，注意：删除的时候不会对工序重新排序，只有新增和编辑才会重新对工序排序
            </summary>
            <param name="stepID">工序ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.DeleteTplStepsByGroupID(System.String)">
            <summary>
            删除分组下的所有工序，注意：删除的时候不会对工序重新排序，只有新增和编辑才会重新对工序排序
            </summary>
            <param name="groupID">分组ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.DeleteTplStepsByTplID(System.String)">
            <summary>
            删除模板下的所有工序，注意：删除的时候不会对工序重新排序，只有新增和编辑才会重新对工序排序
            </summary>
            <param name="tplID">模板ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.DeleteTplGroupsAndStepsByTplID(System.String)">
            <summary>
            删除模板下的所有工序分组，注意：删除的时候不会对工序重新排序，只有新增和编辑才会重新对工序排序
            </summary>
            <param name="tplID">模板ID</param> 
        </member>
        <member name="M:QMD.Web.Controllers.TaskTplController.UnpublishTpl(System.String,System.Int32,System.Boolean)">
            <summary>
            把publish置为false
            </summary>
            <param name="tplID">模板ID</param>
            <param name="type">类型，1：模板，2：分组，3：工序,4:分组工序,5:强制打回,即任务模板/分组/工序</param>
            <param name="changeAllTemp">是否直接把所有的工序和分组全部的IsTemporary属性设置为True</param>
        </member>
        <member name="T:QMD.Web.Controllers.TestController">
            <summary>
            类似以前的SiteInfo基础表，任务的目标对象基础信息相关接口
            </summary>
        </member>
        <member name="P:QMD.Web.Controllers.TestDateTimeDto.ShowTime">
            <summary>
            显示时间
            </summary>
        </member>
        <member name="T:QMD.Web.Controllers.UserLoginController">
            <summary>
            用户登录权限接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.UserLoginController.GetUserResourceAuth(System.String,System.String,System.String)">
            <summary>
            获取用户的菜单及其资源权限信息
            </summary>
            <param name="menuname"></param>
            <param name="projectID"></param>
            <param name="appScope"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.UserLoginController.GetTokenApi(System.String)">
            <summary>
            根据code获取token数据
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.UserLoginController.GetSimpleUserInfo">
            <summary>
            根据code获取token数据
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.UserLoginController.GetSubAPPAuth(System.String)">
            <summary>
            根据code获取token数据
            </summary>
        </member>
        <member name="T:QMD.Web.Controllers.UserPositionController">
            <summary>
            用户岗位管理接口
            </summary>
        </member>
        <member name="M:QMD.Web.Controllers.UserPositionController.Insert(QMD.Model.NetUserPositionDto)">
            <summary>
            新增
            </summary>
            <param name="reqDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.UserPositionController.Update(QMD.Model.NetUserPositionDto)">
            <summary>
            更新
            </summary>
            <param name="reqDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.UserPositionController.Delete(QMD.Model.NetUserPositionDto)">
            <summary>
            删除
            </summary>
            <param name="reqDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.UserPositionController.QueryList(QMD.Model.NetUserPositionReqDto,Common.DAL.Methods.PageCriteria)">
            <summary>
            查询列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.UserPositionController.Export(QMD.Model.NetUserPositionReqDto)">
            <summary>
            导出数据
            </summary>
            <param name="reqDto"></param>
            <returns></returns>
        </member>
        <member name="M:QMD.Web.Controllers.UserPositionController.Import(Microsoft.AspNetCore.Http.IFormCollection)">
            <summary>
            导入数据
            </summary>
            <param name="formInfo"></param>
            <returns></returns>
        </member>
        <member name="F:QMD.Web.TestDatas.TestConfigEnvValues._testNmpDataDtos">
            <summary>
            网管测试数据
            </summary>
        </member>
        <member name="F:QMD.Web.TestDatas.TestConfigEnvValues._testBoardDataDtos">
            <summary>
            单盘测试数据
            </summary>
        </member>
    </members>
</doc>
