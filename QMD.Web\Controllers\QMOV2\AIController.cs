using Common.DAL.Methods;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using QMD.Model;
using QMD.Service.QMOV2Service.AI;
using System;
using System.IO;
using System.Threading.Tasks;

namespace QMD.Web.Controllers.QMOV2
{
    /// <summary>
    /// AI智能填报控制器
    /// </summary>
    [Route("api/qmov2/[controller]")]
    [ApiController]
#if RELEASE
    [Authorize]
#endif
    public class AIController : ControllerBase
    {
        private readonly ILogger<AIController> _logger;
        private readonly AIServices _aiServices;

        public AIController(
            ILogger<AIController> logger,
            AIServices aiServices)
        {
            _logger = logger;
            _aiServices = aiServices;
        }

        /// <summary>
        /// AI智能填报QMO工单（同步接口，保持原有逻辑）
        /// </summary>
        /// <returns>AI解析结果</returns>
        [HttpPost("fillform")]
        public async Task<BaseRes<ReportordersDto>> AIFillForm(IFormFile file)
        {
            try
            {
                _logger.LogInformation("接收到AI智能填报请求");

                // 调用AI服务解析文件并直接返回结果
                return await _aiServices.AIFillFormAsync(file);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AI智能填报失败");
                return new BaseRes<ReportordersDto>(false, $"处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 启动AI智能解析docx文档任务（异步接口）
        /// </summary>
        /// <param name="file">要解析的docx文件</param>
        /// <returns>任务启动结果</returns>
        [HttpPost("startParseDocx")]
        public async Task<BaseRes<AIFillTaskStartResult>> StartParseDocxTask(IFormFile file)
        {
            try
            {
                _logger.LogInformation("接收到AI智能解析docx文档请求");

                return await _aiServices.StartParseDocxTaskAsync(file);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动AI智能解析docx任务失败");
                return new BaseRes<AIFillTaskStartResult>(false, $"启动解析任务失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 查询AI解析docx文档进度
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>进度信息</returns>
        [HttpGet("parseDocxProgress/{taskId}")]
        public async Task<BaseRes<AIFillProgressDto>> GetParseDocxProgress(string taskId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(taskId))
                {
                    return new BaseRes<AIFillProgressDto>(false, "任务ID不能为空");
                }

                var progress = await _aiServices.GetParseDocxProgressAsync(taskId);
                if (progress == null)
                {
                    return new BaseRes<AIFillProgressDto>(false, "任务不存在或已过期");
                }
                
                return new BaseRes<AIFillProgressDto>(progress);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"查询AI解析docx进度失败: {taskId}");
                return new BaseRes<AIFillProgressDto>(false, $"查询进度失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取AI解析docx文档结果
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>解析结果</returns>
        [HttpGet("parseDocxResult/{taskId}")]
        public async Task<BaseRes<ReportordersDto>> GetParseDocxResult(string taskId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(taskId))
                {
                    return new BaseRes<ReportordersDto>(false, "任务ID不能为空");
                }

                var result = await _aiServices.GetParseDocxResultAsync(taskId);
                if (result == null)
                {
                    return new BaseRes<ReportordersDto>(false, "任务结果不存在或已过期");
                }
                
                if (result.Success)
                {
                    return new BaseRes<ReportordersDto>(result.Data);
                }
                else
                {
                    return new BaseRes<ReportordersDto>(false, result.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取AI解析docx结果失败: {taskId}");
                return new BaseRes<ReportordersDto>(false, $"获取解析结果失败: {ex.Message}");
            }
        }
    }
} 