2025-08-13 13:36:47.6118 | 文本优化：原始长度 1566，优化后长度 1550，节省 16 个字符 (1.02%) 
2025-08-13 13:36:47.6118 | 请求体: {"model":"qwen-plus","messages":[{"role":"system","content":"你是一个专业的网络名称匹配专家，擅长将用户提供的网络名称与标准网络名称进行匹配。请尽可能找到最匹配的标准名称。"},{"role":"user","content":"请根据以下信息，匹配最合适的标准网络名称。 原始网络名称：PON网络 省份：湖北省 地市：咸宁市 专业：接入 标准网络列表（只包含本省份，包含网络名、运营商和专业信息）： [{\"NetName\":\"湖北省咸宁市移动网络接入本地网\",\"Province\":\"湖北省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"接入\",\"Operator\":\"移动\"},{\"NetName\":\"湖北省咸宁市联通OTN本地网\",\"Province\":\"湖北省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"OTN\",\"Operator\":\"联通\"},{\"NetName\":\"湖北省咸宁市移动PTN本地网\",\"Province\":\"湖北省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"PTN\",\"Operator\":\"移动\"},{\"NetName\":\"湖北省咸宁市电信接入本地网\",\"Province\":\"湖北省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"接入\",\"Operator\":\"电信\"},{\"NetName\":\"湖北省咸宁市移动OTN本地网\",\"Province\":\"湖北省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"OTN\",\"Operator\":\"移动\"},{\"NetName\":\"湖北省咸宁市移动MSTP本地网\",\"Province\":\"湖北省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"MSTP\",\"Operator\":\"移动\"},{\"NetName\":\"湖北省咸宁市电信OTN本地网\",\"Province\":\"湖北省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"OTN\",\"Operator\":\"电信\"},{\"NetName\":\"湖北省咸宁市电信MSTP本地网\",\"Province\":\"湖北省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"MSTP\",\"Operator\":\"电信\"},{\"NetName\":\"湖北省咸宁市移动SPN本地网\",\"Province\":\"湖北省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"SPN\",\"Operator\":\"移动\"},{\"NetName\":\"湖北省咸宁市高速交换机路由器本地网湖北联投鄂州到咸宁高速公路项目网\",\"Province\":\"湖北省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"交换机路由器\",\"Operator\":\"行网\"},{\"NetName\":\"湖北省咸宁市电力MSTP本地网\",\"Province\":\"湖北省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"MSTP\",\"Operator\":\"行网\"}] 可选专业列表： [\"接入\",\"OTN\",\"PTN\",\"MSTP\",\"SPN\",\"交换机路由器\"] 可选运营商列表： [\"移动\",\"联通\",\"电信\",\"行网\"] 匹配规则： 1. 只在指定省份和地市下查找。 2. 优先精确匹配网络名称。 3. 如果网络名称不匹配： - 如果网络名称中不包含专业信息，则使用传入的专业参数。 - 如果网络名称中包含专业信息，则使用网络名称中的专业，再结合标准网络中的专业、运营商和省市名进行辅助判断。 4. 返回最匹配的标准网络名称、运营商和专业信息，以及置信度（0-100）。 返回格式： { \"standardNetworkName\": \"\", \"matchConfidence\": 100 }"}],"temperature":0.7,"stream":false} 
2025-08-13 13:36:47.6269 | 请求URL: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-13 13:36:47.6269 | Start processing HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-13 13:36:47.6269 | Sending HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-13 13:36:49.1171 | Received HTTP response headers after 1480.9859ms - 200 
2025-08-13 13:36:49.1171 | End processing HTTP request after 1489.5024ms - 200 
2025-08-13 13:36:49.1241 | API响应: {"choices":[{"message":{"role":"assistant","content":"{\n  \"standardNetworkName\": \"湖北省咸宁市移动网络接入本地网\",\n  \"matchConfidence\": 95\n}"},"finish_reason":"stop","index":0,"logprobs":null}],"object":"chat.completion","usage":{"prompt_tokens":649,"completion_tokens":29,"total_tokens":678,"prompt_tokens_details":{"cached_tokens":0}},"created":1755063408,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-a3f31d0d-0b41-9af9-8c0d-fc32e8320cac"} 
2025-08-13 13:36:50.8849 | AI匹配标准网络名称完成: 湖北省咸宁市移动网络接入本地网，匹配置信度: 95% 
2025-08-13 13:36:51.0898 | 开始提取详细信息，工单类型: 割接 
2025-08-13 13:36:51.0953 | 文本优化：原始长度 6280，优化后长度 5819，节省 461 个字符 (7.34%) 
2025-08-13 13:36:51.0953 | 请求体: {"model":"qwen-plus","messages":[{"role":"system","content":"你是一个专业的QMO工单解析助手，擅长从文档中提取详细的工单信息，包括风险级别、计划时间、任务内容等。请按照指定的JSON格式返回结果。"},{"role":"user","content":"请根据以下文档内容，提取QMO工单报备所需的详细信息。 基础信息： - 工单类型：非升级 请从文档中提取以下信息： 1. 风险级别 必须严格从以下标准省份列表中匹配：一级(紧急),一级,二级(紧急),二级,三级(紧急),三级,四级(紧急),四级 2. 是否重保 文档中无相关字段 - bool类型 - 直接赋值false 3. 产品线附加信息 4. 计划开始时间 从文档中的【操作步骤】里进行提取： - 从操作步骤时间段中提取开始时间，并减去2个小时。 - 格式为YYYY-MM-DD HH:MM:SS。 5. 计划结束时间 从文档中的【操作步骤】里进行提取： - 从操作步骤时间段中提取结束时间，并加上2个小时。 - 格式为YYYY-MM-DD HH:MM:SS。 6. 工单主题 工单主题 -工单主题一般出现在内容开头部分，用来描述整个文档的具体内容。 -例如：7月11日0：00-04：00浙江绍兴网络2LN4、1LN4单盘升级操作网络变更操作技术方案-OTN-V3.1（此例不能作为最后的提取内容） 7. 抄送人 文档中无相关字段 - 直接赋值空数组 8. 网管信息集合 据文档中的版本信息，网管版本信息表格提取 - 仅提取网管系列、网管版本和网管补丁。 - 网管系列应包含网管系统的名称，主版本即为网管版本，补丁信息为网管补丁。 **注意**：在提取时需关注相近词汇的识别，以确保信息的准确性。 9. 设备信息集合 据文档中的版本信息，设备版本信息表格提取 - 设备型号(或者类型) - 板卡名称 - 板卡物料号（注意：硬件版本即为板卡物料号） - 软件版本 - 软件编译日期（格式为YYYY-MM-DD） **注意**：有些表格中，软件版本和编译日期可能写在一起，需要进行分离，在提取时需关注相近词汇的识别，以确保信息的准确性。如果没有匹配的信息，就置为空 10. 变更工作量集合 据文档中的操作对象，进行提取 **实施对象**： - 即表格中的设备类型。 **本次实施数量**： - 即表格中的数量，数字格式。 **注意**：在提取时需关注相近词汇的识别，以确保信息的准确性。 11. 默认客户信息 **注意**：根据文档中的【变更操作准备】-【人员安排】-【客户变更小组名单】部分的表格进行提取（不要从客户审核的表格中查找）： - 查找包含\"姓名\"、\"电话\"、\"职责\"等列的客户人员表格 - 选取表格中的第一行客户数据（排除表头） - 提取该客户的姓名、电话、邮箱信息 - 如果表格中没有邮箱列或邮箱为空，则邮箱字段设为空字符串 - 如果电话（或者相近的字段，比如手机号，联系方式等）为空，则电话字段设为空字符串 - 绝对不能编造任何数据 - 返回格式：{\"email\":\"客户邮箱或空\",\"nickName\":\"客户姓名\",\"phoneNumber\":\"客户电话或空\"} 12. 默认客户邮箱 **注意**：从第11项提取的默认客户信息中获取邮箱字段，如果没有邮箱信息则返回空字符串（结果和11项中的email结果保持一致）。 13. 默认客户电话 **注意**：从第11项提取的默认客户信息中获取电话字段，如果没有电话信息则返回空字符串（结果和11项中的phoneNumber结果保持一致）。 14. 实施人信息（支持多个） 据文档中的操作对象，烽火现场实施小组名单中表格进行提取，当职责为实施负责人，负责本次操作实施 **姓名**： - 即实施负责人姓名。 **电话**： - 即实施负责人电话。 15. 审核人信息（支持多个） 据文档中的操作对象，烽火现场实施小组名单中表格进行提取，当职责为负责审核实施操作步骤过程 **姓名**： - 即审核人姓名。 **电话**： - 即审核人电话。 16. 工单内容 根据文档中的内容，总结该文档的目的和原因 17. 所有客户信息 **注意**：根据文档中的 人员安排-客户变更小组名单 下方的第一个表， 这个表中所有的客户信息（客户姓名，客户邮箱，客户电话）。 - 是一个数组对象，格式如下[{\"invNickName\":\"\",\"invPhone\":,\"invEmail\":\"\"}] 文档内容： 网络变更操作技术方案-PON 咸宁移动运营商PON网络 硬件更换/硬件改造/软件升级/业务割接方案 湖北省技服中心 时间：2025年8月7日 对3级及以上风险要求提供变更操作方案，4级风险由本地技服根据实情自行要求。 所有项目不允许裁剪，当本次操作不涉及时，可以描述为不涉及。 三级及以上风险网络变更需要到技服技术支撑部TAC中心审核，需提前24小时报备；二级风险网络变更需要到产出线工程部审核，需提前48小时报备；一级风险网络变更需要到研发LMT审核，需提前72小时报备。同时，设置有三级（紧急）、二级（紧急）、一级（紧急）三个风险等级，此等级报备无需时间限制。对于一、二、三级网络变更操作和特殊时期（例如：集采期、投标、重大保障期、高访、集团网络巡检等）所有等级操作均需报备销售认可。 销售审核意见： 省技服审核意见: 十、变更操作概述 操作简述 在此描述实施本次操作的类型、原因和主要内容简述，割接实施窗口时间。 操作类型：E类割接。 操作原因： ONU调整PON口&OLT上联改造 内容简述：PON口迁移 割接时间：8月13日 1：00-06：00 影响评估 风险等级：三级（参考风险分级标准确定，并和OA报备工单一致） 影响范围：本次变更操作，预期会影响各类业务多少量。详细影响范围如下： 2台OLT 64个PON口 风险评估（重点） 操作对象 版本信息 软件版本信息 成功标准 成功标准： 业务确认正常 十一、变更操作准备 人员安排 客户变更小组名单 烽火现场小组名单【注：对外提供时，属性列去除】 烽火远程支撑保障名单 时间窗口 授权操作时间窗口 内部控制的操作时间窗口【注：对外该部分可以去除】 普通操作预留检查时间2小时，正常可操作时间1：00-4：00 失控风险控制时间2小时，出现不可控风险，启动应急预案时间为4：00 其他准备 软件准备 备份 涉及OLT变更的，需导出并备份OLT配置文件 涉及网管变更的，需完整备份网管文件 备件 网络变更涉及到的单盘均需有相应的备件 远程接入 提前搭建远程环境，便于远程支撑时 账号检测 网管变更操作申请管理员权限账号 割接前设备运行情况检查 如存在影响割接的异常，需和用户沟通确定下一步计划； 十二、操作实施步骤（重点） 设备操作步骤 报备人按照下面整体时间控制节点补充详细的操作步骤 在此详细描述实施本次操作的具体步骤，要求包括操作内容和时间预估。 网管变更操作 不涉及 验证方案 验证： 确认mac地址，vlan可以在OLTmac地址表学到. 状态查询： 确认端口状态正常，有流量 客户确认 与客户确认，确保业务均正常才能宣告离场 应急预案（重点） 启动条件： 当满足以下任一条件时，要求必须启动应急预案： 在操作窗口结束前2小时，如X点整（请根据实际窗口时间确定），出现操作严重滞后，操作预计无法完成。 操作过程中，任何时候出现本次操作引发的、不在预期范围内的设备故障。 严重影响客户感知且客户要求必须启动应急预案。 预案： 硬件故障，可尝试更换；软件问题，可尝试回退至原有版本。如无法独立解决应及时联系总部保障人员处理。 销售审核人 审核意见 戢鹏 通过 省技服人员 审核人 审核意见 网络负责人 李晓杰 通过 技术主管 王博 通过 序号 风险 可能性 影响程度 应对措施 1 割接后，业务异常 低 高 倒回原有系统 设备类型 数量 AN5516-01 2 AN5516-04 0 AN5516-06 0 AN6000-17 0 设备类型 板卡名 硬件版本 软件版本（编译时间） 目标版本（编译时间） AN5516-01 不涉及 不涉及 AN600-17 不涉及 不涉及 UNM2000 不涉及 不涉及 …… 姓名 电话 职责 罗剑 15872044578 总指挥 罗剑 15872044578 信息确认人 罗剑 15872044578 现场操作人 …… 姓名 电话 职责 部门 属性 徐子晨 15972506040 割接总指挥，对割接结果负责 武汉技服 合作方 徐子晨 15972506040 实施负责人，负责组织本次操作实施 武汉技服 合作方 黄榕 18371512306 技术支撑 武汉技服 合作方 徐子晨 15972506040 负责完成具体操作。 武汉技服 合作方 姓名 电话 职责 部门 王博 18986203215 技术接口人 湖北技服 时间 工作内容 技术责任人 8月13日1：00-6：00 PON口数据制作 徐子晨 软件类型 版本 原版本（用于倒回） 不涉及 新版本 不涉及 类型 说明 接入方案 方案1：向日葵； 序号 检查项目 关键动作 检查标准 是否正常 1 设备各单盘与主控通信是否正常 Ping 各单盘的私有地址 无丢包 是 2 网管各服务状态正常 查看服务进程 服务均已启动 是 3 告警核查 导出告警 生成告警导出文件 是 序号 时间段 操作内容 预期结果 风险应对 责任人 1 0:00-0:30 割接操作前准备备份，告警屏蔽 徐子晨 2 0:30-4:00 开始进行相关操作 徐子晨 3 4:00-4:20 核查业务 正常运行 无风险 徐子晨 4 4:20-6:00 客户验证，宣告离场 徐子晨 步骤 操作内容 1 回退到原本配置 2 …… 必选 倒回后测试：按照操作后测试进行测试验证网络各项功能恢复 必选 倒回后确认：按照确认方案进行准确确认 ### 所有提取的内容不能自己编造，只能从文档中提取，宁缺勿错的原则！ ### 请严格按照以下JSON格式返回提取结果,JSON里的举例不能作为结果返回，如果没有置为空(注意，格式里的具体内容和文档无关，请你只提取文档里的内容)： { \"riskLevel\": \"一级\", // 1.风险级别 \"isReinstated\": false, // 2.是否重保 \"lineRemark\": \"产品附加信息\", // 3.产品附加信息 \"planStartTime\": \"2025-07-14 10:46:10\", // 4.计划开始时间 \"planEndTime\": \"2025-08-13 10:46:14\", // 5.计划结束时间 \"title\": \"工单主题\", // 6.工单主题 \"copyUsersList\":[ {\"id\":null,\"userType\":1,\"invUserName\":\"<EMAIL>\",\"invNickName\":\"肖绍谦\",\"invPhone\":\"18627827716\",\"invEmail\":\"<EMAIL>\",\"invPostCode\":4,\"invPostName\":\"软件工程师\",\"invDept\":\"武汉烽火技术服务有限公司-数字技术部\",\"invUserLevel\":null} ], // 7.抄送人列表（绝对不能使用示例数据或者捏造数据） \"involveNmpinfos\": [{\"id\":null,\"orderKind\":3,\"nmpSeries\":\"网管系列（当前版本）\",\"nmpVersion\":\"网管版本（当前版本）\",\"nmpPatches\":\"网管补丁（当前版本）\",\"tarNmpSeries\":\"网管系列（目标版本）\",\"tarNmpVersion\":\"网管版本（目标版本）\",\"tarNmpPatch\":\"网管补丁（目标版本）\"}], // 8.网管信息集合（绝对不能使用示例数据或者捏造数据） \"involveDevices\": [{\"id\":null,\"orderKind\":3,\"invDeviceType\":\"对象-设备型号\",\"invBoardName\":\"对象-板卡名称\",\"invHardVersion\":\"对象-板卡物料号\",\"invSoftVersion\":\"软件版本-当前\",\"compileTime\":\"软件编译日期-当前版本\",\"tarInvSoftVersion\":\"软件版本-目标版本\",\"tarCompileTime\":\"软件编译日期-目标版本\"}], // 9.设备信息集合（绝对不能使用示例数据或者捏造数据） \"involveProducts\": [{\"id\":null,\"orderKind\":3,\"objectCount\":12,\"objectName\":\"实施对象\"}], // 10.变更工作量集合（绝对不能使用示例数据或者捏造数据） \"customName\": {\"email\":\"<EMAIL>\",\"nickName\":\"xiaoshaoqian\",\"phoneNumber\":\"12324567131\"}, // 11.默认客户信息（数据不能捏造和使用示例数据） \"customEmail\": \"<EMAIL>\", // 12.客户邮箱（结果和11项中的email字段的结果完全保持一致） \"customPhone\": \"12324567131\", // 13.客户电话（结果和11项中的phoneNumber字段的结果完全保持一致） \"taskUsers\": [{\"nickName\":\"朱浩\",\"userPhone\":\"13787031546\"}], // 14.实施人信息（绝对不能使用示例数据或者捏造数据） \"checkUsers\": [{\"nickName\":\"肖绍谦\",\"userPhone\":\"18627827716\"}], // 15.审核人（绝对不能使用示例数据或者捏造数据） \"taskContent\": \"\", // 16.任务内容 \"customUsers\":[{\"invNickName\":\"肖绍谦\",\"invPhone\":12324567131,\"invEmail\":<EMAIL>\"}] //17.所有客户信息（绝对不能使用示例数据或者捏造数据） }"}],"temperature":0.7,"stream":false} 
2025-08-13 13:36:51.1645 | 请求URL: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-13 13:36:51.1775 | Start processing HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-13 13:36:51.1775 | Sending HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-13 13:37:07.2503 | Received HTTP response headers after 16066.5612ms - 200 
2025-08-13 13:37:07.2503 | End processing HTTP request after 16084.1713ms - 200 
2025-08-13 13:37:07.2653 | API响应: {"choices":[{"message":{"role":"assistant","content":"```json\n{\n  \"riskLevel\": \"三级\",\n  \"isReinstated\": false,\n  \"lineRemark\": \"\",\n  \"planStartTime\": \"2025-08-12 23:00:00\",\n  \"planEndTime\": \"2025-08-13 08:00:00\",\n  \"title\": \"网络变更操作技术方案-PON\",\n  \"copyUsersList\": [],\n  \"involveNmpinfos\": [\n    {\n      \"nmpSeries\": \"UNM2000\",\n      \"nmpVersion\": \"不涉及\",\n      \"nmpPatches\": \"不涉及\"\n    }\n  ],\n  \"involveDevices\": [\n    {\n      \"invDeviceType\": \"AN5516-01\",\n      \"invBoardName\": \"不涉及\",\n      \"invHardVersion\": \"不涉及\",\n      \"invSoftVersion\": \"不涉及\",\n      \"compileTime\": \"\",\n      \"tarInvSoftVersion\": \"不涉及\",\n      \"tarCompileTime\": \"\"\n    },\n    {\n      \"invDeviceType\": \"AN600-17\",\n      \"invBoardName\": \"不涉及\",\n      \"invHardVersion\": \"不涉及\",\n      \"invSoftVersion\": \"不涉及\",\n      \"compileTime\": \"\",\n      \"tarInvSoftVersion\": \"不涉及\",\n      \"tarCompileTime\": \"\"\n    }\n  ],\n  \"involveProducts\": [\n    {\n      \"objectName\": \"AN5516-01\",\n      \"objectCount\": 2\n    },\n    {\n      \"objectName\": \"AN5516-04\",\n      \"objectCount\": 0\n    },\n    {\n      \"objectName\": \"AN5516-06\",\n      \"objectCount\": 0\n    },\n    {\n      \"objectName\": \"AN6000-17\",\n      \"objectCount\": 0\n    }\n  ],\n  \"customName\": {\n    \"nickName\": \"罗剑\",\n    \"phoneNumber\": \"15872044578\",\n    \"email\": \"\"\n  },\n  \"customEmail\": \"\",\n  \"customPhone\": \"15872044578\",\n  \"taskUsers\": [\n    {\n      \"nickName\": \"徐子晨\",\n      \"userPhone\": \"15972506040\"\n    }\n  ],\n  \"checkUsers\": [\n    {\n      \"nickName\": \"王博\",\n      \"userPhone\": \"18986203215\"\n    }\n  ],\n  \"taskContent\": \"本次网络变更操作主要涉及ONU调整PON口和OLT上联改造，操作类型为E类割接。操作时间为8月13日1:00-6:00，预计影响2台OLT设备的64个PON口。\",\n  \"customUsers\": [\n    {\n      \"invNickName\": \"罗剑\",\n      \"invPhone\": \"15872044578\",\n      \"invEmail\": \"\"\n    }\n  ]\n}\n```"},"finish_reason":"stop","index":0,"logprobs":null}],"object":"chat.completion","usage":{"prompt_tokens":3660,"completion_tokens":675,"total_tokens":4335,"prompt_tokens_details":{"cached_tokens":0}},"created":1755063426,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-fdaa88d6-80e8-90d6-9b56-07675880726c"} 
2025-08-13 13:37:16.1283 | 提取详细信息完成，工单标题: 网络变更操作技术方案-PON 
2025-08-13 13:37:16.1283 | AI智能填报QMO工单完成，工单类型: 割接 
2025-08-13 13:40:00.2806 | [2025-08-13 13:40:00]定时任务SyncReadEmailJob执行失败：False 
2025-08-13 13:46:21.7765 | Request:/api/qmov2/ai/fillform 
2025-08-13 13:46:23.7913 | 接收到AI智能填报请求 
2025-08-13 13:46:25.7489 | 开始AI智能填报QMO工单，文件名: 8月13日-9月12日1：00-06：00咸宁移动PON口迁移OLT上联改造操作网络变更操作技术方案.docx 
2025-08-13 13:46:35.5162 | 开始从docx文件提取文本: C:\Users\<USER>\AppData\Local\Temp\QMDAITemp\8e7d8d66-2227-4f0d-9a71-cf08649b6ca7.docx 
2025-08-13 14:47:18.3813 | Initialized Scheduler Signaller of type: Quartz.Core.SchedulerSignalerImpl 
2025-08-13 14:47:18.3995 | Quartz Scheduler created 
2025-08-13 14:47:18.3995 | Registering Quartz Job Initialization Plug-in. 
2025-08-13 14:47:18.3995 | RAMJobStore initialized. 
2025-08-13 14:47:18.4055 | Scheduler meta-data: Quartz Scheduler (v*******) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'Quartz.Core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'Quartz.Simpl.DefaultThreadPool' - with 10 threads.
  Using job-store 'Quartz.Simpl.RAMJobStore' - which does not support persistence. and is not clustered.
 
2025-08-13 14:47:18.4055 | Quartz scheduler 'QuartzScheduler' initialized 
2025-08-13 14:47:18.4055 | Quartz scheduler version: ******* 
2025-08-13 14:47:18.4055 | JobFactory set to: QMD.Job.JobFactory 
2025-08-13 14:47:18.4055 | Parsing XML file: D:\Code\nmosphub\QMD.Web\bin\Debug\net5.0\quartz_jobs.xml with systemId: ~/quartz_jobs.xml 
2025-08-13 14:47:18.5770 | Adding 20 jobs, 20 triggers. 
2025-08-13 14:47:18.5823 | Adding job: qmd_qa_jobgroup.syncQANetJob 
2025-08-13 14:47:18.5952 | Adding job: qmd_qa_jobgroup.SyncQaDashBoardRedisJob 
2025-08-13 14:47:18.5952 | Adding job: qmd_qa_jobgroup.syncNetInspectItemJob 
2025-08-13 14:47:18.5952 | Adding job: qmd_why_jobgroup.syncNetproviderJob 
2025-08-13 14:47:18.5952 | Adding job: qmd_itr_jobgroup.syncItrSolutionJob 
2025-08-13 14:47:18.5952 | Adding job: qmd_itr_jobgroup.syncItrOrderTaskJob 
2025-08-13 14:47:18.5952 | Adding job: qmd_itr_jobgroup.syncItrOrderInfoJob 
2025-08-13 14:47:18.5952 | Adding job: cycle_group.cycleRecordJob 
2025-08-13 14:47:18.5952 | Adding job: qmd_itr_jobgroup.syncDingtalkUserJob 
2025-08-13 14:47:18.5952 | Adding job: qmd_itr_jobgroup.syncItrProductInfoJob 
2025-08-13 14:47:18.5952 | Adding job: qmd_itr_jobgroup.syncNoticeRecordJob 
2025-08-13 14:47:18.5952 | Adding job: qmd_itr_jobgroup.syncNmospUserInfoJob 
2025-08-13 14:47:18.5952 | Adding job: qmd_itr_jobgroup.syncReadEmailJob 
2025-08-13 14:47:18.6097 | Adding job: qmd_qmo_jobgroup.syncNoticeTaskAdventJob 
2025-08-13 14:47:18.6097 | Adding job: qmd_qmo_jobgroup.syncNoticeTaskResultJob 
2025-08-13 14:47:18.6097 | Adding job: qmd_qmo_jobgroup.syncNoticeTaskTodoJob 
2025-08-13 14:47:18.6097 | Adding job: qmd_qmo_jobgroup.syncExpireOrderJob 
2025-08-13 14:47:18.6097 | Adding job: qmd_qmo_jobgroup.syncExpireTaskJob 
2025-08-13 14:47:18.6097 | Adding job: qmd_qmo_jobgroup.escalatedTaskRemindJob 
2025-08-13 14:47:18.6097 | Adding job: qmd_qmo_jobgroup.syncNoticeDisOrderJob 
2025-08-13 14:47:18.6097 | Scheduler QuartzScheduler_$_NON_CLUSTERED started. 
2025-08-13 14:47:18.9394 | Loading proxy data from config. 
2025-08-13 14:47:20.1952 | [2025-08-13 14:47:20]定时任务SyncReadEmailJob执行失败：False 
2025-08-13 14:47:35.2052 | Request:/api/qmov2/ai/validatetemplatefilesasync 
2025-08-13 14:47:38.9085 | 开始验证模板文件分割标识符 
2025-08-13 14:47:47.6057 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-传输网管-V3.0-20240802.docx 
2025-08-13 14:47:48.8883 | 开始从docx文件提取文本: D:\Code\nmosphub\QMD.Web\bin\Debug\net5.0\AI\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-传输网管-V3.0-20240802.docx 
2025-08-13 14:48:54.7436 | Initialized Scheduler Signaller of type: Quartz.Core.SchedulerSignalerImpl 
2025-08-13 14:48:54.7587 | Quartz Scheduler created 
2025-08-13 14:48:54.7587 | Registering Quartz Job Initialization Plug-in. 
2025-08-13 14:48:54.7587 | RAMJobStore initialized. 
2025-08-13 14:48:54.7648 | Scheduler meta-data: Quartz Scheduler (v*******) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'Quartz.Core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'Quartz.Simpl.DefaultThreadPool' - with 10 threads.
  Using job-store 'Quartz.Simpl.RAMJobStore' - which does not support persistence. and is not clustered.
 
2025-08-13 14:48:54.7648 | Quartz scheduler 'QuartzScheduler' initialized 
2025-08-13 14:48:54.7648 | Quartz scheduler version: ******* 
2025-08-13 14:48:54.7648 | JobFactory set to: QMD.Job.JobFactory 
2025-08-13 14:48:54.7820 | Parsing XML file: D:\Code\nmosphub\QMD.Web\bin\Debug\net5.0\quartz_jobs.xml with systemId: ~/quartz_jobs.xml 
2025-08-13 14:48:54.9287 | Adding 20 jobs, 20 triggers. 
2025-08-13 14:48:54.9287 | Adding job: qmd_qa_jobgroup.syncQANetJob 
2025-08-13 14:48:54.9470 | Adding job: qmd_qa_jobgroup.SyncQaDashBoardRedisJob 
2025-08-13 14:48:54.9470 | Adding job: qmd_qa_jobgroup.syncNetInspectItemJob 
2025-08-13 14:48:54.9470 | Adding job: qmd_why_jobgroup.syncNetproviderJob 
2025-08-13 14:48:54.9470 | Adding job: qmd_itr_jobgroup.syncItrSolutionJob 
2025-08-13 14:48:54.9470 | Adding job: qmd_itr_jobgroup.syncItrOrderTaskJob 
2025-08-13 14:48:54.9470 | Adding job: qmd_itr_jobgroup.syncItrOrderInfoJob 
2025-08-13 14:48:54.9470 | Adding job: cycle_group.cycleRecordJob 
2025-08-13 14:48:54.9470 | Adding job: qmd_itr_jobgroup.syncDingtalkUserJob 
2025-08-13 14:48:54.9516 | Adding job: qmd_itr_jobgroup.syncItrProductInfoJob 
2025-08-13 14:48:54.9516 | Adding job: qmd_itr_jobgroup.syncNoticeRecordJob 
2025-08-13 14:48:54.9516 | Adding job: qmd_itr_jobgroup.syncNmospUserInfoJob 
2025-08-13 14:48:54.9516 | Adding job: qmd_itr_jobgroup.syncReadEmailJob 
2025-08-13 14:48:54.9516 | Adding job: qmd_qmo_jobgroup.syncNoticeTaskAdventJob 
2025-08-13 14:48:54.9516 | Adding job: qmd_qmo_jobgroup.syncNoticeTaskResultJob 
2025-08-13 14:48:54.9516 | Adding job: qmd_qmo_jobgroup.syncNoticeTaskTodoJob 
2025-08-13 14:48:54.9516 | Adding job: qmd_qmo_jobgroup.syncExpireOrderJob 
2025-08-13 14:48:54.9516 | Adding job: qmd_qmo_jobgroup.syncExpireTaskJob 
2025-08-13 14:48:54.9675 | Adding job: qmd_qmo_jobgroup.escalatedTaskRemindJob 
2025-08-13 14:48:54.9675 | Adding job: qmd_qmo_jobgroup.syncNoticeDisOrderJob 
2025-08-13 14:48:54.9675 | Scheduler QuartzScheduler_$_NON_CLUSTERED started. 
2025-08-13 14:48:55.3215 | Loading proxy data from config. 
2025-08-13 14:48:56.4562 | [2025-08-13 14:48:56]定时任务SyncReadEmailJob执行失败：False 
2025-08-13 14:48:58.7285 | Request:/api/qmov2/ai/validatetemplatefilesasync 
2025-08-13 14:49:07.0108 | 开始验证模板文件分割标识符 
2025-08-13 14:49:12.6843 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-传输网管-V3.0-20240802.docx 
2025-08-13 14:49:13.3805 | 开始从docx文件提取文本: D:\Code\nmosphub\QMD.Web\bin\Debug\net5.0\AI\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-传输网管-V3.0-20240802.docx 
2025-08-13 14:51:18.1193 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-传输网管-V3.0-20240802.docx 
2025-08-13 14:51:18.5526 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-传输网管-V3.0-20240802.docx 
2025-08-13 14:51:18.6350 | [2025-08-13 14:51:18]定时任务SyncReadEmailJob执行失败：False 
2025-08-13 14:52:21.6535 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-传输网管-V3.0-20240802.docx 
2025-08-13 14:52:25.1555 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-传输网管-V3.0-20240802.docx 
2025-08-13 14:53:00.6726 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-服务器存储-V3.0-20240802.docx 
2025-08-13 14:53:02.3601 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-服务器存储-V3.0-20240802.docx 
2025-08-13 14:53:39.7953 | 从docx文件提取文本完成，文本长度: 3183 
2025-08-13 14:53:39.8394 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-路由器交换机-V3.0-20240802 (1).docx 
2025-08-13 14:53:39.8423 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-路由器交换机-V3.0-20240802 (1).docx 
2025-08-13 14:53:39.8931 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-终端-V3.0-20240802 (1).docx 
2025-08-13 14:53:39.8931 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-终端-V3.0-20240802 (1).docx 
2025-08-13 14:53:39.9367 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-CDN-V3.0-20240802 (1).docx 
2025-08-13 14:53:39.9367 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-CDN-V3.0-20240802 (1).docx 
2025-08-13 14:53:39.9831 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-IPRAN-V3.2-20250527.docx 
2025-08-13 14:53:39.9831 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-IPRAN-V3.2-20250527.docx 
2025-08-13 14:53:40.1542 | 从docx文件提取文本完成，文本长度: 7552 
2025-08-13 14:53:40.2643 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PON-V3.0-20240802.docx 
2025-08-13 14:53:40.2643 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PON-V3.0-20240802.docx 
2025-08-13 14:53:40.3044 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PTN&SPN-V3.0-20240802.docx 
2025-08-13 14:53:40.3044 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PTN&SPN-V3.0-20240802.docx 
2025-08-13 14:53:40.3485 | 正在验证模板: XX月XX日0：00-04：00XX网络XX操作网络变更操作技术方案（模板）-OTN-V3.0-20240802.docx 
2025-08-13 14:53:40.3485 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-04：00XX网络XX操作网络变更操作技术方案（模板）-OTN-V3.0-20240802.docx 
2025-08-13 14:55:42.9334 | Request:/api/qmov2/ai/validatetemplatefilesasync 
2025-08-13 14:55:48.0405 | 开始验证模板文件分割标识符 
2025-08-13 14:55:49.4089 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-传输网管-V3.0-20240802.docx 
2025-08-13 14:55:49.4089 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-传输网管-V3.0-20240802.docx 
2025-08-13 14:55:49.4523 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-服务器存储-V3.0-20240802.docx 
2025-08-13 14:55:49.4523 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-服务器存储-V3.0-20240802.docx 
2025-08-13 14:55:49.4871 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-路由器交换机-V3.0-20240802 (1).docx 
2025-08-13 14:55:49.4871 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-路由器交换机-V3.0-20240802 (1).docx 
2025-08-13 14:55:49.5263 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-终端-V3.0-20240802 (1).docx 
2025-08-13 14:55:49.5312 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-终端-V3.0-20240802 (1).docx 
2025-08-13 14:55:49.5635 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-CDN-V3.0-20240802 (1).docx 
2025-08-13 14:55:49.5635 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-CDN-V3.0-20240802 (1).docx 
2025-08-13 14:55:49.6071 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-IPRAN-V3.2-20250527.docx 
2025-08-13 14:55:49.6071 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-IPRAN-V3.2-20250527.docx 
2025-08-13 14:55:49.6558 | 从docx文件提取文本完成，文本长度: 7552 
2025-08-13 14:55:49.6896 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PON-V3.0-20240802.docx 
2025-08-13 14:55:49.6896 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PON-V3.0-20240802.docx 
2025-08-13 14:55:49.7214 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PTN&SPN-V3.0-20240802.docx 
2025-08-13 14:55:49.7214 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PTN&SPN-V3.0-20240802.docx 
2025-08-13 14:55:49.7558 | 正在验证模板: XX月XX日0：00-04：00XX网络XX操作网络变更操作技术方案（模板）-OTN-V3.0-20240802.docx 
2025-08-13 14:55:49.7558 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-04：00XX网络XX操作网络变更操作技术方案（模板）-OTN-V3.0-20240802.docx 
2025-08-13 14:55:49.7960 | 模板验证完成，结果已保存到: D:\Tempfiles\模板验证结果.txt 
2025-08-13 15:00:00.2814 | [2025-08-13 15:00:00]定时任务SyncReadEmailJob执行失败：False 
2025-08-13 15:03:35.8290 | Request:/api/qmov2/ai/validatetemplatefilesasync 
2025-08-13 15:03:53.5587 | 开始验证模板文件分割标识符 
2025-08-13 15:04:01.5025 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-传输网管-V3.0-20240802.docx 
2025-08-13 15:04:05.6902 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-传输网管-V3.0-20240802.docx 
2025-08-13 15:13:03.4908 | [2025-08-13 15:13:03]定时任务SyncReadEmailJob执行失败：False 
2025-08-13 15:13:04.2018 | Spire.Doc解析成功: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-传输网管-V3.0-20240802.docx 
2025-08-13 15:13:04.2018 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-服务器存储-V3.0-20240802.docx 
2025-08-13 15:13:04.2018 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-服务器存储-V3.0-20240802.docx 
2025-08-13 15:13:04.3533 | Spire.Doc解析成功: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-服务器存储-V3.0-20240802.docx 
2025-08-13 15:13:04.3533 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-路由器交换机-V3.0-20240802 (1).docx 
2025-08-13 15:13:04.3604 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-路由器交换机-V3.0-20240802 (1).docx 
2025-08-13 15:13:04.5618 | Spire.Doc解析成功: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-路由器交换机-V3.0-20240802 (1).docx 
2025-08-13 15:13:04.5618 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-终端-V3.0-20240802 (1).docx 
2025-08-13 15:13:04.5618 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-终端-V3.0-20240802 (1).docx 
2025-08-13 15:13:04.7502 | Spire.Doc解析成功: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-终端-V3.0-20240802 (1).docx 
2025-08-13 15:13:04.7502 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-CDN-V3.0-20240802 (1).docx 
2025-08-13 15:13:04.7502 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-CDN-V3.0-20240802 (1).docx 
2025-08-13 15:13:04.9202 | Spire.Doc解析成功: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-CDN-V3.0-20240802 (1).docx 
2025-08-13 15:13:04.9202 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-IPRAN-V3.2-20250527.docx 
2025-08-13 15:13:04.9202 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-IPRAN-V3.2-20250527.docx 
2025-08-13 15:13:04.9738 | 从docx文件提取文本完成，文本长度: 7552 
2025-08-13 15:13:05.0218 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PON-V3.0-20240802.docx 
2025-08-13 15:13:05.0218 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PON-V3.0-20240802.docx 
2025-08-13 15:13:05.1950 | Spire.Doc解析成功: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PON-V3.0-20240802.docx 
2025-08-13 15:13:05.1950 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PTN&SPN-V3.0-20240802.docx 
2025-08-13 15:13:05.2020 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PTN&SPN-V3.0-20240802.docx 
2025-08-13 15:13:05.3659 | Spire.Doc解析成功: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PTN&SPN-V3.0-20240802.docx 
2025-08-13 15:13:05.3659 | 正在验证模板: XX月XX日0：00-04：00XX网络XX操作网络变更操作技术方案（模板）-OTN-V3.0-20240802.docx 
2025-08-13 15:13:05.3659 | 开始从docx文件提取文本: D:\Tempfiles\XX月XX日0：00-04：00XX网络XX操作网络变更操作技术方案（模板）-OTN-V3.0-20240802.docx 
2025-08-13 15:13:05.5329 | Spire.Doc解析成功: XX月XX日0：00-04：00XX网络XX操作网络变更操作技术方案（模板）-OTN-V3.0-20240802.docx 
2025-08-13 15:13:05.5462 | 模板验证完成，结果已保存到: D:\Tempfiles\模板验证结果.txt 
2025-08-13 15:20:00.2225 | [2025-08-13 15:20:00]定时任务SyncReadEmailJob执行失败：False 
2025-08-13 15:25:58.3605 | Request:/api/qmov2/ai/validatetemplatefilesasync 
2025-08-13 15:26:03.7545 | 开始验证模板文件分割标识符（使用Spire.Doc） 
2025-08-13 15:26:03.8132 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-传输网管-V3.0-20240802.docx 
2025-08-13 15:26:03.8132 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-服务器存储-V3.0-20240802.docx 
2025-08-13 15:26:03.8132 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-路由器交换机-V3.0-20240802 (1).docx 
2025-08-13 15:26:03.8132 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-终端-V3.0-20240802 (1).docx 
2025-08-13 15:26:03.8264 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-CDN-V3.0-20240802 (1).docx 
2025-08-13 15:26:03.8264 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-IPRAN-V3.2-20250527.docx 
2025-08-13 15:26:03.8264 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PON-V3.0-20240802.docx 
2025-08-13 15:26:03.8264 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PTN&SPN-V3.0-20240802.docx 
2025-08-13 15:26:03.8264 | 正在验证模板: XX月XX日0：00-04：00XX网络XX操作网络变更操作技术方案（模板）-OTN-V3.0-20240802.docx 
2025-08-13 15:26:03.8416 | 模板验证完成，结果已保存到: D:\Code\nmosphub\QMD.Web\bin\Debug\net5.0\AI\模板验证结果_SpireDoc.txt 
2025-08-13 15:27:44.0018 | Request:/api/qmov2/ai/validatetemplatefilesasync 
2025-08-13 15:27:48.5042 | 开始验证模板文件分割标识符（使用Spire.Doc） 
2025-08-13 15:27:52.0707 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-传输网管-V3.0-20240802.docx 
2025-08-13 15:27:52.3889 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-服务器存储-V3.0-20240802.docx 
2025-08-13 15:27:52.5067 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-路由器交换机-V3.0-20240802 (1).docx 
2025-08-13 15:27:52.7449 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-终端-V3.0-20240802 (1).docx 
2025-08-13 15:27:52.8662 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-CDN-V3.0-20240802 (1).docx 
2025-08-13 15:27:53.0316 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-IPRAN-V3.2-20250527.docx 
2025-08-13 15:27:53.3065 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PON-V3.0-20240802.docx 
2025-08-13 15:27:53.4681 | 正在验证模板: XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PTN&SPN-V3.0-20240802.docx 
2025-08-13 15:27:53.6725 | 正在验证模板: XX月XX日0：00-04：00XX网络XX操作网络变更操作技术方案（模板）-OTN-V3.0-20240802.docx 
2025-08-13 15:27:53.9023 | 模板验证完成，结果已保存到: D:\Tempfiles\模板验证结果_SpireDoc.txt 
2025-08-13 15:30:00.3228 | [2025-08-13 15:30:00]定时任务SyncReadEmailJob执行失败：False 
2025-08-13 15:40:00.2885 | [2025-08-13 15:40:00]定时任务SyncReadEmailJob执行失败：False 
2025-08-13 15:50:00.2113 | [2025-08-13 15:50:00]定时任务SyncReadEmailJob执行失败：False 
2025-08-13 15:54:07.3041 | Initialized Scheduler Signaller of type: Quartz.Core.SchedulerSignalerImpl 
2025-08-13 15:54:07.3222 | Quartz Scheduler created 
2025-08-13 15:54:07.3222 | Registering Quartz Job Initialization Plug-in. 
2025-08-13 15:54:07.3285 | RAMJobStore initialized. 
2025-08-13 15:54:07.3285 | Scheduler meta-data: Quartz Scheduler (v*******) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'Quartz.Core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'Quartz.Simpl.DefaultThreadPool' - with 10 threads.
  Using job-store 'Quartz.Simpl.RAMJobStore' - which does not support persistence. and is not clustered.
 
2025-08-13 15:54:07.3285 | Quartz scheduler 'QuartzScheduler' initialized 
2025-08-13 15:54:07.3285 | Quartz scheduler version: ******* 
2025-08-13 15:54:07.3285 | JobFactory set to: QMD.Job.JobFactory 
2025-08-13 15:54:07.3573 | Parsing XML file: D:\Code\nmosphub\QMD.Web\bin\Debug\net5.0\quartz_jobs.xml with systemId: ~/quartz_jobs.xml 
2025-08-13 15:54:07.5217 | Adding 20 jobs, 20 triggers. 
2025-08-13 15:54:07.5217 | Adding job: qmd_qa_jobgroup.syncQANetJob 
2025-08-13 15:54:07.5387 | Adding job: qmd_qa_jobgroup.SyncQaDashBoardRedisJob 
2025-08-13 15:54:07.5387 | Adding job: qmd_qa_jobgroup.syncNetInspectItemJob 
2025-08-13 15:54:07.5387 | Adding job: qmd_why_jobgroup.syncNetproviderJob 
2025-08-13 15:54:07.5387 | Adding job: qmd_itr_jobgroup.syncItrSolutionJob 
2025-08-13 15:54:07.5387 | Adding job: qmd_itr_jobgroup.syncItrOrderTaskJob 
2025-08-13 15:54:07.5387 | Adding job: qmd_itr_jobgroup.syncItrOrderInfoJob 
2025-08-13 15:54:07.5387 | Adding job: cycle_group.cycleRecordJob 
2025-08-13 15:54:07.5387 | Adding job: qmd_itr_jobgroup.syncDingtalkUserJob 
2025-08-13 15:54:07.5387 | Adding job: qmd_itr_jobgroup.syncItrProductInfoJob 
2025-08-13 15:54:07.5387 | Adding job: qmd_itr_jobgroup.syncNoticeRecordJob 
2025-08-13 15:54:07.5387 | Adding job: qmd_itr_jobgroup.syncNmospUserInfoJob 
2025-08-13 15:54:07.5387 | Adding job: qmd_itr_jobgroup.syncReadEmailJob 
2025-08-13 15:54:07.5387 | Adding job: qmd_qmo_jobgroup.syncNoticeTaskAdventJob 
2025-08-13 15:54:07.5455 | Adding job: qmd_qmo_jobgroup.syncNoticeTaskResultJob 
2025-08-13 15:54:07.5455 | Adding job: qmd_qmo_jobgroup.syncNoticeTaskTodoJob 
2025-08-13 15:54:07.5455 | Adding job: qmd_qmo_jobgroup.syncExpireOrderJob 
2025-08-13 15:54:07.5455 | Adding job: qmd_qmo_jobgroup.syncExpireTaskJob 
2025-08-13 15:54:07.5455 | Adding job: qmd_qmo_jobgroup.escalatedTaskRemindJob 
2025-08-13 15:54:07.5455 | Adding job: qmd_qmo_jobgroup.syncNoticeDisOrderJob 
2025-08-13 15:54:07.5455 | Scheduler QuartzScheduler_$_NON_CLUSTERED started. 
2025-08-13 15:54:07.8195 | Loading proxy data from config. 
2025-08-13 15:54:08.9775 | [2025-08-13 15:54:08]定时任务SyncReadEmailJob执行失败：False 
2025-08-13 15:54:23.4032 | Request:/api/qmov2/ai/testdocumentsplitting 
2025-08-13 15:54:23.5062 | 接收到文档分割测试请求 
2025-08-13 15:54:23.5062 | 开始分割文档: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-传输网管-V3.0-20240802.docx 
2025-08-13 15:54:24.2671 | 找到标识符: 变更操作概述 (模式: [变更操作概述]) 
2025-08-13 15:54:24.2671 | 找到标识符: 变更操作准备 (模式: [变更操作准备]) 
2025-08-13 15:54:24.2671 | 找到标识符: 操作实施步骤 (模式: [操作实施步骤]) 
2025-08-13 15:54:24.2797 | 分割完成: 找到 4/3 个标识符 
2025-08-13 15:54:24.2797 | 文档分割完成，共提取 4 个部分 
2025-08-13 16:00:00.3885 | [2025-08-13 16:00:00]定时任务SyncReadEmailJob执行失败：False 
2025-08-13 16:10:00.2377 | [2025-08-13 16:10:00]定时任务SyncReadEmailJob执行失败：False 
2025-08-13 16:20:00.2180 | [2025-08-13 16:20:00]定时任务SyncReadEmailJob执行失败：False 
2025-08-13 16:30:00.3308 | [2025-08-13 16:30:00]定时任务SyncReadEmailJob执行失败：False 
2025-08-13 16:40:00.2125 | [2025-08-13 16:40:00]定时任务SyncReadEmailJob执行失败：False 
2025-08-13 16:50:00.2683 | [2025-08-13 16:50:00]定时任务SyncReadEmailJob执行失败：False 
2025-08-13 17:00:00.2733 | [2025-08-13 17:00:00]定时任务SyncReadEmailJob执行失败：False 
2025-08-13 17:03:40.7482 | Request:/api/qmov2/ai/fillform 
2025-08-13 17:03:43.3952 | 接收到AI智能填报请求 
2025-08-13 17:03:46.2303 | 开始AI智能填报QMO工单，文件名: 2025年7月11日01：00-06：00漯河移动PON网络升级操作网络变更操作技术方案-PON-V3.0.docx 
2025-08-13 17:03:52.7683 | 🔄 开始文档分割 
2025-08-13 17:03:54.7246 | 开始分割文档: C:\Users\<USER>\AppData\Local\Temp\QMDAITemp\71dfc641-4e19-4ff3-a5ac-850aaf2e7f4e.docx 
2025-08-13 17:03:54.8720 | 找到标识符: 变更操作概述 (模式: [变更操作概述]) 
2025-08-13 17:03:54.8745 | 找到标识符: 变更操作准备 (模式: [变更操作准备]) 
2025-08-13 17:03:54.8745 | 找到标识符: 操作实施步骤 (模式: [操作实施步骤]) 
2025-08-13 17:03:54.8745 | 分割完成: 找到 4/3 个标识符 
2025-08-13 17:03:54.8745 | 文档分割完成，共提取 4 个部分 
2025-08-13 17:04:00.9498 | ✅ 文档分割完成，找到 4/3 个标识符，耗时: 7027.6444ms 
2025-08-13 17:04:01.8712 | 🔄 第一阶段开始：基础信息提取 
2025-08-13 17:04:16.8101 | 开始提取基础信息（省份、网络名称、工单类型、专业） 
2025-08-13 17:04:16.9505 | 文本优化：原始长度 4947，优化后长度 4904，节省 43 个字符 (0.87%) 
2025-08-13 17:04:16.9663 | 请求体: {"model":"qwen-plus","messages":[{"role":"system","content":"你是一个专业的QMO工单解析助手，擅长从文档中提取省份、网络名称和工单类型等基础信息。请严格按照标准列表进行匹配。"},{"role":"user","content":"请仔细阅读以下文档内容，并从中提取省份、网络名称和工单类型信息。 注意： 1. 省份必须严格从以下标准省份列表中匹配： [\"广东省\",\"湖北省\",\"湖南省\",\"西藏自治区\",\"黑龙江省\",\"甘肃省\",\"河南省\",\"北京市\",\"辽宁省\",\"广西壮族自治区\",\"江苏省\",\"山西省\",\"河北省\",\"吉林省\",\"智利\",\"四川省\",\"印度尼西亚\",\"山东省\",\"新疆维吾尔自治区\",\"云南省\",\"上海市\",\"重庆市\",\"浙江省\",\"安徽省\",\"内蒙古自治区\",\"江西省\",\"阿尔及利亚\",\"陕西省\",\"泰国\",\"贵州省\",\"肯尼亚\",\"越南\",\"青海省\",\"宁夏回族自治区\",\"巴西\",\"新加坡\",\"匈牙利\",\"印度\",\"罗马尼亚\",\"阿曼\",\"菲律宾\",\"西班牙\",\"葡萄牙\",\"天津市\",\"福建省\",\"马来西亚\",\"墨西哥\",\"伊拉克\",\"孟加拉国\",\"海南省\",\"约旦\",\"伊朗\",\"缅甸\",\"哥伦比亚\",\"亚美尼亚\",\"巴勒斯坦\",\"巴基斯坦\",\"厄瓜多尔\",\"也门\",\"南非\",\"摩洛哥\",\"GM\",\"阿根廷\",\"香港\",\"多米尼加\",\"波兰\",\"虚拟省\",\"WG\",\"土耳其\",\"蒙古\",\"乌克兰\",\"加拿大\",\"尼日利亚\",\"赤道几内亚\",\"塞浦路斯\",\"埃及\",\"乌干达\",\"津巴布韦\",\"老挝\"] 2. 地市必须严格从以下标准省份列表中匹配： [\"揭阳市\",\"武汉市\",\"长沙市\",\"拉萨市\",\"绥化市\",\"安阳市\",\"北京市\",\"珠海市\",\"江门市\",\"锦州市\",\"百色市\",\"南京市\",\"晋城市\",\"沧州市\",\"长春市\",\"哈尔滨市\",\"黄石市\",\"邵阳市\",\"圣地亚哥\",\"东莞市\",\"淮安市\",\"成都市\",\"潮州市\",\"Jakarta Raya\",\"鄂州市\",\"黑河市\",\"青岛市\",\"贺州地区\",\"德阳市\",\"兰州市\",\"常州市\",\"哈密地区\",\"梅州市\",\"徐州市\",\"唐山市\",\"曲靖市\",\"咸宁市\",\"上海市\",\"重庆市\",\"杭州市\",\"南通市\",\"盐城市\",\"十堰市\",\"南充市\",\"白银市\",\"淄博市\",\"平凉市\",\"济宁市\",\"石河子市\",\"佳木斯市\",\"太原市\",\"甘孜藏族自治州\",\"哈密市\",\"南宁市\",\"渝北区\",\"石家庄市\",\"孝感市\",\"合肥市\",\"平顶山市\",\"阳泉市\",\"西城区\",\"东爪哇\",\"抚顺市\",\"鸡西市\",\"黄山市\",\"包头市\",\"衡水市\",\"金昌市\",\"商丘市\",\"株洲市\",\"菏泽市\",\"塔城地区\",\"驻马店市\",\"运城市\",\"茂名市\",\"南昌市\",\"济源市\",\"阿尔及尔\",\"直辖行政单位\",\"眉山市\",\"西安市\",\"曼谷\",\"安顺市\",\"广州市\",\"延安市\",\"滁州市\",\"黄冈市\",\"洛阳市\",\"忻州市\",\"玉林市\",\"内罗毕\",\"新余市\",\"甘南藏族自治州\",\"河内\",\"西宁市\",\"清远市\",\"德州市\",\"邯郸市\",\"济南市\",\"日喀则地区\",\"比奥比奥大区\",\"郑州市\",\"张家口市\",\"图木舒克市\",\"漯河市\",\"银川市\",\"湖州市\",\"贵阳市\",\"荆门市\",\"肇庆市\",\"湘潭市\",\"丹东市\",\"克拉玛依市\",\"乐山市\",\"Minas Gerais\",\"Singapore\",\"张家界市\",\"汕尾市\",\"扬州市\",\"杰尔-莫松-肖普朗\",\"玉溪市\",\"盘锦市\",\"石嘴山市\",\"保定市\",\"襄阳市\",\"Maharashtra\",\"大庆市\",\"娄底市\",\"巴中市\",\"烟台市\",\"攀枝花市\",\"沈阳市\",\"呼和浩特市\",\"泰安市\",\"林芝地区\",\"宜昌市\",\"布加勒斯特\",\"昌都地区\",\"昆明市\",\"临沧市\",\"亳州市\",\"Rio de Janeiro\",\"Shamal al Batinah\",\"萍乡市\",\"鞍山市\",\"随州市\",\"朝阳市\",\"汕头市\",\"大雅加达首都特区\",\"宁波市\",\"湛江市\",\"西双版纳傣族自治州\",\"佛山市\",\"临夏回族自治州\",\"河源市\",\"信阳市\",\"Jawa Barat\",\"山南地区\",\"Cavite\",\"保山市\",\"巴塞罗那\",\"里斯本\",\"葫芦岛市\",\"郴州市\",\"安庆市\",\"资阳市\",\"临汾市\",\"NCR\",\"阳江市\",\"河东区\",\"铜陵市\",\"鄂尔多斯市\",\"六盘水市\",\"秦皇岛市\",\"乌鲁木齐市\",\"漳州市\",\"晋中市\",\"嘉兴市\",\"鹤壁市\",\"吉隆坡\",\"抚州市\",\"铜仁地区\",\"里约热内卢\",\"鹤岗市\",\"苏州市\",\"墨西哥城\",\"益阳市\",\"中山市\",\"深圳市\",\"福州市\",\"芜湖市\",\"桂林市\",\"仙桃市\",\"Al Anbar\",\"枣庄市\",\"内江市\",\"云浮市\",\"天门市\",\"廊坊市\",\"Baghdad\",\"绍兴市\",\"达卡\",\"雄安新区\",\"陇南市\",\"海口市\",\"马斯喀特省\",\"延边朝鲜族自治州\",\"金华市\",\"滨州市\",\"Region Metropolitana de Santiago\",\"梧州市\",\"七台河市\",\"新德里\",\"六安市\",\"雅加达\",\"许昌市\",\"固原市\",\"长治市\",\"喀什地区\",\"张掖市\",\"文山壮族苗族自治州\",\"牡丹江市\",\"伊春市\",\"本溪市\",\"Ma'an\",\"淮北市\",\"昌吉回族自治州\",\"河池地区\",\"达州市\",\"伊犁哈萨克自治州\",\"酒泉市\",\"National Capital Region\",\"丽江市\",\"白山市\",\"阜新市\",\"宣城市\",\"蚌埠市\",\"宜春市\",\"Tehran\",\"大兴安岭地区\",\"仰光省\",\"大连市\",\"聊城市\",\"阿里地区\",\"凉山彝族自治州\",\"三门峡市\",\"吕梁市\",\"双鸭山市\",\"昌平区\",\"泰州市\",\"天津市\",\"铁岭市\",\"泉州市\",\"玉树藏族自治州\",\"龙岩市\",\"波哥大首都区\",\"惠州市\",\"河池市\",\"埃里温市\",\"瓜达拉哈拉\",\"白城市\",\"宿州市\",\"那曲地区\",\"莱芜市\",\"沙捞越\",\"营口市\",\"神农架林区\",\"西岸\",\"文昌市\",\"濮阳市\",\"镇江市\",\"临沂市\",\"卡拉奇\",\"考卡\",\"金山区\",\"伊斯兰堡\",\"贵安新区\",\"皮钦查\",\"邢台市\",\"萨那\",\"荆州市\",\"赣州市\",\"咸阳市\",\"连云港市\",\"新乡市\",\"Karnataka\",\"果洛藏族自治州\",\"南阳市\",\"中卫市\",\"三明市\",\"东伦敦\",\"淮南市\",\"开封市\",\"乌兰察布市\",\"定西市\",\"阿坝藏族羌族自治州\",\"韶关市\",\"汉中市\",\"Rabat-Sale-Zemmour-Zaer\",\"怀化市\",\"Esfahan\",\"通化市\",\"嘉峪关市\",\"布宜诺斯艾利斯\",\"无锡市\",\"吐鲁番市\",\"普洱市\",\"塔拉帕卡大区\",\"岳阳市\",\"大理白族自治州\",\"九江市\",\"庆阳市\",\"吴忠市\",\"贺州市\",\"博尔塔拉蒙古自治州\",\"鹰潭市\",\"威海市\",\"雅安市\",\"乌海市\",\"呼伦贝尔市\",\"阿勒泰地区\",\"和田地区\",\"辽源市\",\"通辽市\",\"赤峰市\",\"马鞍山市\",\"铜川市\",\"丽水市\",\"承德市\",\"广元市\",\"潜江市\",\"安康市\",\"阿拉善盟\",\"安曼\",\"香港\",\"北碚区\",\"舟山市\",\"潍坊市\",\"怒江傈僳族自治州\",\"Distrito Nacional\",\"兴安盟\",\"Grand Casablanca\",\"钦州市\",\"华沙\",\"北海市\",\"朔州市\",\"厦门市\",\"萨拉戈萨\",\"绵阳市\",\"辽阳市\",\"自贡市\",\"日照市\",\"吉安市\",\"池州市\",\"虚拟市\",\"景德镇市\",\"阜阳市\",\"海南藏族自治州\",\"恩施土家族苗族自治州\",\"红河哈尼族彝族自治州\",\"上饶市\",\"大同市\",\"宝鸡市\",\"焦作市\",\"崇左市\",\"Moskva\",\"马拉加\",\"伊斯坦布尔\",\"来宾市\",\"东营市\",\"南戈壁\",\"湘西土家族苗族自治州\",\"遵义市\",\"海东地区\",\"黄南藏族自治州\",\"基辅\",\"Valle del Cauca\",\"麦德林\",\"Ontario\",\"柳州市\",\"黄浦区\",\"巴音郭楞蒙古自治州\",\"克孜勒苏柯尔克孜自治州\",\"瓜亚斯\",\"齐齐哈尔市\",\"商洛市\",\"Alberta\",\"阿比亚\",\"清迈\",\"衢州市\",\"防城港市\",\"天水市\",\"温州市\",\"Sindh\",\"台州市\",\"西青区\",\"南平市\",\"巴彦淖尔市\",\"Madrid, Comunidad de\",\"榆林市\",\"黔西南布依族苗族自治州\",\"渭南市\",\"泸州市\",\"Sao Paulo\",\"重庆高新技术产业开发\",\"Annobon\",\"锡林郭勒盟\",\"衡阳市\",\"四平市\",\"河南省直辖县级行政区划\",\"宜宾市\",\"宿迁市\",\"Lefkosia\",\"Masqat\",\"布达佩斯\",\"周口市\",\"塞阿拉\",\"广安市\",\"静安区\",\"开罗\",\"昭通市\",\"永州市\",\"常德市\",\"Sulawesi Selatan\",\"贵港市\",\"南开区\",\"基多\",\"沙巴\",\"浦东新区\",\"莫斯科\",\"坎帕拉\",\"黔南布依族苗族自治州\",\"宁河区\",\"百色地区\",\"海淀区\",\"Jawa Timur\",\"哈拉雷\",\"朝阳区\",\"毕节地区\",\"Punjab\",\"班加罗尔\",\"松原市\",\"黔东南苗族侗族自治州\",\"海西蒙古族藏族自治州\",\"门多萨\",\"阿克苏地区\",\"Abuja Federal Capital Territory\",\"Al 'Aqabah\",\"Buryatiya, Respublika\",\"铜梁区\",\"安卡拉\",\"万象\",\"江津区\",\"楚雄彝族自治州\",\"大兴区\",\"武威市\",\"毕节市\",\"卡萨布兰卡\"] 2. 工单类型必须严格从以下标准工单类型列表中匹配（每个工单类型包含id和name字段）： [{\"id\":1,\"name\":\"割接\"},{\"id\":4,\"name\":\"网络整改\"},{\"id\":5,\"name\":\"网络检查\"},{\"id\":7,\"name\":\"升级\"},{\"id\":8,\"name\":\"测试\"}] 举例说明： - 如果文档标题为\"XXX网络升级割接方案\"，且版本信息板块中明确提到目标版本为\"v1.0\"，则工单类型应识别为\"升级\"，因为满足最高优先级。 - 如果文档标题为\"XXX网络割接方案\"，且版本信息板块中没有明确提到目标版本，工单类型也应识别为\"割接\"。 - 如果文档标题为\"XXX网络变更操作技术方案\"，但操作简述中明确提到\"割接实施窗口时间\"，且版本信息板块中没有明确提到目标版本,工单类型应识别为\"割接\"。 3. 工单类型识别优先级： - 最高优先级：文档版本信息板块中明确的网管目标版本或设备的目标版本（表示工单类型为\"升级\"），否则是其他类型 - 次高优先级：文档标题中明确的工单类型（如\"XXX割接方案\"表示工单类型为\"割接\"） - 再次优先级：操作简述部分明确提到的工单类型 - 最低优先级：文档其他部分提到的工单类型 4. 网络名称应尽可能准确提取，主要从文件名、标题或其他总结性文字中提取，用于后续匹配标准网络名称,网络名称注意提取完整，尽量把整句话提取出来。 5. 专业必须严格从以下标准专业中匹配： [\"MSTP\",\"服务器存储\",\"OTN\",\"接入\",\"PTN\",\"IPRAN\",\"交换机路由器\",\"SPN\",\"智网\",\"终端\",\"其他\",\"CDN\",\"STN\",\"外购件（第三方）\",\"opticalFiber光纤\"] 特别说明： - 如果文档中出现PON或者OLT,属于接入专业。 文档内容： 网络 请按照以下JSON格式返回提取结果： { \"province\": \"省份名称（必须从标准列表中匹配）\", \"city\":\"地市名称（必须从标准列表中匹配）\", \"networkName\": \"网络名称\", \"orderType\": 工单类型ID（必须从标准列表中匹配）, \"orderTypeName\": \"工单类型名称（必须与标准列表中id对应的name完全一致）\", \"productSpe\": \"IPRAN\" }"}],"temperature":0.7,"stream":false} 
2025-08-13 17:04:16.9881 | 请求URL: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-13 17:04:16.9996 | Start processing HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-13 17:04:16.9996 | Sending HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-13 17:04:18.7486 | Received HTTP response headers after 1738.7944ms - 200 
2025-08-13 17:04:18.7486 | End processing HTTP request after 1760.849ms - 200 
2025-08-13 17:04:18.7486 | API响应: {"choices":[{"message":{"role":"assistant","content":"```json\n{\n  \"province\": \"广东省\",\n  \"city\": \"揭阳市\",\n  \"networkName\": \"IPRAN\",\n  \"orderType\": 7,\n  \"orderTypeName\": \"升级\",\n  \"productSpe\": \"IPRAN\"\n}\n```"},"finish_reason":"stop","index":0,"logprobs":null}],"object":"chat.completion","usage":{"prompt_tokens":2667,"completion_tokens":58,"total_tokens":2725,"prompt_tokens_details":{"cached_tokens":0}},"created":1755075858,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-143359ce-7286-9525-8daf-1856e6e91f7a"} 
2025-08-13 17:04:18.8503 | 提取基础信息完成，省份: 广东省，网络名称: IPRAN，工单类型: 升级 
2025-08-13 17:13:13.7483 | Initialized Scheduler Signaller of type: Quartz.Core.SchedulerSignalerImpl 
2025-08-13 17:13:13.7646 | Quartz Scheduler created 
2025-08-13 17:13:13.7646 | Registering Quartz Job Initialization Plug-in. 
2025-08-13 17:13:13.7646 | RAMJobStore initialized. 
2025-08-13 17:13:13.7646 | Scheduler meta-data: Quartz Scheduler (v*******) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'Quartz.Core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'Quartz.Simpl.DefaultThreadPool' - with 10 threads.
  Using job-store 'Quartz.Simpl.RAMJobStore' - which does not support persistence. and is not clustered.
 
2025-08-13 17:13:13.7646 | Quartz scheduler 'QuartzScheduler' initialized 
2025-08-13 17:13:13.7646 | Quartz scheduler version: ******* 
2025-08-13 17:13:13.7646 | JobFactory set to: QMD.Job.JobFactory 
2025-08-13 17:13:13.7646 | Parsing XML file: D:\Code\nmosphub\QMD.Web\bin\Debug\net5.0\quartz_jobs.xml with systemId: ~/quartz_jobs.xml 
2025-08-13 17:13:13.9101 | Adding 20 jobs, 20 triggers. 
2025-08-13 17:13:13.9101 | Adding job: qmd_qa_jobgroup.syncQANetJob 
2025-08-13 17:13:13.9256 | Adding job: qmd_qa_jobgroup.SyncQaDashBoardRedisJob 
2025-08-13 17:13:13.9256 | Adding job: qmd_qa_jobgroup.syncNetInspectItemJob 
2025-08-13 17:13:13.9256 | Adding job: qmd_why_jobgroup.syncNetproviderJob 
2025-08-13 17:13:13.9256 | Adding job: qmd_itr_jobgroup.syncItrSolutionJob 
2025-08-13 17:13:13.9256 | Adding job: qmd_itr_jobgroup.syncItrOrderTaskJob 
2025-08-13 17:13:13.9256 | Adding job: qmd_itr_jobgroup.syncItrOrderInfoJob 
2025-08-13 17:13:13.9256 | Adding job: cycle_group.cycleRecordJob 
2025-08-13 17:13:13.9256 | Adding job: qmd_itr_jobgroup.syncDingtalkUserJob 
2025-08-13 17:13:13.9256 | Adding job: qmd_itr_jobgroup.syncItrProductInfoJob 
2025-08-13 17:13:13.9256 | Adding job: qmd_itr_jobgroup.syncNoticeRecordJob 
2025-08-13 17:13:13.9256 | Adding job: qmd_itr_jobgroup.syncNmospUserInfoJob 
2025-08-13 17:13:13.9256 | Adding job: qmd_itr_jobgroup.syncReadEmailJob 
2025-08-13 17:13:13.9256 | Adding job: qmd_qmo_jobgroup.syncNoticeTaskAdventJob 
2025-08-13 17:13:13.9256 | Adding job: qmd_qmo_jobgroup.syncNoticeTaskResultJob 
2025-08-13 17:13:13.9256 | Adding job: qmd_qmo_jobgroup.syncNoticeTaskTodoJob 
2025-08-13 17:13:13.9392 | Adding job: qmd_qmo_jobgroup.syncExpireOrderJob 
2025-08-13 17:13:13.9392 | Adding job: qmd_qmo_jobgroup.syncExpireTaskJob 
2025-08-13 17:13:13.9392 | Adding job: qmd_qmo_jobgroup.escalatedTaskRemindJob 
2025-08-13 17:13:13.9392 | Adding job: qmd_qmo_jobgroup.syncNoticeDisOrderJob 
2025-08-13 17:13:13.9392 | Scheduler QuartzScheduler_$_NON_CLUSTERED started. 
2025-08-13 17:13:14.2280 | Loading proxy data from config. 
2025-08-13 17:13:15.3274 | [2025-08-13 17:13:15]定时任务SyncReadEmailJob执行失败：False 
2025-08-13 17:13:42.9703 | Request:/api/qmov2/ai/fillform 
2025-08-13 17:13:45.5568 | 接收到AI智能填报请求 
2025-08-13 17:13:47.3448 | 开始AI智能填报QMO工单，文件名: 2025年7月11日01：00-06：00漯河移动PON网络升级操作网络变更操作技术方案-PON-V3.0.docx 
2025-08-13 17:13:55.1123 | 🔄 开始文档分割 
2025-08-13 17:13:56.9361 | 开始分割文档: C:\Users\<USER>\AppData\Local\Temp\QMDAITemp\0071dad8-fc44-484b-80b0-7c3ef7434680.docx 
2025-08-13 17:13:57.6872 | 分割完成: 找到 1/3 个标识符 
2025-08-13 17:13:57.7017 | 文档分割完成，共提取 4 个部分 
2025-08-13 17:20:50.6185 | 🔄 开始文档分割 
2025-08-13 17:20:54.4376 | 开始分割文档: C:\Users\<USER>\AppData\Local\Temp\QMDAITemp\0071dad8-fc44-484b-80b0-7c3ef7434680.docx 
2025-08-13 17:20:55.5426 | [2025-08-13 17:20:55]定时任务SyncReadEmailJob执行失败：False 
2025-08-13 17:21:07.8656 | 标题格式验证通过: '变更操作概述' (模式: ^变更操作概述$) 
2025-08-13 17:21:07.8656 | 找到标题: 变更操作概述 -> '变更操作概述' (位置: 360) 
2025-08-13 17:21:09.1509 | 标题格式验证通过: '变更操作准备' (模式: ^变更操作准备$) 
2025-08-13 17:21:09.1509 | 找到标题: 变更操作准备 -> '变更操作准备' (位置: 897) 
2025-08-13 17:21:28.5509 | 分割完成: 找到 3/3 个标识符 
2025-08-13 17:21:34.7508 | 文档分割完成，共提取 4 个部分 
2025-08-13 17:27:23.3173 | 🔄 开始文档分割 
2025-08-13 17:27:26.7573 | 开始分割文档: C:\Users\<USER>\AppData\Local\Temp\QMDAITemp\0071dad8-fc44-484b-80b0-7c3ef7434680.docx 
2025-08-13 17:27:29.6874 | 开始提取完整文档内容: C:\Users\<USER>\AppData\Local\Temp\QMDAITemp\0071dad8-fc44-484b-80b0-7c3ef7434680.docx 
2025-08-13 17:27:43.6484 | 文档内容提取完成，总长度: 6267 
2025-08-13 17:28:03.1399 | 标题格式验证通过: '变更操作概述' (模式: ^变更操作概述$) 
2025-08-13 17:28:03.2021 | 找到标题: 变更操作概述 -> '变更操作概述' (位置: 423) 
2025-08-13 17:28:03.2021 | 标题格式验证通过: '变更操作准备' (模式: ^变更操作准备$) 
2025-08-13 17:28:03.2731 | 找到标题: 变更操作准备 -> '变更操作准备' (位置: 3762) 
2025-08-13 17:28:03.2796 | 分割完成: 找到 3/3 个标识符 
2025-08-13 17:28:42.2430 | 文档分割完成，共提取 4 个部分 
