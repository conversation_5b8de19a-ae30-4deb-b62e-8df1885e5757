请根据以下文档内容，提取QMO工单报备所需的详细信息。

基础信息：
- 工单类型：升级
- 处理部分：第一部分（开头到变更操作概述）

请从文档中提取以下信息：
6. 软件形态
  文档中无相关字段
    - 字符数组类型
    - 直接赋值空的数组
8. 工单主题
  工单主题
    -工单主题一般出现在内容开头部分，用来描述整个文档的具体内容。
    -例如：7月11日0：00-04：00浙江绍兴网络2LN4、1LN4单盘升级操作网络变更操作技术方案-OTN-V3.1（此例不能作为最后的提取内容）
9. 抄送人
  文档中无相关字段
    - 直接赋值空数组

文档内容：
{docContent}

### 所有提取的内容不能自己编造，只能从文档中提取，宁缺勿错的原则！

### 请严格按照以下JSON格式返回提取结果,JSON里的举例不能作为结果返回，如果没有置为空(注意，格式里的具体内容和文档无关，请你只提取文档里的内容)：
{
  softwareForm:["ECN软件包（影响业务）"], //6.软件形态
  "title": "工单主题",  // 8.工单主题
  "copyUsersList":[
    {"id":null,"userType":1,"invUserName":"<EMAIL>","invNickName":"肖绍谦","invPhone":"18627827716","invEmail":"<EMAIL>","invPostCode":4,"invPostName":"软件工程师","invDept":"武汉烽火技术服务有限公司-数字技术部","invUserLevel":null}
  ],  // 9.抄送人列表（绝对不能使用示例数据或者捏造数据）
}
