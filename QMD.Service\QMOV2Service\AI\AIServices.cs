using Common.DAL.Methods;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using QMD.DAL;
using QMD.DAL.Expends;
using QMD.DAL.Migrations;
using QMD.DAL.Table;
using QMD.DAL.Table.emt;
using QMD.Model;
using QMD.Model.emt;
using QMD.Model.QA;
using QMD.Model.Why.Why2;
using QMD.Repository;
using QMD.Repository.emt;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Common.Utility;
using QMD.Service.RedisService;

namespace QMD.Service.QMOV2Service.AI
{
    /// <summary>
    /// AI智能填报QMO工单服务
    /// </summary>
    public class AIServices
    {
        private readonly ILogger<AIServices> _logger;
        private readonly QmdDbContext _dbContext;
        private readonly OpenAIService _openAIService;
        private readonly DispatchOrderService _disOrderService;
        private readonly NetUserPositionRepository _positionRepository;
        private readonly PersonOutsourcedRepository _outPersonRepository;
        private readonly PersonOwnedRepository _ownPersonRepository;

        // 标准工单类型列表
        private readonly Dictionary<int, string> _orderTypes;

        // 模板内容缓存
        private static string _firstStageTemplate;
        private static string _secondStageTemplate;
        private static string _upgradeTemplate;
        private static string _commonTemplate;
        
        // 分段模板内容缓存
        private static string _upgradeSection1Template;
        private static string _upgradeSection2Template;
        private static string _upgradeSection3Template;
        private static string _upgradeSection4Template;
        private static string _commonSection1Template;
        private static string _commonSection2Template;
        private static string _commonSection3Template;
        private static string _commonSection4Template;

        public AIServices(
            ILogger<AIServices> logger,
            QmdDbContext dbContext,
            OpenAIService openAIService,
            DispatchOrderService disOrderService,
            PersonOutsourcedRepository outPersonRepository,
            PersonOwnedRepository ownPersonRepository,
            NetUserPositionRepository positionRepository)
        {
            _logger = logger;
            _dbContext = dbContext;
            _openAIService = openAIService;
            _disOrderService = disOrderService;
            _outPersonRepository = outPersonRepository;
            _ownPersonRepository = ownPersonRepository;
            _positionRepository = positionRepository;

            // 从枚举获取标准工单类型列表
            _orderTypes = GetOrderTypesFromEnum();

        }

        #region 提示词缓存
        private static readonly object _lock = new object();

        /// <summary>
        /// 第一阶段模板：提取基础信息（省份、网络名称、工单类型）
        /// </summary>
        public string FirstStageTemplate
        {
            get
            {
                lock (_lock)
                {
                    if (string.IsNullOrWhiteSpace(_firstStageTemplate))
                    {
                        string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AIPrompts", "first_stage_template.md");
                        if (File.Exists(templatePath))
                        {
                            _firstStageTemplate = File.ReadAllText(templatePath);
                        }
                    }
                }
                return _firstStageTemplate;
            }
        }

        /// <summary>
        /// 第二阶段模板：匹配标准网络名称
        /// </summary>
        public string SecondStageTemplate
        {
            get
            {
                lock (_lock)
                {
                    if (string.IsNullOrWhiteSpace(_secondStageTemplate))
                    {
                        string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AIPrompts", "second_stage_template.md");
                        if (File.Exists(templatePath))
                        {
                            _secondStageTemplate = File.ReadAllText(templatePath);
                        }
                    }
                }
                return _secondStageTemplate;
            }
        }

        /// <summary>
        /// 升级类型工单模板
        /// </summary>
        public string UpgradeTemplate
        {
            get
            {
                lock (_lock)
                {
                    if (string.IsNullOrWhiteSpace(_upgradeTemplate))
                    {
                        string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AIPrompts", "upgrade_template.md");
                        if (File.Exists(templatePath))
                        {
                            _upgradeTemplate = File.ReadAllText(templatePath);
                        }
                    }
                }
                return _upgradeTemplate;
            }
        }

        /// <summary>
        /// 通用工单模板
        /// </summary>
        public string CommonTemplate
        {
            get
            {
                lock (_lock)
                {
                    {
                        string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AIPrompts", "common_template.md");
                        if (File.Exists(templatePath))
                        {
                            _commonTemplate = File.ReadAllText(templatePath);
                        }
                    }
                }
                return _commonTemplate;
            }
        }

        /// <summary>
        /// 升级类型第一部分模板
        /// </summary>
        public string UpgradeSection1Template
        {
            get
            {
                lock (_lock)
                {
                    if (string.IsNullOrWhiteSpace(_upgradeSection1Template))
                    {
                        string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AIPrompts", "upgrade_section1_template.md");
                        if (File.Exists(templatePath))
                        {
                            _upgradeSection1Template = File.ReadAllText(templatePath);
                        }
                    }
                }
                return _upgradeSection1Template;
            }
        }

        /// <summary>
        /// 升级类型第二部分模板
        /// </summary>
        public string UpgradeSection2Template
        {
            get
            {
                lock (_lock)
                {
                    if (string.IsNullOrWhiteSpace(_upgradeSection2Template))
                    {
                        string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AIPrompts", "upgrade_section2_template.md");
                        if (File.Exists(templatePath))
                        {
                            _upgradeSection2Template = File.ReadAllText(templatePath);
                        }
                    }
                }
                return _upgradeSection2Template;
            }
        }

        /// <summary>
        /// 升级类型第三部分模板
        /// </summary>
        public string UpgradeSection3Template
        {
            get
            {
                lock (_lock)
                {
                    if (string.IsNullOrWhiteSpace(_upgradeSection3Template))
                    {
                        string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AIPrompts", "upgrade_section3_template.md");
                        if (File.Exists(templatePath))
                        {
                            _upgradeSection3Template = File.ReadAllText(templatePath);
                        }
                    }
                }
                return _upgradeSection3Template;
            }
        }

        /// <summary>
        /// 升级类型第四部分模板
        /// </summary>
        public string UpgradeSection4Template
        {
            get
            {
                lock (_lock)
                {
                    if (string.IsNullOrWhiteSpace(_upgradeSection4Template))
                    {
                        string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AIPrompts", "upgrade_section4_template.md");
                        if (File.Exists(templatePath))
                        {
                            _upgradeSection4Template = File.ReadAllText(templatePath);
                        }
                    }
                }
                return _upgradeSection4Template;
            }
        }

        /// <summary>
        /// 非升级类型第一部分模板
        /// </summary>
        public string CommonSection1Template
        {
            get
            {
                lock (_lock)
                {
                    if (string.IsNullOrWhiteSpace(_commonSection1Template))
                    {
                        string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AIPrompts", "common_section1_template.md");
                        if (File.Exists(templatePath))
                        {
                            _commonSection1Template = File.ReadAllText(templatePath);
                        }
                    }
                }
                return _commonSection1Template;
            }
        }

        /// <summary>
        /// 非升级类型第二部分模板
        /// </summary>
        public string CommonSection2Template
        {
            get
            {
                lock (_lock)
                {
                    if (string.IsNullOrWhiteSpace(_commonSection2Template))
                    {
                        string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AIPrompts", "common_section2_template.md");
                        if (File.Exists(templatePath))
                        {
                            _commonSection2Template = File.ReadAllText(templatePath);
                        }
                    }
                }
                return _commonSection2Template;
            }
        }

        /// <summary>
        /// 非升级类型第三部分模板
        /// </summary>
        public string CommonSection3Template
        {
            get
            {
                lock (_lock)
                {
                    if (string.IsNullOrWhiteSpace(_commonSection3Template))
                    {
                        string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AIPrompts", "common_section3_template.md");
                        if (File.Exists(templatePath))
                        {
                            _commonSection3Template = File.ReadAllText(templatePath);
                        }
                    }
                }
                return _commonSection3Template;
            }
        }

        /// <summary>
        /// 非升级类型第四部分模板
        /// </summary>
        public string CommonSection4Template
        {
            get
            {
                lock (_lock)
                {
                    if (string.IsNullOrWhiteSpace(_commonSection4Template))
                    {
                        string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AIPrompts", "common_section4_template.md");
                        if (File.Exists(templatePath))
                        {
                            _commonSection4Template = File.ReadAllText(templatePath);
                        }
                    }
                }
                return _commonSection4Template;
            }
        }

        #endregion

        /// <summary>
        /// 从EnumDisOrderType枚举获取标准工单类型列表
        /// </summary>
        /// <returns>工单类型字典</returns>
        private Dictionary<int, string> GetOrderTypesFromEnum()
        {
            var orderTypes = new Dictionary<int, string>();

            // 获取EnumDisOrderType枚举类型
            Type enumType = typeof(EnumDisOrderType);

            // 遍历枚举值
            foreach (EnumDisOrderType value in Enum.GetValues(enumType))
            {
                // 获取枚举值的字段信息
                FieldInfo field = enumType.GetField(value.ToString());

                // 获取Description特性
                DescriptionAttribute attribute = field.GetCustomAttribute<DescriptionAttribute>();

                // 获取EnumOrderKind特性
                EnumOrderKindAttribute kindAttribute = field.GetCustomAttribute<EnumOrderKindAttribute>();

                // 只添加OrderKind为1或3的工单类型（全部适用或报备适用）
                if (kindAttribute != null && (kindAttribute.OrderKind == 1 || kindAttribute.OrderKind == 3))
                {
                    // 使用Description特性作为名称
                    string description = attribute?.Description ?? value.ToString();

                    // 添加到字典
                    orderTypes.Add((int)value, description);
                }
            }

            return orderTypes;
        }

        /// <summary>
        /// AI智能填报QMO工单（单个文件）
        /// </summary>
        /// <param name="file">要解析的docx文件</param>
        /// <returns>解析结果</returns>
        public async Task<BaseRes<ReportordersDto>> AIFillFormAsync(IFormFile file)
        {
            try
            {
                _logger.LogInformation($"开始AI智能填报QMO工单，文件名: {file.FileName}");

                // 验证是否为docx文件
                string fileExtension = Path.GetExtension(file.FileName).ToLower();
                if (fileExtension != ".docx")
                {
                    return new BaseRes<ReportordersDto>(false, "仅支持docx格式的Word文档文件");
                }

                // 保存上传的文件到临时目录，使用GUID生成唯一的临时文件名
                string tempDir = Path.Combine(Path.GetTempPath(), "QMDAITemp");
                if (!Directory.Exists(tempDir))
                {
                    Directory.CreateDirectory(tempDir);
                }

                string tempFilePath = Path.Combine(tempDir, $"{Guid.NewGuid()}.docx");
                using (var stream = new FileStream(tempFilePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                try
                {
                    var totalStartTime = DateTime.Now;
                    
                    // 第0步：文档分割
                    _logger.LogInformation($"🔄 开始文档分割");
                    var splitStartTime = DateTime.Now;
                    var splitResult = SplitDocumentIntoSections(tempFilePath);
                    if (!splitResult.Success)
                    {
                        return new BaseRes<ReportordersDto>(false, splitResult.ErrorMessage);
                    }
                    var splitTime = DateTime.Now - splitStartTime;
                    _logger.LogInformation($"✅ 文档分割完成，找到 {splitResult.Sections.Count(s => s.IsFound)}/3 个标识符，耗时: {splitTime.TotalMilliseconds}ms");

                    // 第一阶段：使用第一部分提取基础信息（优化）
                    _logger.LogInformation($"🔄 第一阶段开始：基础信息提取");
                    var stage1StartTime = DateTime.Now;
                    var firstSectionContent = splitResult.Sections.Count > 0 ? splitResult.Sections[0].Content : string.Empty;
                    
                    var basicInfoResult = await ExtractBasicInfoAsync(firstSectionContent);
                    if (!basicInfoResult.Success)
                    {
                        return new BaseRes<ReportordersDto>(false, basicInfoResult.ErrorMessage);
                    }
                    var basicInfo = basicInfoResult.BasicInfo;

                    // 验证基础信息
                    if (!ValidateBasicInfo(basicInfo, out var errorMsg))
                    {
                        return new BaseRes<ReportordersDto>(false, errorMsg);
                    }
                    var stage1Time = DateTime.Now - stage1StartTime;
                    _logger.LogInformation($"✅ 第一阶段完成，耗时: {stage1Time.TotalMilliseconds}ms");

                    // 第二阶段：将网络名称与数据库匹配，获取标准网络名称（保持不变）
                    _logger.LogInformation($"🔄 第二阶段开始：网络名称匹配");
                    var stage2StartTime = DateTime.Now;
                    var matchResult = await MatchNetworkNameAsync(basicInfo.NetworkName, basicInfo.Province, basicInfo.City, basicInfo.ProductSpe);
                    if (!matchResult.Success)
                    {
                        return new BaseRes<ReportordersDto>(false, matchResult.ErrorMessage);
                    }
                    var networkInfo = matchResult.NetworkInfo;
                    var stage2Time = DateTime.Now - stage2StartTime;
                    _logger.LogInformation($"✅ 第二阶段完成，耗时: {stage2Time.TotalMilliseconds}ms");

                    // 第三阶段：4线程并发处理详细信息（重构）
                    var detailResult = await ExtractDetailInfoConcurrentAsync(splitResult.Sections, basicInfo, networkInfo);
                    if (!detailResult.Success)
                    {
                        return new BaseRes<ReportordersDto>(false, detailResult.ErrorMessage);
                    }

                    var totalTime = DateTime.Now - totalStartTime;
                    _logger.LogInformation($"✅ AI智能填报完成，工单类型: {basicInfo.OrderTypeName}，总耗时: {totalTime.TotalMilliseconds}ms");

                    return new BaseRes<ReportordersDto>(detailResult.DetailInfo);
                }
                finally
                {
                    // 删除临时文件
                    if (File.Exists(tempFilePath))
                    {
                        File.Delete(tempFilePath);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AI智能填报QMO工单失败");
                return new BaseRes<ReportordersDto>(false, $"处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从docx文件提取文本内容
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>提取结果，包含是否成功、提取的文本内容和错误消息</returns>
        private (bool Success, string Text, string ErrorMessage) ExtractTextFromDocx(string filePath)
        {
            try
            {
                _logger.LogInformation($"开始从docx文件提取文本: {filePath}");

                if (!File.Exists(filePath))
                {
                    return (false, null, $"文件不存在: {filePath}");
                }

                StringBuilder text = new StringBuilder();

                // 使用 DocumentFormat.OpenXml 解析 docx 文件
                using (WordprocessingDocument doc = WordprocessingDocument.Open(filePath, false))
                {
                    if (doc.MainDocumentPart?.Document?.Body == null)
                    {
                        return (false, null, "无效的docx文件：无法找到文档主体");
                    }

                    Body body = doc.MainDocumentPart.Document.Body;

                    // 提取所有段落的文本
                    foreach (var paragraph in body.Elements<Paragraph>())
                    {
                        string paragraphText = paragraph.InnerText;
                        if (!string.IsNullOrWhiteSpace(paragraphText))
                        {
                            text.AppendLine(paragraphText);
                        }
                    }

                    // 提取表格中的文本
                    foreach (var table in body.Elements<Table>())
                    {
                        foreach (var row in table.Elements<TableRow>())
                        {
                            foreach (var cell in row.Elements<TableCell>())
                            {
                                string cellText = cell.InnerText;
                                if (!string.IsNullOrWhiteSpace(cellText))
                                {
                                    text.Append(cellText + "\t");
                                }
                            }
                            text.AppendLine(); // 表格行结束换行
                        }
                    }
                }

                var extractedText = text.ToString().Trim();
                _logger.LogInformation($"从docx文件提取文本完成，文本长度: {extractedText.Length}");

                if (string.IsNullOrWhiteSpace(extractedText))
                {
                    _logger.LogWarning($"从docx文件提取的文本为空: {filePath}");
                }

                return (true, extractedText, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"从docx文件提取文本失败: {filePath}");
                return (false, null, $"从docx文件提取文本失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 第一阶段：提取基础信息
        /// </summary>
        /// <param name="docText">文档文本</param>
        /// <returns>提取结果，包含是否成功、基础信息和错误消息</returns>
        private async Task<(bool Success, BasicInfoDto BasicInfo, string ErrorMessage)> ExtractBasicInfoAsync(string docText)
        {
            try
            {
                _logger.LogInformation("开始提取基础信息（省份、网络名称、工单类型、专业）");

                // 获取标准省份和专业列表
                var provinces = _dbContext.Set<Netprovider>()
                    .Where(p => !string.IsNullOrWhiteSpace(p.Province) && p.IsActived).Select(x => x.Province).Distinct().ToList();
                var cities = _dbContext.Set<Netprovider>()
                    .Where(p => !string.IsNullOrWhiteSpace(p.City) && p.IsActived).Select(x => x.City).Distinct().ToList();

                var productSpes = _dbContext.Set<Netprovider>()
                    .Select(p => p.ProductSpe).Distinct().ToList(); // 确保专业字段唯一

                // 获取第一阶段提示词模板
                var templateContent = FirstStageTemplate;

                // 替换模板中的占位符
                var prompt = templateContent
                    .Replace("{provinces}", JsonConvert.SerializeObject(provinces))
                    .Replace("{cities}", JsonConvert.SerializeObject(cities))
                    .Replace("{orderTypes}", JsonConvert.SerializeObject(_orderTypes.Select(kv => new { id = kv.Key, name = kv.Value }).ToList()))
                    .Replace("{productSpes}", JsonConvert.SerializeObject(productSpes)) // 添加专业字段替换
                    .Replace("{docContent}", docText);

                // 调用AI提取基础信息
                var response = await _openAIService.GetFirstStageCompletionAsync(prompt);

                // 解析JSON响应
                var parseResult = ParseBasicInfoResponse(response);
                if (!parseResult.Success)
                {
                    return (false, null, parseResult.ErrorMessage);
                }

                _logger.LogInformation($"提取基础信息完成，省份: {parseResult.BasicInfo.Province}，网络名称: {parseResult.BasicInfo.NetworkName}，工单类型: {parseResult.BasicInfo.OrderTypeName}");

                return (true, parseResult.BasicInfo, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "提取基础信息失败");
                return (false, null, $"提取基础信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 解析第一阶段AI响应
        /// </summary>
        /// <param name="response">AI响应</param>
        /// <returns>解析结果，包含是否成功、基础信息和错误消息</returns>
        private (bool Success, BasicInfoDto BasicInfo, string ErrorMessage) ParseBasicInfoResponse(string response)
        {
            try
            {
                // 使用正则表达式提取JSON部分
                var match = Regex.Match(response, @"\{[\s\S]*\}");
                if (!match.Success)
                {
                    return (false, null, "无法从AI响应中提取JSON");
                }

                var jsonStr = match.Value;
                var basicInfo = JsonConvert.DeserializeObject<BasicInfoDto>(jsonStr);

                if (basicInfo == null)
                {
                    return (false, null, "解析AI响应JSON失败");
                }

                return (true, basicInfo, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"解析AI响应失败: {response}");
                return (false, null, $"解析AI响应失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证基础信息
        /// </summary>
        /// <param name="basicInfo">基础信息</param>
        /// <param name="errorMsg">错误信息</param>
        /// <returns>校验是否通过</returns>
        private bool ValidateBasicInfo(BasicInfoDto basicInfo, out string errorMsg)
        {
            errorMsg = null;
            // 验证省份
            var provinces = _dbContext.Set<Netprovider>()
                .Select(p => p.Province)
                .Distinct()
                .ToList();
            if (!provinces.Contains(basicInfo.Province))
            {
                errorMsg = $"提取的省份 '{basicInfo.Province}' 不在标准省份列表中";
                return false;
            }
            //验证专业
            var productSpes = _dbContext.Set<Netprovider>()
                .Select(p => p.ProductSpe)
                .Distinct()
                .ToList();
            if (!productSpes.Contains(basicInfo.ProductSpe))
            {
                errorMsg = $"提取的专业 '{basicInfo.ProductSpe}' 不在标准专业列表中";
                return false;
            }

            // 验证工单类型
            if (!_orderTypes.ContainsKey(basicInfo.OrderType))
            {
                errorMsg = $"提取的工单类型ID '{basicInfo.OrderType}' 不在标准工单类型列表中";
                return false;
            }

            // 验证工单类型名称
            if (_orderTypes[basicInfo.OrderType] != basicInfo.OrderTypeName)
            {
                errorMsg = $"提取的工单类型名称 '{basicInfo.OrderTypeName}' 与ID '{basicInfo.OrderType}' 不匹配";
                return false;
            }

            // 验证网络名称
            if (string.IsNullOrWhiteSpace(basicInfo.NetworkName))
            {
                errorMsg = "提取的网络名称不能为空";
                return false;
            }

            return true;
        }

        /// <summary>
        /// 第二阶段：匹配标准网络名称
        /// </summary>
        /// <param name="networkName">提取的网络名称</param>
        /// <param name="province">提取的省份</param>
        /// <returns>匹配结果，包含是否成功、网络信息和错误消息</returns>
        private async Task<(bool Success, NetworkInfoDto NetworkInfo, string ErrorMessage)> MatchNetworkNameAsync(string networkName, string province, string city, string productSpe)
        {
            try
            {
                _logger.LogInformation($"开始匹配标准网络名称: {networkName}");

                // 从数据库获取所有网络名称
                var netProviders = _dbContext.Set<Netprovider>().ToList();

                // 先用省份过滤，得到本省份所有网络
                var filteredProviders = netProviders
                    .Where(p => p.Province == province && !string.IsNullOrEmpty(p.NetName))
                    .ToList();
                if (!string.IsNullOrWhiteSpace(city))
                {
                    filteredProviders = filteredProviders.Where(x => x.City == city).ToList();
                }

                if (filteredProviders.Count == 0)
                {
                    return (false, null, $"未找到省份 '{province}:{city}' 下的网络数据");
                }

                // 提取所有可用的专业和运营商，去重
                var allProductSpe = filteredProviders
                    .Where(p => !string.IsNullOrEmpty(p.ProductSpe))
                    .Select(p => p.ProductSpe)
                    .Distinct()
                    .ToList();
                var allOperator = filteredProviders
                    .Where(p => !string.IsNullOrEmpty(p.Operator))
                    .Select(p => p.Operator)
                    .Distinct()
                    .ToList();

                // 构建标准网络列表，包含网络名、运营商和专业
                var standardNetworks = filteredProviders
                    .Select(p => new
                    {
                        p.NetName,
                        p.Province,
                        p.NetProperties,
                        p.ProductSpe,
                        p.Operator
                    })
                    .ToList();

                // 获取第二阶段提示词模板
                var templateContent = SecondStageTemplate;

                // 构建AI提示词，包含省份、网络名称、所有可选专业、所有可选运营商
                var prompt = templateContent
                    .Replace("{productSpe}", productSpe)
                    .Replace("{networkName}", networkName)
                    .Replace("{province}", province)
                    .Replace("{city}", city)
                    .Replace("{standardNetworks}", JsonConvert.SerializeObject(standardNetworks))
                    .Replace("{allProductSpe}", JsonConvert.SerializeObject(allProductSpe))
                    .Replace("{allOperator}", JsonConvert.SerializeObject(allOperator));

                // 调用AI匹配标准网络名称
                var response = await _openAIService.GetSecondStageCompletionAsync(prompt);

                // 解析AI响应
                var match = Regex.Match(response, @"\{[\s\S]*\}");
                if (!match.Success)
                {
                    return (false, null, "无法从AI响应中提取JSON");
                }

                var jsonStr = match.Value;
                var matchResult = JsonConvert.DeserializeObject<NetworkMatchResult>(jsonStr);

                if (matchResult == null || string.IsNullOrEmpty(matchResult.StandardNetworkName))
                {
                    return (false, null, "无法匹配标准网络名称");
                }

                // 查找匹配的网络提供商
                var matchedProvider = filteredProviders.FirstOrDefault(p =>
                    p.NetName == matchResult.StandardNetworkName);

                if (matchedProvider == null)
                {
                    // 如果没有找到匹配的网络名称，使用专业属性进行辅助判断
                    matchedProvider = filteredProviders.FirstOrDefault(p =>
                        string.IsNullOrEmpty(p.ProductSpe) && p.ProductSpe == productSpe);
                }

                if (matchedProvider == null)
                {
                    return (false, null, $"无法找到匹配的标准网络名称: {matchResult.StandardNetworkName}");
                }

                var networkInfo = new NetworkInfoDto
                {
                    StandardNetworkName = matchedProvider.NetName,
                    CodeSk = matchedProvider.CodeSk,
                    DepName = matchedProvider.DepName,
                    MatchResult = matchResult.MatchConfidence
                };

                _logger.LogInformation($"AI匹配标准网络名称完成: {networkInfo.StandardNetworkName}，匹配置信度: {matchResult.MatchConfidence}%");

                return (true, networkInfo, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"匹配标准网络名称失败: {networkName}");
                return (false, null, $"匹配标准网络名称失败: {networkName}");
            }
        }

        /// <summary>
        /// 第三阶段：提取详细字段信息
        /// </summary>
        /// <param name="docText">文档文本</param>
        /// <param name="basicInfo">基础信息</param>
        /// <param name="networkInfo">网络信息</param>
        /// <returns>提取结果，包含是否成功、详细表单信息和错误消息</returns>
        private async Task<(bool Success, ReportordersDto DetailInfo, string ErrorMessage)> ExtractDetailInfoAsync(string docText, BasicInfoDto basicInfo, NetworkInfoDto networkInfo)
        {
            try
            {
                _logger.LogInformation($"开始提取详细信息，工单类型: {basicInfo.OrderTypeName}");

                // 根据工单类型选择提示词模板
                string templateContent;
                if (basicInfo.OrderType == 7) // 7是升级类型,EnumDisOrderType
                {
                    templateContent = UpgradeTemplate;
                }
                else
                {
                    templateContent = CommonTemplate;
                }

                // 替换模板中的占位符
                var prompt = templateContent
                    .Replace("{docContent}", docText)
                    .Replace("{riskLevels}", string.Join(",", QMD.Env.ConfigEnvValues.QmoV2Cfg.RepRiskLevels)); // 使用ConfigEnvValues中的风险级别替换

                // 调用AI提取详细信息
                var response = await _openAIService.GetDetailCompletionAsync(prompt);

                // 解析JSON响应
                var parseResult = ParseDetailInfoResponse(response, basicInfo, networkInfo);
                if (!parseResult.Success)
                {
                    return (false, null, parseResult.ErrorMessage);
                }

                _logger.LogInformation($"提取详细信息完成，工单标题: {parseResult.DetailInfo.Title}");

                return (true, parseResult.DetailInfo, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "提取详细信息失败");
                return (false, null, $"提取详细信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 第三阶段：并发提取详细字段信息
        /// </summary>
        /// <param name="sections">文档分割结果</param>
        /// <param name="basicInfo">基础信息</param>
        /// <param name="networkInfo">网络信息</param>
        /// <returns>提取结果，包含是否成功、详细表单信息和错误消息</returns>
        private async Task<(bool Success, ReportordersDto DetailInfo, string ErrorMessage)> ExtractDetailInfoConcurrentAsync(List<DocumentSection> sections, BasicInfoDto basicInfo, NetworkInfoDto networkInfo)
        {
            try
            {
                _logger.LogInformation($"🔄 第三阶段开始：4线程并发处理，工单类型: {basicInfo.OrderTypeName}");
                var startTime = DateTime.Now;

                bool isUpgrade = basicInfo.OrderType == 7; // 7是升级类型

                // 创建4个并发任务
                var tasks = new List<Task<PartialResult>>();

                // 线程1：处理第一部分
                if (sections.Count > 0)
                {
                    _logger.LogInformation($"🔄 线程1开始处理第一部分（字段{(isUpgrade ? "6,8,9" : "6,7")}）");
                    tasks.Add(ExtractFromSection1Async(sections[0].Content, isUpgrade));
                }

                // 线程2：处理第二部分  
                if (sections.Count > 1)
                {
                    _logger.LogInformation($"🔄 线程2开始处理第二部分（字段{(isUpgrade ? "1,2,3,7,10,11,12,13,19" : "1,2,3,8,9,10,16")}）");
                    tasks.Add(ExtractFromSection2Async(sections[1].Content, isUpgrade));
                }

                // 线程3：处理第三部分
                if (sections.Count > 2)
                {
                    _logger.LogInformation($"🔄 线程3开始处理第三部分（字段{(isUpgrade ? "14,15,16,17,18,20" : "11,12,13,14,15,17")}）");
                    tasks.Add(ExtractFromSection3Async(sections[2].Content, isUpgrade));
                }

                // 线程4：处理第四部分
                if (sections.Count > 3)
                {
                    _logger.LogInformation($"🔄 线程4开始处理第四部分（字段4,5）");
                    tasks.Add(ExtractFromSection4Async(sections[3].Content, isUpgrade));
                }

                // 等待所有任务完成
                var results = await Task.WhenAll(tasks);

                // 记录各线程完成情况
                for (int i = 0; i < results.Length; i++)
                {
                    var result = results[i];
                    if (result.Success)
                    {
                        _logger.LogInformation($"✅ 线程{i + 1}完成，耗时: {result.ProcessTime.TotalMilliseconds}ms");
                    }
                    else
                    {
                        _logger.LogWarning($"⚠️ 线程{i + 1}失败: {result.ErrorMessage}，耗时: {result.ProcessTime.TotalMilliseconds}ms");
                    }
                }

                var totalTime = DateTime.Now - startTime;
                var maxTime = results.Max(r => r.ProcessTime);
                _logger.LogInformation($"✅ 第三阶段完成，最大耗时: {maxTime.TotalMilliseconds}ms，总耗时: {totalTime.TotalMilliseconds}ms");

                // 合并结果
                _logger.LogInformation($"🔄 开始结果合并");
                var mergeStartTime = DateTime.Now;
                var detailInfo = MergePartialResults(results, basicInfo, networkInfo);
                var mergeTime = DateTime.Now - mergeStartTime;
                _logger.LogInformation($"✅ 结果合并完成，耗时: {mergeTime.TotalMilliseconds}ms");

                return (true, detailInfo, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "并发提取详细信息失败");
                return (false, null, $"并发提取详细信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理第一部分内容
        /// </summary>
        private async Task<PartialResult> ExtractFromSection1Async(string content, bool isUpgrade)
        {
            var startTime = DateTime.Now;
            var result = new PartialResult
            {
                SectionName = "第一部分",
                Success = false
            };

            try
            {
                // 选择对应的模板
                string template = isUpgrade ? UpgradeSection1Template : CommonSection1Template;
                
                // 替换模板中的占位符
                var prompt = template.Replace("{docContent}", content);

                // 调用AI提取信息
                var response = await _openAIService.GetDetailCompletionAsync(prompt);

                // 解析JSON响应
                var parseResult = ParseSectionResponse(response);
                if (parseResult.Success)
                {
                    result.ExtractedFields = parseResult.Fields;
                    result.Success = true;
                }
                else
                {
                    result.ErrorMessage = parseResult.ErrorMessage;
                }
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"第一部分处理失败: {ex.Message}";
            }
            finally
            {
                result.ProcessTime = DateTime.Now - startTime;
            }

            return result;
        }

        /// <summary>
        /// 处理第二部分内容
        /// </summary>
        private async Task<PartialResult> ExtractFromSection2Async(string content, bool isUpgrade)
        {
            var startTime = DateTime.Now;
            var result = new PartialResult
            {
                SectionName = "第二部分",
                Success = false
            };

            try
            {
                // 选择对应的模板
                string template = isUpgrade ? UpgradeSection2Template : CommonSection2Template;
                
                // 替换模板中的占位符
                var prompt = template
                    .Replace("{docContent}", content)
                    .Replace("{riskLevels}", string.Join(",", QMD.Env.ConfigEnvValues.QmoV2Cfg.RepRiskLevels));

                // 调用AI提取信息
                var response = await _openAIService.GetDetailCompletionAsync(prompt);

                // 解析JSON响应
                var parseResult = ParseSectionResponse(response);
                if (parseResult.Success)
                {
                    result.ExtractedFields = parseResult.Fields;
                    result.Success = true;
                }
                else
                {
                    result.ErrorMessage = parseResult.ErrorMessage;
                }
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"第二部分处理失败: {ex.Message}";
            }
            finally
            {
                result.ProcessTime = DateTime.Now - startTime;
            }

            return result;
        }

        /// <summary>
        /// 处理第三部分内容
        /// </summary>
        private async Task<PartialResult> ExtractFromSection3Async(string content, bool isUpgrade)
        {
            var startTime = DateTime.Now;
            var result = new PartialResult
            {
                SectionName = "第三部分",
                Success = false
            };

            try
            {
                // 选择对应的模板
                string template = isUpgrade ? UpgradeSection3Template : CommonSection3Template;
                
                // 替换模板中的占位符
                var prompt = template.Replace("{docContent}", content);

                // 调用AI提取信息
                var response = await _openAIService.GetDetailCompletionAsync(prompt);

                // 解析JSON响应
                var parseResult = ParseSectionResponse(response);
                if (parseResult.Success)
                {
                    result.ExtractedFields = parseResult.Fields;
                    result.Success = true;
                }
                else
                {
                    result.ErrorMessage = parseResult.ErrorMessage;
                }
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"第三部分处理失败: {ex.Message}";
            }
            finally
            {
                result.ProcessTime = DateTime.Now - startTime;
            }

            return result;
        }

        /// <summary>
        /// 处理第四部分内容
        /// </summary>
        private async Task<PartialResult> ExtractFromSection4Async(string content, bool isUpgrade)
        {
            var startTime = DateTime.Now;
            var result = new PartialResult
            {
                SectionName = "第四部分",
                Success = false
            };

            try
            {
                // 选择对应的模板（升级和非升级的第四部分模板相同）
                string template = isUpgrade ? UpgradeSection4Template : CommonSection4Template;
                
                // 替换模板中的占位符
                var prompt = template.Replace("{docContent}", content);

                // 调用AI提取信息
                var response = await _openAIService.GetDetailCompletionAsync(prompt);

                // 解析JSON响应
                var parseResult = ParseSectionResponse(response);
                if (parseResult.Success)
                {
                    result.ExtractedFields = parseResult.Fields;
                    result.Success = true;
                }
                else
                {
                    result.ErrorMessage = parseResult.ErrorMessage;
                }
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"第四部分处理失败: {ex.Message}";
            }
            finally
            {
                result.ProcessTime = DateTime.Now - startTime;
            }

            return result;
        }

        /// <summary>
        /// 解析分段响应
        /// </summary>
        private (bool Success, Dictionary<string, object> Fields, string ErrorMessage) ParseSectionResponse(string response)
        {
            try
            {
                // 使用正则表达式提取JSON部分
                var match = Regex.Match(response, @"\{[\s\S]*\}");
                if (!match.Success)
                {
                    return (false, null, "无法从AI响应中提取JSON");
                }

                var jsonStr = match.Value;
                var fields = JsonConvert.DeserializeObject<Dictionary<string, object>>(jsonStr);

                if (fields == null)
                {
                    return (false, null, "解析AI响应JSON失败");
                }

                return (true, fields, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, null, $"解析分段响应失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 合并部分处理结果 - 严格遵循原有ParseDetailInfoResponse格式
        /// </summary>
        private ReportordersDto MergePartialResults(PartialResult[] results, BasicInfoDto basicInfo, NetworkInfoDto networkInfo)
        {
            try
            {
                // 合并所有成功的字段到一个JObject中
                var mergedJsonObject = new JObject();
                foreach (var result in results.Where(r => r.Success))
                {
                    foreach (var field in result.ExtractedFields)
                    {
                        mergedJsonObject[field.Key] = JToken.FromObject(field.Value);
                    }
                }

                // 创建表单DTO对象
                var orderType = (EnumDisOrderType)basicInfo.OrderType;

                #region 对人员的特殊处理(适应前端的需求)   
                var taskUsers = DeserializeJson<List<NetUserPosition>>(mergedJsonObject, "taskUsers") ?? new List<NetUserPosition>(); // 从 JSON 中提取
                var checkUsers = DeserializeJson<List<NetUserPosition>>(mergedJsonObject, "checkUsers") ?? new List<NetUserPosition>(); // 从 JSON 中提取
                //对照数据库，补充人员信息
                taskUsers = GetMappedUsers(taskUsers, networkInfo.DepName);
                checkUsers = GetMappedUsers(checkUsers, networkInfo.DepName);
                var handleTaskUsers = new List<InvolveUserDto>();
                var handleCheckUsers = new List<InvolveUserDto>();
                var relatedUserEmails = taskUsers.Select(u => u.UserEmail)
                    .Union(checkUsers.Select(u => u.UserEmail))
                    .Where(email => !string.IsNullOrWhiteSpace(email))
                    .Distinct()
                    .ToList();
                var personOwns = _ownPersonRepository.Query(x => relatedUserEmails.Contains(x.UserEmail)).ToList();
                var personOuts = _outPersonRepository.Query(x => relatedUserEmails.Contains(x.UserEmail)).ToList();
                var personLevels = personOwns.Select(x => new InvolveUserLevelDto { ID = x.ID, UserType = "own", ProductSpe = x.ProductSpe, UserLevel = x.UserLevel, UserEmail = x.UserEmail }).ToList()
                                             .Concat(personOuts.Select(x => new InvolveUserLevelDto { ID = x.ID, UserType = "out", ProductSpe = x.ProductSpe, UserLevel = x.UserLevel, UserEmail = x.UserEmail }).ToList()).ToList();
                
                foreach(var taskUser in taskUsers)
                {
                    var handleTaskUser = new InvolveUserDto
                    {
                        InvEmail = taskUser.UserEmail,
                        InvNickName = taskUser.NickName,
                        InvPhone = taskUser.UserPhone,
                        InvDept = taskUser.DepName,
                        InvPostName = taskUser.Position,
                        //InvUserLevel = taskUser.UserLevel,这个没办法解析
                        InvUserName = taskUser.UserEmail,
                        UserType = EnumInvolveUserType.实施人,
                        IsActived = true,
                        UserLevels = personLevels.Where(x => x.UserEmail == taskUser.UserEmail).ToList()
                    };
                    handleTaskUsers.Add(handleTaskUser);
                }

                foreach (var checkUser in checkUsers)
                {
                    var handleCheckUser = new InvolveUserDto
                    {
                        InvEmail = checkUser.UserEmail,
                        InvNickName = checkUser.NickName,
                        InvPhone = checkUser.UserPhone,
                        InvDept = checkUser.DepName,
                        InvPostName = checkUser.Position,
                        //InvUserLevel = checkUser.UserLevel,这个没办法解析
                        InvUserName = checkUser.UserEmail,
                        UserType = EnumInvolveUserType.核查人,
                        IsActived = true,
                        UserLevels = personLevels.Where(x => x.UserEmail == checkUser.UserEmail).ToList()
                    };
                    handleCheckUsers.Add(handleCheckUser);
                }
                //申请人也要处理
                var applyUser = _positionRepository.Query(x => x.IsActived && x.UserEmail == HttpContextHelper.CurrentUserEmail).FirstOrDefault();
                var handleApplyUser = applyUser == null ? new InvolveUserDto() : new InvolveUserDto()
                {
                    InvEmail = applyUser.UserEmail,
                    InvNickName = applyUser.NickName,
                    InvPhone = applyUser.UserPhone,
                    InvDept = applyUser.DepName,
                    InvPostName = applyUser.Position,
                    //InvUserLevel = applyUser.UserLevel,这个没办法解析
                    InvUserName = applyUser.UserEmail,
                    UserType = EnumInvolveUserType.申请人,
                    IsActived = true,
                    UserLevels = personLevels.Where(x => x.UserEmail == applyUser.UserEmail).ToList()
                };
                #endregion

                var detailInfo = new ReportordersDto();
                if(orderType == EnumDisOrderType.升级)
                {
                    var softwareFormList = DeserializeJson<List<string>>(mergedJsonObject, "softwareForm");
                    var upgradeReasonList = DeserializeJson<List<string>>(mergedJsonObject, "upgradeReason");

                    detailInfo.OrderType = orderType; // 根据 basicInfo 的 OrderType 确定
                    detailInfo.LineRemark = DeserializeJson<string>(mergedJsonObject, "lineRemark"); // 从 JSON 中提取
                    detailInfo.RiskLevel = DeserializeJson<string>(mergedJsonObject, "riskLevel"); // 从 JSON 中提取
                    detailInfo.PlanStartTime = DeserializeJson<DateTime?>(mergedJsonObject, "planStartTime"); // 从 JSON 中提取并转换为 DateTime
                    detailInfo.PlanEndTime = DeserializeJson<DateTime?>(mergedJsonObject, "planEndTime"); // 从 JSON 中提取并转换为 DateTime
                    detailInfo.Title = DeserializeJson<string>(mergedJsonObject, "title"); // 从 JSON 中提取
                    detailInfo.CodeSk = networkInfo.CodeSk; // 从 JSON 中提取
                    detailInfo.TaskContent = DeserializeJson<string>(mergedJsonObject, "taskContent"); // 从 JSON 中提取
                    detailInfo.IsReinstated = DeserializeJson<bool>(mergedJsonObject, "isReinstated"); // 从 JSON 中提取

                    detailInfo.SoftwareForm = softwareFormList == null ? null : JsonConvert.SerializeObject(softwareFormList); // 转换为 JSON 字符串
                    detailInfo.UpgradeReason = upgradeReasonList == null ? null : JsonConvert.SerializeObject(upgradeReasonList); // 转换为 JSON 字符串
                    detailInfo.IsActived = true; // 始终有效
                    detailInfo.TaskUsers = handleTaskUsers;
                    detailInfo.CheckUsers = handleCheckUsers;
                    detailInfo.InvolveDevices = DeserializeJson<List<InvolveDeviceDto>>(mergedJsonObject, "involveDevices") ?? new List<InvolveDeviceDto>(); // 从 JSON 中提取
                    detailInfo.InvolveNmpinfos = DeserializeJson<List<InvolveNmpinfoDto>>(mergedJsonObject, "involveNmpinfos") ?? new List<InvolveNmpinfoDto>(); // 从 JSON 中提取
                    detailInfo.InvolveProducts = DeserializeJson<List<InvolveProductDto>>(mergedJsonObject, "involveProducts") ?? new List<InvolveProductDto>(); // 从 JSON 中提取

                    detailInfo.UpgradeMode = (EnumUpgradeMode)DeserializeJson<int>(mergedJsonObject, "upgradeMode");// 从 JSON 中提取
                    detailInfo.CustomUsers = DeserializeJson<List<InvolveUserDto>>(mergedJsonObject, "customUsers") ?? new List<InvolveUserDto>(); // 从 JSON 中提取

                }
                else
                {
                    detailInfo.OrderType = orderType; // 根据 basicInfo 的 OrderType 确定
                    detailInfo.LineRemark = DeserializeJson<string>(mergedJsonObject, "lineRemark"); // 从 JSON 中提取
                    detailInfo.RiskLevel = DeserializeJson<string>(mergedJsonObject, "riskLevel"); // 从 JSON 中提取
                    detailInfo.PlanStartTime = DeserializeJson<DateTime?>(mergedJsonObject, "planStartTime"); // 从 JSON 中提取并转换为 DateTime
                    detailInfo.PlanEndTime = DeserializeJson<DateTime?>(mergedJsonObject, "planEndTime"); // 从 JSON 中提取并转换为 DateTime
                    detailInfo.Title = DeserializeJson<string>(mergedJsonObject, "title"); // 从 JSON 中提取
                    detailInfo.CodeSk = networkInfo.CodeSk;// 从 JSON 中提取
                    detailInfo.TaskContent = DeserializeJson<string>(mergedJsonObject, "taskContent"); // 从 JSON 中提取
                    detailInfo.IsReinstated = DeserializeJson<bool>(mergedJsonObject, "isReinstated"); // 从 JSON 中提取
                    detailInfo.IsActived = true; // 始终有效
                    detailInfo.TaskUsers = handleTaskUsers;
                    detailInfo.CheckUsers = handleCheckUsers;
                    detailInfo.InvolveDevices = DeserializeJson<List<InvolveDeviceDto>>(mergedJsonObject, "involveDevices") ?? new List<InvolveDeviceDto>(); // 从 JSON 中提取
                    detailInfo.InvolveNmpinfos = DeserializeJson<List<InvolveNmpinfoDto>>(mergedJsonObject, "involveNmpinfos") ?? new List<InvolveNmpinfoDto>(); // 从 JSON 中提取
                    detailInfo.InvolveProducts = DeserializeJson<List<InvolveProductDto>>(mergedJsonObject, "involveProducts") ?? new List<InvolveProductDto>(); // 从 JSON 中提取
                    detailInfo.CustomUsers = DeserializeJson<List<InvolveUserDto>>(mergedJsonObject, "customUsers") ?? new List<InvolveUserDto>(); // 从 JSON 中提取
                }

                //OutputLine,ProductLine,ProductLMT这三个需要代码判断
                var netOrgs = _disOrderService.GetWhyIINetOrganizationCache().GetAwaiter().GetResult()?.Data ?? new List<ProductLineInfoDto>();
                var netLinqOrgs = netOrgs.GroupBy(x => x.codeSk).Select(x => new
                {
                    CodeSk = x.Key,
                    OutputLine = x.Max(t => t.primaryOrganization),
                    ProductLine = x.Max(t => t.productLine),
                    ProductLmt = x.Select(t => t.fourthOrganization).Distinct().ToList()
                }).ToList();
                netLinqOrgs = netLinqOrgs.Where(x => networkInfo.CodeSk == x.CodeSk).ToList();

                var netLinqOrg = netLinqOrgs.FirstOrDefault();
                if (netLinqOrg != null)
                {
                    detailInfo.OutputLine = netLinqOrg.OutputLine;
                    detailInfo.ProductLine = netLinqOrg.ProductLine;
                    //detailInfo.ProductLMT = netLinqOrg.ProductLmt;
                    if(netLinqOrg.ProductLmt.Count == 1)//只有一个默认选第一个
                    {
                        detailInfo.ProductLMT = netLinqOrg.ProductLmt.ToJson();
                    }
                    else
                    {
                        detailInfo.ProductLMT = null;
                    }
                    
                }
                detailInfo.LocalTime = DateTime.Now;

                detailInfo.CustomUsers.ForEach(x =>
                {
                    x.UserType = EnumInvolveUserType.客户;
                    x.InvUserName = x.InvEmail;//数据库里两者保持一致
                });

                _logger.LogInformation($"结果合并成功，合并了{results.Count(r => r.Success)}/{results.Length}个部分的结果");
                return detailInfo;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"合并部分结果失败: {ex.Message}");
                
                // 返回基础的detailInfo，确保不会完全失败
                var detailInfo = new ReportordersDto
                {
                    OrderType = (EnumDisOrderType)basicInfo.OrderType,
                    CodeSk = networkInfo.CodeSk,
                    IsActived = true,
                    LocalTime = DateTime.Now,
                    TaskUsers = new List<InvolveUserDto>(),
                    CheckUsers = new List<InvolveUserDto>(),
                    InvolveDevices = new List<InvolveDeviceDto>(),
                    InvolveNmpinfos = new List<InvolveNmpinfoDto>(),
                    InvolveProducts = new List<InvolveProductDto>(),
                    CustomUsers = new List<InvolveUserDto>()
                };
                
                return detailInfo;
            }
        }


        /// <summary>
        /// 获取详细信息提取模板内容
        /// </summary>
        /// <param name="orderTypeId">工单类型ID</param>
        /// <returns>模板内容</returns>
        private string GetDetailTemplate(int orderTypeId)
        {
            // 只区分升级类型和其他类型
            return orderTypeId == 7 ? UpgradeTemplate : CommonTemplate;
        }

        /// <summary>
        /// 解析第三阶段AI响应
        /// </summary>
        /// <param name="response">AI响应</param>
        /// <param name="basicInfo">基础信息</param>
        /// <param name="networkInfo">网络信息</param>
        /// <returns>解析结果，包含是否成功、详细表单信息和错误消息</returns>
        private (bool Success, ReportordersDto DetailInfo, string ErrorMessage) ParseDetailInfoResponse(string response, BasicInfoDto basicInfo, NetworkInfoDto networkInfo)
        {
            try
            {
                // 使用正则表达式提取JSON部分
                var match = Regex.Match(response, @"\{[\s\S]*\}");
                if (!match.Success)
                {
                    return (false, null, "无法从AI响应中提取JSON");
                }

                var jsonStr = match.Value;
                var jsonObject = JsonConvert.DeserializeObject<JObject>(jsonStr);

                if (jsonObject == null)
                {
                    return (false, null, "解析AI响应JSON失败");
                }

                // 创建表单DTO对象
                var orderType = (EnumDisOrderType)basicInfo.OrderType;

                #region 对人员的特殊处理(适应前端的需求)   
                var taskUsers = DeserializeJson<List<NetUserPosition>>(jsonObject, "taskUsers") ?? new List<NetUserPosition>(); // 从 JSON 中提取
                var checkUsers = DeserializeJson<List<NetUserPosition>>(jsonObject, "checkUsers") ?? new List<NetUserPosition>(); // 从 JSON 中提取
                //对照数据库，补充人员信息
                taskUsers = GetMappedUsers(taskUsers, networkInfo.DepName);
                checkUsers = GetMappedUsers(checkUsers, networkInfo.DepName);
                var handleTaskUsers = new List<InvolveUserDto>();
                var handleCheckUsers = new List<InvolveUserDto>();
                var relatedUserEmails = taskUsers.Select(u => u.UserEmail)
                    .Union(checkUsers.Select(u => u.UserEmail))
                    .Where(email => !string.IsNullOrWhiteSpace(email))
                    .Distinct()
                    .ToList();
                var personOwns = _ownPersonRepository.Query(x => relatedUserEmails.Contains(x.UserEmail)).ToList();
                var personOuts = _outPersonRepository.Query(x => relatedUserEmails.Contains(x.UserEmail)).ToList();
                var personLevels = personOwns.Select(x => new InvolveUserLevelDto { ID = x.ID, UserType = "own", ProductSpe = x.ProductSpe, UserLevel = x.UserLevel, UserEmail = x.UserEmail }).ToList()
                                             .Concat(personOuts.Select(x => new InvolveUserLevelDto { ID = x.ID, UserType = "out", ProductSpe = x.ProductSpe, UserLevel = x.UserLevel, UserEmail = x.UserEmail }).ToList()).ToList();
                
                foreach(var taskUser in taskUsers)
                {
                    var handleTaskUser = new InvolveUserDto
                    {
                        InvEmail = taskUser.UserEmail,
                        InvNickName = taskUser.NickName,
                        InvPhone = taskUser.UserPhone,
                        InvDept = taskUser.DepName,
                        InvPostName = taskUser.Position,
                        //InvUserLevel = taskUser.UserLevel,这个没办法解析
                        InvUserName = taskUser.UserEmail,
                        UserType = EnumInvolveUserType.实施人,
                        IsActived = true,
                        UserLevels = personLevels.Where(x => x.UserEmail == taskUser.UserEmail).ToList()
                    };
                    handleTaskUsers.Add(handleTaskUser);
                }

                foreach (var checkUser in checkUsers)
                {
                    var handleCheckUser = new InvolveUserDto
                    {
                        InvEmail = checkUser.UserEmail,
                        InvNickName = checkUser.NickName,
                        InvPhone = checkUser.UserPhone,
                        InvDept = checkUser.DepName,
                        InvPostName = checkUser.Position,
                        //InvUserLevel = checkUser.UserLevel,这个没办法解析
                        InvUserName = checkUser.UserEmail,
                        UserType = EnumInvolveUserType.核查人,
                        IsActived = true,
                        UserLevels = personLevels.Where(x => x.UserEmail == checkUser.UserEmail).ToList()
                    };
                    handleCheckUsers.Add(handleCheckUser);
                }
                //申请人也要处理
                var applyUser = _positionRepository.Query(x => x.IsActived && x.UserEmail == HttpContextHelper.CurrentUserEmail).FirstOrDefault();
                var handleApplyUser = applyUser == null ? new InvolveUserDto() : new InvolveUserDto()
                {
                    InvEmail = applyUser.UserEmail,
                    InvNickName = applyUser.NickName,
                    InvPhone = applyUser.UserPhone,
                    InvDept = applyUser.DepName,
                    InvPostName = applyUser.Position,
                    //InvUserLevel = applyUser.UserLevel,这个没办法解析
                    InvUserName = applyUser.UserEmail,
                    UserType = EnumInvolveUserType.申请人,
                    IsActived = true,
                    UserLevels = personLevels.Where(x => x.UserEmail == applyUser.UserEmail).ToList()
                };
                #endregion


                var detailInfo = new ReportordersDto();
                if(orderType == EnumDisOrderType.升级)
                {
                    var softwareFormList = DeserializeJson<List<string>>(jsonObject, "softwareForm");
                    var upgradeReasonList = DeserializeJson<List<string>>(jsonObject, "upgradeReason");

                    detailInfo.OrderType = orderType; // 根据 basicInfo 的 OrderType 确定
                    detailInfo.LineRemark = DeserializeJson<string>(jsonObject, "lineRemark"); // 从 JSON 中提取
                    detailInfo.RiskLevel = DeserializeJson<string>(jsonObject, "riskLevel"); // 从 JSON 中提取
                    detailInfo.PlanStartTime = DeserializeJson<DateTime?>(jsonObject, "planStartTime"); // 从 JSON 中提取并转换为 DateTime
                    detailInfo.PlanEndTime = DeserializeJson<DateTime?>(jsonObject, "planEndTime"); // 从 JSON 中提取并转换为 DateTime
                    detailInfo.Title = DeserializeJson<string>(jsonObject, "title"); // 从 JSON 中提取
                    detailInfo.CodeSk = networkInfo.CodeSk; // 从 JSON 中提取
                    detailInfo.TaskContent = DeserializeJson<string>(jsonObject, "taskContent"); // 从 JSON 中提取
                    detailInfo.IsReinstated = DeserializeJson<bool>(jsonObject, "isReinstated"); // 从 JSON 中提取

                    detailInfo.SoftwareForm = softwareFormList == null ? null : JsonConvert.SerializeObject(softwareFormList); // 转换为 JSON 字符串
                    detailInfo.UpgradeReason = upgradeReasonList == null ? null : JsonConvert.SerializeObject(upgradeReasonList); // 转换为 JSON 字符串
                    detailInfo.IsActived = true; // 始终有效
                    detailInfo.TaskUsers = handleTaskUsers;
                    detailInfo.CheckUsers = handleCheckUsers;
                    detailInfo.InvolveDevices = DeserializeJson<List<InvolveDeviceDto>>(jsonObject, "involveDevices") ?? new List<InvolveDeviceDto>(); // 从 JSON 中提取
                    detailInfo.InvolveNmpinfos = DeserializeJson<List<InvolveNmpinfoDto>>(jsonObject, "involveNmpinfos") ?? new List<InvolveNmpinfoDto>(); // 从 JSON 中提取
                    detailInfo.InvolveProducts = DeserializeJson<List<InvolveProductDto>>(jsonObject, "involveProducts") ?? new List<InvolveProductDto>(); // 从 JSON 中提取

                    detailInfo.UpgradeMode = (EnumUpgradeMode)DeserializeJson<int>(jsonObject, "upgradeMode");// 从 JSON 中提取
                    detailInfo.CustomUsers = DeserializeJson<List<InvolveUserDto>>(jsonObject, "customUsers") ?? new List<InvolveUserDto>(); // 从 JSON 中提取

                }
                else
                {
                    detailInfo.OrderType = orderType; // 根据 basicInfo 的 OrderType 确定
                    detailInfo.LineRemark = DeserializeJson<string>(jsonObject, "lineRemark"); // 从 JSON 中提取
                    detailInfo.RiskLevel = DeserializeJson<string>(jsonObject, "riskLevel"); // 从 JSON 中提取
                    detailInfo.PlanStartTime = DeserializeJson<DateTime?>(jsonObject, "planStartTime"); // 从 JSON 中提取并转换为 DateTime
                    detailInfo.PlanEndTime = DeserializeJson<DateTime?>(jsonObject, "planEndTime"); // 从 JSON 中提取并转换为 DateTime
                    detailInfo.Title = DeserializeJson<string>(jsonObject, "title"); // 从 JSON 中提取
                    detailInfo.CodeSk = networkInfo.CodeSk;// 从 JSON 中提取
                    detailInfo.TaskContent = DeserializeJson<string>(jsonObject, "taskContent"); // 从 JSON 中提取
                    detailInfo.IsReinstated = DeserializeJson<bool>(jsonObject, "isReinstated"); // 从 JSON 中提取
                    detailInfo.IsActived = true; // 始终有效
                    detailInfo.TaskUsers = handleTaskUsers;
                    detailInfo.CheckUsers = handleCheckUsers;
                    detailInfo.InvolveDevices = DeserializeJson<List<InvolveDeviceDto>>(jsonObject, "involveDevices") ?? new List<InvolveDeviceDto>(); // 从 JSON 中提取
                    detailInfo.InvolveNmpinfos = DeserializeJson<List<InvolveNmpinfoDto>>(jsonObject, "involveNmpinfos") ?? new List<InvolveNmpinfoDto>(); // 从 JSON 中提取
                    detailInfo.InvolveProducts = DeserializeJson<List<InvolveProductDto>>(jsonObject, "involveProducts") ?? new List<InvolveProductDto>(); // 从 JSON 中提取
                    detailInfo.CustomUsers = DeserializeJson<List<InvolveUserDto>>(jsonObject, "customUsers") ?? new List<InvolveUserDto>(); // 从 JSON 中提取
                }

                //OutputLine,ProductLine,ProductLMT这三个需要代码判断
                var netOrgs = _disOrderService.GetWhyIINetOrganizationCache().GetAwaiter().GetResult()?.Data ?? new List<ProductLineInfoDto>();
                var netLinqOrgs = netOrgs.GroupBy(x => x.codeSk).Select(x => new
                {
                    CodeSk = x.Key,
                    OutputLine = x.Max(t => t.primaryOrganization),
                    ProductLine = x.Max(t => t.productLine),
                    ProductLmt = x.Select(t => t.fourthOrganization).Distinct().ToList()
                }).ToList();
                netLinqOrgs = netLinqOrgs.Where(x => networkInfo.CodeSk == x.CodeSk).ToList();

                var netLinqOrg = netLinqOrgs.FirstOrDefault();
                if (netLinqOrg != null)
                {
                    detailInfo.OutputLine = netLinqOrg.OutputLine;
                    detailInfo.ProductLine = netLinqOrg.ProductLine;
                    //detailInfo.ProductLMT = netLinqOrg.ProductLmt;
                    if(netLinqOrg.ProductLmt.Count == 1)//只有一个默认选第一个
                    {
                        detailInfo.ProductLMT = netLinqOrg.ProductLmt.ToJson();
                    }
                    else
                    {
                        detailInfo.ProductLMT = null;
                    }
                    
                }
                detailInfo.LocalTime = DateTime.Now;

                detailInfo.CustomUsers.ForEach(x =>
                {
                    x.UserType = EnumInvolveUserType.客户;
                    x.InvUserName = x.InvEmail;//数据库里两者保持一致
                });


                return (true, detailInfo, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"解析AI响应失败: {response}");
                return (false, null, $"解析AI响应失败: {ex.Message}");
            }
        }
        /// <summary>
        /// 统一的用户匹配逻辑
        /// 逻辑：昵称(+部门)筛选 -> 如果唯一则返回 -> 如果多个则加电话筛选 -> 如果还有多个取第一个 -> 没有则返回null
        /// </summary>
        /// <param name="user">要匹配的用户信息</param>
        /// <param name="depName">部门名称（可选）</param>
        /// <returns>匹配的用户，如果没有匹配则返回null</returns>
        private NetUserPosition FindUserWithSmartMatching(NetUserPosition user, string depName)
        {
            if (string.IsNullOrWhiteSpace(user.NickName))
                return null;

            // 第一步：用昵称筛选，如果有部门信息则加上部门筛选
            var query = _positionRepository.Query(x => x.IsActived && x.NickName == user.NickName);

            if (!string.IsNullOrWhiteSpace(depName))
            {
                query = query.Where(x => x.DepName == depName || x.Department1 == depName || x.Department2 == depName || x.Department3 == depName);
            }

            var candidateUsers = query.ToList();

            if (candidateUsers.Count == 0)
                return null;

            if (candidateUsers.Count == 1)
                return candidateUsers.First();

            // 第二步：如果有多个，加上电话筛选
            if (!string.IsNullOrWhiteSpace(user.UserPhone))
            {
                var phoneFilteredUsers = candidateUsers.Where(x => x.UserPhone == user.UserPhone).ToList();
                if (phoneFilteredUsers.Count > 0)
                {
                    return phoneFilteredUsers.First(); // 如果还有多个就取第一个
                }
            }

            // 第三步：如果电话筛选后还是没有匹配，取第一个候选用户
            return candidateUsers.First();
        }

        /// <summary>
        /// 将识别到的用户信息转换成数据库标准的数据
        /// </summary>
        /// <param name="userEntities"></param>
        /// <param name="depName"></param>
        /// <returns></returns>
        private List<NetUserPosition> GetMappedUsers(List<NetUserPosition> userEntities, string depName)
        {
            var mappedUsers = new List<NetUserPosition>();

            foreach (var user in userEntities)
            {
                var mappedUser = FindUserWithSmartMatching(user, depName);

                // 添加到列表
                if (mappedUser != null)
                {
                    mappedUsers.Add(mappedUser);
                }
                else
                {
                    mappedUsers.Add(new NetUserPosition()); // 如果没有找到，添加空对象
                }
            }

            return mappedUsers;
        }
        private T DeserializeJson<T>(JObject jsonObject, string propertyName)
        {
            var settings = new JsonSerializerSettings
            {
                ContractResolver = new DefaultContractResolver
                {
                    NamingStrategy = new CamelCaseNamingStrategy()
                }
            };
            return jsonObject[propertyName].ToObject<T>(JsonSerializer.Create(settings));
        }

        #region 进度管理方法

        /// <summary>
        /// 启动AI智能解析docx文档任务（异步处理）
        /// </summary>
        /// <param name="file">要解析的docx文件</param>
        /// <returns>任务启动结果</returns>
        public async Task<BaseRes<AIFillTaskStartResult>> StartParseDocxTaskAsync(IFormFile file)
        {
            try
            {
                var taskId = Guid.NewGuid().ToString();
                _logger.LogInformation($"启动AI智能解析docx文档任务，任务ID: {taskId}，文件名: {file.FileName}");

                // 验证是否为docx文件
                string fileExtension = Path.GetExtension(file.FileName).ToLower();
                if (fileExtension != ".docx")
                {
                    return new BaseRes<AIFillTaskStartResult>(false, "仅支持docx格式的Word文档文件");
                }

                // 立即保存文件到临时目录，避免IFormFile生命周期问题
                string tempDir = Path.Combine(Path.GetTempPath(), "QMDAITemp");
                if (!Directory.Exists(tempDir))
                {
                    Directory.CreateDirectory(tempDir);
                }

                string tempFilePath = Path.Combine(tempDir, $"{taskId}.docx");
                using (var stream = new FileStream(tempFilePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                // 初始化进度
                UpdateProgress(taskId, AIFillStage.Starting, "任务已启动，准备处理文档");

                // 启动后台任务处理，传递文件路径而非IFormFile对象
                _ = Task.Run(() => ProcessAIFillFormInBackground(taskId, tempFilePath))
                    .ContinueWith(task => 
                    {
                        if (task.IsFaulted)
                        {
                            _logger.LogError(task.Exception, $"后台任务执行异常: {taskId}");
                        }
                    }, TaskContinuationOptions.OnlyOnFaulted);

                var result = new AIFillTaskStartResult
                {
                    TaskId = taskId,
                    Success = true
                };

                return new BaseRes<AIFillTaskStartResult>(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动AI智能填报任务失败");
                return new BaseRes<AIFillTaskStartResult>(false, $"启动任务失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 后台处理AI填报任务（保持原有业务逻辑不变）
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="tempFilePath">临时文件路径</param>
        private async Task ProcessAIFillFormInBackground(string taskId, string tempFilePath)
        {
            try
            {
                _logger.LogInformation($"开始后台处理AI填报任务: {taskId}");

                // 验证文件是否存在
                if (!File.Exists(tempFilePath))
                {
                    var errorMsg = $"临时文件不存在: {tempFilePath}";
                    _logger.LogError(errorMsg);
                    UpdateProgress(taskId, AIFillStage.Starting, errorMsg, true, errorMsg);
                    SaveFinalResult(taskId, false, null, errorMsg);
                    return;
                }

                try
                {
                    var totalStartTime = DateTime.Now;
                    
                    // 第0步：文档分割
                    _logger.LogInformation($"🔄 开始文档分割");
                    UpdateProgress(taskId, AIFillStage.DocumentSplitting, "正在分割文档");
                    var splitStartTime = DateTime.Now;
                    var splitResult = SplitDocumentIntoSections(tempFilePath);
                    if (!splitResult.Success)
                    {
                        UpdateProgress(taskId, AIFillStage.DocumentSplitting, splitResult.ErrorMessage, true, splitResult.ErrorMessage);
                        SaveFinalResult(taskId, false, null, splitResult.ErrorMessage);
                        return;
                    }
                    var splitTime = DateTime.Now - splitStartTime;
                    _logger.LogInformation($"✅ 文档分割完成，找到 {splitResult.Sections.Count(s => s.IsFound)}/3 个标识符，耗时: {splitTime.TotalMilliseconds}ms");

                    // 第一阶段：使用第一部分提取基础信息（优化）
                    _logger.LogInformation($"🔄 第一阶段开始：基础信息提取");
                    UpdateProgress(taskId, AIFillStage.BasicInfoExtraction, "正在提取基础信息（省份、网络名称、工单类型）");
                    var stage1StartTime = DateTime.Now;
                    var firstSectionContent = splitResult.Sections.Count > 0 ? splitResult.Sections[0].Content : string.Empty;
                    
                    var basicInfoResult = await ExtractBasicInfoAsync(firstSectionContent);
                    if (!basicInfoResult.Success)
                    {
                        UpdateProgress(taskId, AIFillStage.BasicInfoExtraction, basicInfoResult.ErrorMessage, true, basicInfoResult.ErrorMessage);
                        SaveFinalResult(taskId, false, null, basicInfoResult.ErrorMessage);
                        return;
                    }
                    var basicInfo = basicInfoResult.BasicInfo;

                    // 验证基础信息
                    if (!ValidateBasicInfo(basicInfo, out var errorMsg))
                    {
                        UpdateProgress(taskId, AIFillStage.BasicInfoExtraction, errorMsg, true, errorMsg);
                        SaveFinalResult(taskId, false, null, errorMsg);
                        return;
                    }
                    var stage1Time = DateTime.Now - stage1StartTime;
                    _logger.LogInformation($"✅ 第一阶段完成，耗时: {stage1Time.TotalMilliseconds}ms");

                    // 第二阶段：将网络名称与数据库匹配，获取标准网络名称（保持不变）
                    _logger.LogInformation($"🔄 第二阶段开始：网络名称匹配");
                    UpdateProgress(taskId, AIFillStage.NetworkMatching, "正在匹配标准网络名称");
                    var stage2StartTime = DateTime.Now;
                    var matchResult = await MatchNetworkNameAsync(basicInfo.NetworkName, basicInfo.Province, basicInfo.City, basicInfo.ProductSpe);
                    if (!matchResult.Success)
                    {
                        UpdateProgress(taskId, AIFillStage.NetworkMatching, matchResult.ErrorMessage, true, matchResult.ErrorMessage);
                        SaveFinalResult(taskId, false, null, matchResult.ErrorMessage);
                        return;
                    }
                    var networkInfo = matchResult.NetworkInfo;
                    var stage2Time = DateTime.Now - stage2StartTime;
                    _logger.LogInformation($"✅ 第二阶段完成，耗时: {stage2Time.TotalMilliseconds}ms");

                    // 第三阶段：4线程并发处理详细信息（重构）
                    UpdateProgress(taskId, AIFillStage.DetailInfoExtraction, "正在4线程并发提取详细信息");
                    var detailResult = await ExtractDetailInfoConcurrentAsync(splitResult.Sections, basicInfo, networkInfo);
                    if (!detailResult.Success)
                    {
                        UpdateProgress(taskId, AIFillStage.DetailInfoExtraction, detailResult.ErrorMessage, true, detailResult.ErrorMessage);
                        SaveFinalResult(taskId, false, null, detailResult.ErrorMessage);
                        return;
                    }

                    // 结果合并完成
                    UpdateProgress(taskId, AIFillStage.ResultMerging, "正在合并处理结果");
                    
                    var totalTime = DateTime.Now - totalStartTime;
                    _logger.LogInformation($"✅ AI智能填报完成，工单类型: {basicInfo.OrderTypeName}，总耗时: {totalTime.TotalMilliseconds}ms");

                    // 保存最终结果并标记完成
                    SaveFinalResult(taskId, true, detailResult.DetailInfo, null);
                    UpdateProgress(taskId, AIFillStage.Completed, "AI智能填报任务完成");
                }
                finally
                {
                    // 删除临时文件
                    if (File.Exists(tempFilePath))
                    {
                        File.Delete(tempFilePath);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"后台处理AI解析docx文档任务失败: {taskId}");
                UpdateProgress(taskId, AIFillStage.Starting, ex.Message, true, ex.Message);
                SaveFinalResult(taskId, false, null, ex.Message);
                
                // 确保异常情况下也删除临时文件
                if (File.Exists(tempFilePath))
                {
                    try
                    {
                        File.Delete(tempFilePath);
                    }
                    catch (Exception deleteEx)
                    {
                        _logger.LogWarning(deleteEx, $"删除临时文件失败: {tempFilePath}");
                    }
                }
            }
        }

        /// <summary>
        /// 获取AI解析docx文档进度
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>进度信息</returns>
        public Task<AIFillProgressDto> GetParseDocxProgressAsync(string taskId)
        {
            var progress = AIFillProgressRedisService.GetProgress(taskId);
            return Task.FromResult(progress);
        }

        /// <summary>
        /// 获取AI解析docx文档最终结果
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>最终结果</returns>
        public Task<AIFillFinalResult> GetParseDocxResultAsync(string taskId)
        {
            var cacheKey = $"ai_fill_result:{taskId}";
            var result = CacheHelper.GetCache<AIFillFinalResult>(cacheKey);
            return Task.FromResult(result);
        }

        /// <summary>
        /// 更新AI填报进度
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="stage">当前阶段</param>
        /// <param name="detailMessage">详细信息</param>
        /// <param name="hasError">是否有错误</param>
        /// <param name="errorMessage">错误信息</param>
        private void UpdateProgress(string taskId, AIFillStage stage, string detailMessage = null, bool hasError = false, string errorMessage = null)
        {
            try
            {
                var progress = AIFillProgressRedisService.GetProgress(taskId) ?? new AIFillProgressDto
                {
                    TaskId = taskId,
                    StartTime = DateTime.Now
                };
                
                // 🎯 关键逻辑：IsCompleted作为前端停止轮询的唯一标志
                // 只有在以下两种情况下设置为true（后台不再继续处理）：
                // 1. 正常完成：stage == AIFillStage.Completed
                // 2. 异常退出：hasError == true
                progress.IsCompleted = stage == AIFillStage.Completed || hasError;
                
                // 设置进度百分比和状态
                if (progress.IsCompleted)
                {
                    // 任务结束时统一设置为100%
                    progress.Percentage = 100;
                    progress.CurrentStage = hasError ? "处理异常" : "处理完成";
                }
                else
                {
                    // 任务进行中时使用阶段对应的百分比
                    progress.Percentage = (int)stage;
                    progress.CurrentStage = GetStageDescription(stage);
                }
                
                progress.DetailMessage = detailMessage ?? progress.DetailMessage;
                progress.HasError = hasError;
                progress.ErrorMessage = errorMessage;
                progress.UpdateTime = DateTime.Now;
                
                // 保存到Redis（30分钟过期）
                AIFillProgressRedisService.SaveProgress(taskId, progress);
                
                var statusText = progress.IsCompleted 
                    ? (hasError ? "异常退出" : "正常完成") 
                    : "正在处理";
                _logger.LogInformation($"更新进度: {taskId} -> {progress.Percentage}% ({progress.CurrentStage}) [IsCompleted={progress.IsCompleted}, {statusText}]");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新进度失败: {taskId}");
            }
        }

        /// <summary>
        /// 保存最终结果
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="success">是否成功</param>
        /// <param name="data">结果数据</param>
        /// <param name="errorMessage">错误信息</param>
        private void SaveFinalResult(string taskId, bool success, ReportordersDto data, string errorMessage)
        {
            try
            {
                var cacheKey = $"ai_fill_result:{taskId}";
                
                var result = new AIFillFinalResult
                {
                    TaskId = taskId,
                    Success = success,
                    Data = data,
                    ErrorMessage = errorMessage
                };
                
                // 缓存30分钟
                CacheHelper.SetCache(cacheKey, result, 30);
                
                _logger.LogInformation($"保存最终结果: {taskId} -> Success: {success}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"保存最终结果失败: {taskId}");
            }
        }

        /// <summary>
        /// 获取阶段描述
        /// </summary>
        /// <param name="stage">阶段</param>
        /// <returns>描述</returns>
        private string GetStageDescription(AIFillStage stage)
        {
            return stage switch
            {
                AIFillStage.Starting => "开始处理",
                AIFillStage.DocumentSplitting => "文档分割中",
                AIFillStage.BasicInfoExtraction => "基础信息提取中",
                AIFillStage.NetworkMatching => "网络名称匹配中",
                AIFillStage.DetailInfoExtraction => "详细信息提取中",
                AIFillStage.ResultMerging => "结果合并中",
                AIFillStage.Completed => "处理完成",
                _ => "未知阶段"
            };
        }

        #endregion


        /// <summary>
        /// 简洁文档分割 - 基于验证结果的简化方案
        /// </summary>
        /// <param name="filePath">文档路径</param>
        /// <returns>分割结果</returns>
        public (bool Success, List<DocumentSection> Sections, string ErrorMessage) SplitDocumentIntoSections(string filePath)
        {
            try
            {
                _logger.LogInformation($"开始分割文档: {filePath}");

                // 提取完整文档文本（包含表格数据）
                var fullText = ExtractCompleteTextFromDocument(filePath);
                if (string.IsNullOrWhiteSpace(fullText))
                {
                    return (false, new List<DocumentSection>(), "无法提取文档内容");
                }

                // 查找分割点并提取内容
                var sections = ExtractSectionsSimple(fullText);
                
                _logger.LogInformation($"文档分割完成，共提取 {sections.Count} 个部分");
                return (true, sections, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"文档分割失败: {filePath}");
                return (false, new List<DocumentSection>(), ex.Message);
            }
        }

        /// <summary>
        /// 简洁提取四个部分 - 基于正则表达式的简单分割
        /// </summary>
        private List<DocumentSection> ExtractSectionsSimple(string fullText)
        {
            var sections = new List<DocumentSection>();
            
            try
            {
                // 查找三个关键标识符的位置
                var overviewIndex = FindSectionIndex(fullText, "变更操作概述");
                var preparationIndex = FindSectionIndex(fullText, "变更操作准备");  
                var implementationIndex = FindSectionIndex(fullText, "操作实施步骤");

                // 第一部分：开头到【变更操作概述】
                sections.Add(new DocumentSection
                {
                    SectionName = "开头到变更操作概述",
                    Content = ExtractTextByIndex(fullText, 0, overviewIndex),
                    IsFound = true,
                    MatchMethod = "文本分割"
                });

                // 第二部分：【变更操作概述】下的内容
                sections.Add(new DocumentSection
                {
                    SectionName = "变更操作概述",
                    Content = ExtractTextByIndex(fullText, overviewIndex, preparationIndex),
                    IsFound = overviewIndex >= 0,
                    MatchMethod = overviewIndex >= 0 ? "正则匹配" : "未找到"
                });

                // 第三部分：【变更操作准备】下的内容
                sections.Add(new DocumentSection
                {
                    SectionName = "变更操作准备", 
                    Content = ExtractTextByIndex(fullText, preparationIndex, implementationIndex),
                    IsFound = preparationIndex >= 0,
                    MatchMethod = preparationIndex >= 0 ? "正则匹配" : "未找到"
                });

                // 第四部分：【操作实施步骤】到结尾
                sections.Add(new DocumentSection
                {
                    SectionName = "操作实施步骤",
                    Content = ExtractTextByIndex(fullText, implementationIndex, fullText.Length),
                    IsFound = implementationIndex >= 0,
                    MatchMethod = implementationIndex >= 0 ? "正则匹配" : "未找到"
                });

                var foundCount = sections.Count(s => s.IsFound);
                _logger.LogInformation($"分割完成: 找到 {foundCount}/3 个标识符");
                
                return sections;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "简洁分割失败");
                return sections;
            }
        }

        /// <summary>
        /// 查找分割标识符位置 - 基于标题特征的精确匹配
        /// 要求：包含关键字 + 独立标题 + 字数小于10
        /// </summary>
        private int FindSectionIndex(string text, string sectionName)
        {
            try
            {
                // 按行分割文本，查找独立的标题行
                var lines = text.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                int currentPosition = 0;

                for (int i = 0; i < lines.Length; i++)
                {
                    var line = lines[i].Trim();
                    
                    // 跳过空行
                    if (string.IsNullOrWhiteSpace(line))
                    {
                        currentPosition += lines[i].Length + 1; // +1 for newline
                        continue;
                    }

                    // 检查是否包含关键字
                    if (line.Contains(sectionName, StringComparison.OrdinalIgnoreCase))
                    {
                        // 验证标题特征
                        if (IsValidSectionTitle(line, sectionName))
                        {
                            // 找到当前行在原文本中的位置
                            int lineStartIndex = text.IndexOf(line, currentPosition);
                            if (lineStartIndex >= 0)
                            {
                                _logger.LogInformation($"找到标题: {sectionName} -> '{line}' (位置: {lineStartIndex})");
                                return lineStartIndex;
                            }
                        }
                    }

                    currentPosition += lines[i].Length + 1; // +1 for newline
                }

                _logger.LogWarning($"未找到符合条件的标题: {sectionName}");
                return -1;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"查找标识符失败: {sectionName}");
                return -1;
            }
        }

        /// <summary>
        /// 提取完整的文档内容（包含表格数据）- 使用Spire.Doc
        /// </summary>
        private string ExtractCompleteTextFromDocument(string filePath)
        {
            try
            {
                _logger.LogInformation($"开始提取完整文档内容: {filePath}");

                if (!File.Exists(filePath))
                {
                    _logger.LogError($"文件不存在: {filePath}");
                    return string.Empty;
                }

                var document = new Spire.Doc.Document();
                document.LoadFromFile(filePath);

                var textBuilder = new StringBuilder();

                // 遍历文档的所有节
                foreach (Spire.Doc.Section section in document.Sections)
                {
                    // 遍历节中的所有段落和表格
                    foreach (var docObject in section.Body.ChildObjects)
                    {
                        if (docObject is Spire.Doc.Documents.Paragraph paragraph)
                        {
                            // 提取段落文本
                            var paragraphText = paragraph.Text?.Trim();
                            if (!string.IsNullOrWhiteSpace(paragraphText))
                            {
                                textBuilder.AppendLine(paragraphText);
                            }
                        }
                        else if (docObject is Spire.Doc.Table table)
                        {
                            // 提取表格内容
                            var tableText = ExtractTableText(table);
                            if (!string.IsNullOrWhiteSpace(tableText))
                            {
                                textBuilder.AppendLine(tableText);
                            }
                        }
                    }
                }

                var result = textBuilder.ToString();
                _logger.LogInformation($"文档内容提取完成，总长度: {result.Length}");
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"提取文档内容失败: {filePath}");
                
                // 备用方案：使用原有的简单提取方法
                try
                {
                    _logger.LogWarning("尝试备用提取方案");
                    return Common.Utility.WordHelper.ExtractTextFromWord(filePath) ?? string.Empty;
                }
                catch (Exception fallbackEx)
                {
                    _logger.LogError(fallbackEx, "备用提取方案也失败");
                    return string.Empty;
                }
            }
        }

        /// <summary>
        /// 提取表格文本内容
        /// </summary>
        private string ExtractTableText(Spire.Doc.Table table)
        {
            try
            {
                var tableBuilder = new StringBuilder();
                
                // 遍历表格的所有行
                foreach (Spire.Doc.TableRow row in table.Rows)
                {
                    var rowTexts = new List<string>();
                    
                    // 遍历行中的所有单元格
                    foreach (Spire.Doc.TableCell cell in row.Cells)
                    {
                        var cellText = ExtractCellText(cell);
                        rowTexts.Add(cellText);
                    }
                    
                    // 用制表符连接单元格内容
                    var rowText = string.Join("\t", rowTexts);
                    if (!string.IsNullOrWhiteSpace(rowText))
                    {
                        tableBuilder.AppendLine(rowText);
                    }
                }

                return tableBuilder.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "提取表格内容失败");
                return string.Empty;
            }
        }

        /// <summary>
        /// 提取单元格文本内容
        /// </summary>
        private string ExtractCellText(Spire.Doc.TableCell cell)
        {
            try
            {
                var cellBuilder = new StringBuilder();
                
                // 遍历单元格中的所有段落
                foreach (var cellObject in cell.ChildObjects)
                {
                    if (cellObject is Spire.Doc.Documents.Paragraph paragraph)
                    {
                        var paragraphText = paragraph.Text?.Trim();
                        if (!string.IsNullOrWhiteSpace(paragraphText))
                        {
                            cellBuilder.Append(paragraphText);
                            cellBuilder.Append(" "); // 段落间加空格
                        }
                    }
                }

                return cellBuilder.ToString().Trim();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "提取单元格内容失败");
                return string.Empty;
            }
        }

        /// <summary>
        /// 验证是否为有效的章节标题
        /// 简化逻辑：包含关键字 + 独立段落 + 不超过12字
        /// </summary>
        private bool IsValidSectionTitle(string line, string sectionName)
        {
            try
            {
                // 1. 字数限制：整行字符数不超过12个字符
                if (line.Length > 12)
                {
                    _logger.LogDebug($"标题长度超限: '{line}' (长度: {line.Length})");
                    return false;
                }

                // 2. 必须包含关键字
                if (!line.Contains(sectionName, StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogDebug($"不包含关键字: '{line}' (关键字: {sectionName})");
                    return false;
                }

                // 3. 简化验证：只要包含关键字且字数合理，就认为是有效标题
                // 这样可以支持各种可能的后缀：（重点）、（重要）、（详细）、说明 等等
                _logger.LogInformation($"标题验证通过: '{line}' (包含: {sectionName}, 长度: {line.Length})");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"标题验证异常: '{line}'");
                return false;
            }
        }

        /// <summary>
        /// 根据索引提取文本内容
        /// </summary>
        private string ExtractTextByIndex(string text, int startIndex, int endIndex)
        {
            try
            {
                if (startIndex < 0) startIndex = 0;
                if (endIndex < 0 || endIndex > text.Length) endIndex = text.Length;
                if (startIndex >= endIndex) return string.Empty;

                return text.Substring(startIndex, endIndex - startIndex).Trim();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"提取文本失败: {startIndex}-{endIndex}");
                return string.Empty;
            }
        }
    }

    /// <summary>
    /// 网络匹配结果
    /// </summary>
    public class NetworkMatchResult
    {
        /// <summary>
        /// 标准网络名称
        /// </summary>
        public string StandardNetworkName { get; set; }

        /// <summary>
        /// 匹配置信度（0-100）
        /// </summary>
        public int MatchConfidence { get; set; }
    }

    /// <summary>
    /// 基础信息DTO
    /// </summary>
    public class BasicInfoDto
    {
        /// <summary>
        /// 省份
        /// </summary>
        public string Province { get; set; }
        /// <summary>
        /// 地市
        /// </summary>
        public string City { get; set; }

        /// <summary>
        /// 网络名称
        /// </summary>
        public string NetworkName { get; set; }

        /// <summary>
        /// 工单类型ID
        /// </summary>
        public int OrderType { get; set; }

        /// <summary>
        /// 工单类型名称
        /// </summary>
        public string OrderTypeName { get; set; }
        /// <summary>
        /// 专业
        /// </summary>
        public string ProductSpe { get; set; }
    }

    /// <summary>
    /// 网络信息DTO
    /// </summary>
    public class NetworkInfoDto
    {
        /// <summary>
        /// 标准网络名称
        /// </summary>
        public string StandardNetworkName { get; set; }

        /// <summary>
        /// CodeSk
        /// </summary>
        public string CodeSk { get; set; }

        /// <summary>
        /// 部门信息
        /// </summary>
        public string DepName { get; set; }

        /// <summary>
        /// 匹配的可信度
        /// </summary>

        public int MatchResult { get; set; }
    }

    public class CustomNameDto
    {
        public string Email { get; set; }
        public string NickName { get; set; }
        public string PhoneNumber { get; set; }
    }

    public class ReportordersFormDto
    {
        public Netprovider NetProvider { get; set; }
        public EnumDisOrderType OrderType { get; set; }
        public string OutputLine { get; set; }
        public string ProductLine { get; set; }
        public List<string> ProductLMT { get; set; }//["OTN网管"]
        public string LineRemark { get; set; }
        public string RiskLevel { get; set; }
        public DateTime? PlanStartTime { get; set; }
        public DateTime? PlanEndTime { get; set; }
        public string Title { get; set; }
        public CustomNameDto CustomName { get; set; }
        public string CustomEmail { get; set; }
        public string CustomPhone { get; set; }
        public string CodeSk { get; set; }
        public string TaskContent { get; set; }
        public bool IsReinstated { get; set; }
        public string SoftwareForm { get; set; }//["网管全量/增量补丁"]
        public string UpgradeReason { get; set; }//["基线版本"]
        public bool IsActived { get; set; }
        public NetUserPosition ApplyUser { get; set; }
        public List<NetUserPosition> TaskUsers { get; set; }
        public List<NetUserPosition> CheckUsers { get; set; }
        public List<InvolveDeviceDto> InvolveDevices { get; set; }
        public List<InvolveNmpinfoDto> InvolveNmpinfos { get; set; }
        public List<InvolveProductDto> InvolveProducts { get; set; }
        public DateTime? LocalTime { get; set; }
        public List<InvolveUserDto> CustomUsers { get; set; }
        public List<NetUserPosition> CopyUsers { get; set; }
        public EnumUpgradeMode UpgradeMode { get; set; }
    }




    /// <summary>
    /// 文档分割结果
    /// </summary>
    public class DocumentSection
    {
        public string SectionName { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public int StartIndex { get; set; } = -1;
        public int EndIndex { get; set; } = -1;
        public bool IsFound { get; set; } = false;
        public string MatchMethod { get; set; } = string.Empty; // 匹配方式：格式+文本、纯文本、智能搜索
    }

    /// <summary>
    /// 部分处理结果
    /// </summary>
    public class PartialResult
    {
        public Dictionary<string, object> ExtractedFields { get; set; } = new Dictionary<string, object>();
        public bool Success { get; set; } = false;
        public string ErrorMessage { get; set; } = string.Empty;
        public string SectionName { get; set; } = string.Empty;
        public TimeSpan ProcessTime { get; set; } = TimeSpan.Zero;
    }

    /// <summary>
    /// AI填报进度信息
    /// </summary>
    public class AIFillProgressDto
    {
        /// <summary>
        /// 进度百分比 (0-100)
        /// </summary>
        public int Percentage { get; set; }
        
        /// <summary>
        /// 当前阶段文字描述
        /// </summary>
        public string CurrentStage { get; set; }
        
        /// <summary>
        /// 详细进度信息
        /// </summary>
        public string DetailMessage { get; set; }
        
        /// <summary>
        /// 是否已完成
        /// </summary>
        public bool IsCompleted { get; set; }
        
        /// <summary>
        /// 是否出错
        /// </summary>
        public bool HasError { get; set; }
        
        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        public string TaskId { get; set; }
    }

    /// <summary>
    /// AI填报进度阶段
    /// </summary>
    public enum AIFillStage
    {
        /// <summary>
        /// 开始处理 - 0%
        /// </summary>
        Starting = 0,
        
        /// <summary>
        /// 文档分割 - 10%
        /// </summary>
        DocumentSplitting = 10,
        
        /// <summary>
        /// 基础信息提取 - 30%
        /// </summary>
        BasicInfoExtraction = 30,
        
        /// <summary>
        /// 网络名称匹配 - 50%
        /// </summary>
        NetworkMatching = 50,
        
        /// <summary>
        /// 并发详细信息提取 - 70%
        /// </summary>
        DetailInfoExtraction = 70,
        
        /// <summary>
        /// 结果合并 - 90%
        /// </summary>
        ResultMerging = 90,
        
        /// <summary>
        /// 处理完成 - 100%
        /// </summary>
        Completed = 100
    }

    /// <summary>
    /// AI填报任务启动结果
    /// </summary>
    public class AIFillTaskStartResult
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public string TaskId { get; set; }
        
        /// <summary>
        /// 是否启动成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// AI填报最终结果
    /// </summary>
    public class AIFillFinalResult
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public string TaskId { get; set; }
        
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 结果数据
        /// </summary>
        public ReportordersDto Data { get; set; }
        
        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }
    }
}


