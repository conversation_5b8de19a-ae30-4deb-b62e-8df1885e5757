2025-08-13 10:36:00.0488 | Job qmd_qmo_jobgroup.escalatedTaskR<PERSON>ind<PERSON><PERSON> threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:36:00.0488 | Job qmd_qmo_jobgroup.syncExpireT<PERSON><PERSON><PERSON> threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:36:00.0488 | Job qmd_qmo_jobgroup.syncExpireT<PERSON><PERSON><PERSON> threw an exception. <PERSON> threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:36:00.0488 | Job qmd_qmo_jobgroup.escalatedTaskR<PERSON>ind<PERSON><PERSON> threw an exception. <PERSON> threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:38:00.0559 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:38:00.0809 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:38:00.0809 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:38:00.0809 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:40:00.1363 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:40:00.1363 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:40:00.1363 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:40:00.1363 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:40:00.1363 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:40:00.1363 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:40:00.1363 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:40:00.1363 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:40:00.1388 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:40:00.1388 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:40:00.2404 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 10:42:00.0720 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:42:00.0720 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:42:00.0720 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:42:00.0720 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:44:00.0761 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:44:00.0761 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:44:00.0782 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:44:00.0782 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:45:00.0425 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:45:00.0425 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:46:00.0786 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:46:00.0786 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:46:00.0786 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:46:00.0786 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:48:00.0840 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:48:00.0840 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:48:00.0840 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:48:00.0840 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:50:00.1129 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:50:00.1129 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:50:00.2091 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:50:00.2091 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:50:00.2091 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:50:00.2240 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:50:00.2240 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:50:00.2332 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:50:00.2332 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:50:00.2531 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:50:00.3191 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 10:52:00.0767 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:52:00.0767 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:52:00.1123 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:52:00.1123 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:54:00.0690 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:54:00.0827 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:54:00.0827 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:54:00.0827 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:55:00.0455 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:55:00.0455 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:56:00.0826 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:56:00.0826 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:56:00.0826 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:56:00.0826 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:58:00.1027 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:58:00.1027 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 10:58:00.1027 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 10:58:00.1027 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:00:00.2542 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:00:00.2791 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:00:00.2908 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeRecordJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:00:00.2908 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:00:00.2908 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:00:00.3129 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:00:00.3129 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:00:00.3129 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an unhandled Exception:  No service for type 'QMD.Job.Nmosp.SyncNmospUserInfoJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:00:00.3129 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:00:00.3129 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:00:00.3479 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:00:00.3479 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:00:00.3479 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:00:00.3600 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:00:00.4447 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 11:02:00.0814 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:02:00.0814 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:02:00.0814 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:02:00.0814 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:04:00.0796 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:04:00.0796 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:04:00.0796 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:04:00.0796 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:05:00.0437 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:05:00.0451 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:06:00.0772 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:06:00.1141 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:06:00.1141 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:06:00.1141 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:08:00.1160 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:08:00.1160 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:08:00.1160 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:08:00.1160 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:10:00.1363 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:10:00.1363 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:10:00.1619 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:10:00.1835 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:10:00.1835 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:10:00.1980 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:10:00.1980 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:10:00.1980 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:10:00.2079 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:10:00.2245 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:10:00.3554 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 11:12:00.0787 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:12:00.0787 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:12:00.0787 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:12:00.0787 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:14:00.0685 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:14:00.0829 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:14:00.0829 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:14:00.0829 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:15:00.0975 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:15:00.0975 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:16:00.0914 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:16:00.0914 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:16:00.0926 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:16:00.0926 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:18:00.0887 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:18:00.1030 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:18:00.1030 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:18:00.1030 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:20:00.0890 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:20:00.1332 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:20:00.1721 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:20:00.1721 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:20:00.1958 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:20:00.1958 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:20:00.2050 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:20:00.2050 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:20:00.2050 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:20:00.2050 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:20:00.3079 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 11:22:00.0738 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:22:00.0738 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:22:00.0738 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:22:00.0738 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 11:22:51.2371 | Job qmd_qa_jobgroup.syncQANetJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncQaInfoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:22:51.2371 | Job qmd_qmo_jobgroup.syncNoticeTaskResultJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskResultJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:22:51.2371 | Job qmd_itr_jobgroup.syncDingtalkUserJob threw an unhandled Exception:  No service for type 'QMD.Job.Dingtalk.SyncDingtalkUserJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:22:51.2371 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:22:51.2371 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an unhandled Exception:  No service for type 'QMD.Job.Nmosp.SyncNmospUserInfoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:22:51.2371 | Job qmd_qmo_jobgroup.syncNoticeTaskTodoJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskTodoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:22:51.2371 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:22:51.2371 | Job qmd_qa_jobgroup.syncQANetJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:22:51.2371 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:22:51.2371 | Job qmd_itr_jobgroup.syncDingtalkUserJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:22:51.2371 | Job qmd_qmo_jobgroup.syncNoticeTaskTodoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:22:51.2371 | Job qmd_qmo_jobgroup.syncNoticeTaskResultJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:22:52.8429 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 11:24:00.0485 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:24:00.0485 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:24:00.0485 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:24:00.0485 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:25:00.0467 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:25:00.0467 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:25:15.0913 | API请求失败: Unauthorized, 内容: {"error":{"message":"Incorrect API key provided. ","type":"invalid_request_error","param":null,"code":"invalid_api_key"},"request_id":"43340a4e-e148-935d-b190-e1b58f2703d6"} 
2025-08-13 11:25:15.0994 | 调用大模型API失败: Unauthorized, {"error":{"message":"Incorrect API key provided. ","type":"invalid_request_error","param":null,"code":"invalid_api_key"},"request_id":"43340a4e-e148-935d-b190-e1b58f2703d6"} 
2025-08-13 11:25:57.8301 | API请求失败: Unauthorized, 内容: {"error":{"message":"Incorrect API key provided. ","type":"invalid_request_error","param":null,"code":"invalid_api_key"},"request_id":"124cb84f-e24f-9d5f-aa65-db12789a52a9"} 
2025-08-13 11:25:57.8431 | 调用大模型API失败: Unauthorized, {"error":{"message":"Incorrect API key provided. ","type":"invalid_request_error","param":null,"code":"invalid_api_key"},"request_id":"124cb84f-e24f-9d5f-aa65-db12789a52a9"} 
2025-08-13 11:26:44.8750 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:26:45.9436 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:26:45.9565 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:26:45.9565 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:28:08.0725 | Job qmd_itr_jobgroup.syncDingtalkUserJob threw an unhandled Exception:  No service for type 'QMD.Job.Dingtalk.SyncDingtalkUserJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:28:08.0725 | Job qmd_qmo_jobgroup.syncNoticeTaskResultJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskResultJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:28:08.0725 | Job qmd_qmo_jobgroup.syncNoticeTaskTodoJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskTodoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:28:08.0725 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an unhandled Exception:  No service for type 'QMD.Job.Nmosp.SyncNmospUserInfoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:28:08.0725 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:28:08.0725 | Job qmd_qa_jobgroup.syncQANetJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncQaInfoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:28:08.0953 | Job qmd_qmo_jobgroup.syncNoticeTaskResultJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:28:08.0953 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:28:08.0953 | Job qmd_qa_jobgroup.syncQANetJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:28:08.0953 | Job qmd_qmo_jobgroup.syncNoticeTaskTodoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:28:08.0953 | Job qmd_itr_jobgroup.syncDingtalkUserJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:28:08.0953 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:28:10.0265 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 11:29:32.1061 | [GetWhyIINetproviders]YYY：Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization 
 ExecutionContext.RunInternal => AsyncStateMachineBox`1.ExecutionContextCallback => <GetWhyIINetOrganization>d__38.MoveNext 

2025-08-13 11:29:32.1481 | [GetWhyIINetproviders]同步维护域网络组织机构数据发生异常：Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization 
 ExecutionContext.RunInternal => AsyncStateMachineBox`1.ExecutionContextCallback => <GetWhyIINetOrganization>d__38.MoveNext 

2025-08-13 11:30:00.0411 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:30:00.0878 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeRecordJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:30:00.0878 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:30:00.0947 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:30:00.0947 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:30:00.1351 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:30:00.1351 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:30:00.1760 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:30:00.1760 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:30:00.1760 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:30:00.2155 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:30:00.2177 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:30:00.2599 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 11:32:07.3862 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:32:07.3862 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:32:09.3000 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:32:09.3378 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:34:11.3033 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:34:11.3145 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:34:11.3145 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:34:11.3273 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:37:50.8678 | Job qmd_qmo_jobgroup.syncNoticeTaskTodoJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskTodoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:37:50.8678 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an unhandled Exception:  No service for type 'QMD.Job.Nmosp.SyncNmospUserInfoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:37:50.8678 | Job qmd_qa_jobgroup.syncQANetJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncQaInfoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:37:50.8678 | Job qmd_qmo_jobgroup.syncNoticeTaskResultJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskResultJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:37:50.8678 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:37:50.8678 | Job qmd_itr_jobgroup.syncDingtalkUserJob threw an unhandled Exception:  No service for type 'QMD.Job.Dingtalk.SyncDingtalkUserJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:37:50.8678 | Job qmd_qmo_jobgroup.syncNoticeTaskResultJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:37:50.8678 | Job qmd_qa_jobgroup.syncQANetJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:37:50.8678 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:37:50.8678 | Job qmd_qmo_jobgroup.syncNoticeTaskTodoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:37:50.8678 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:37:50.8737 | Job qmd_itr_jobgroup.syncDingtalkUserJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:37:52.3093 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 11:38:00.0374 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:38:00.0374 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:38:00.0374 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:38:00.0374 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:40:00.0618 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:40:00.0618 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:40:00.0964 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:40:00.0964 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:40:00.0964 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:40:00.0964 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:40:00.0964 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:40:00.1116 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:40:00.1116 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 11:40:00.1238 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:40:00.2027 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 11:42:00.0724 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:42:00.0724 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:42:00.0724 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:42:00.0724 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:44:00.0427 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:44:00.0427 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:44:00.0427 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:44:00.0427 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:47:32.2634 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:47:32.2634 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:47:33.2924 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:47:33.2924 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:47:33.3297 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:47:33.7059 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:49:34.4703 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:49:34.4703 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 11:49:34.4848 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 11:49:34.4848 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 13:34:32.8989 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:34:32.8989 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:34:32.9118 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 13:34:32.9118 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:34:32.9118 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 13:34:32.9268 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeRecordJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:34:32.9268 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:34:32.9268 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 13:34:32.9443 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:34:32.9443 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 13:34:32.9443 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an unhandled Exception:  No service for type 'QMD.Job.Nmosp.SyncNmospUserInfoJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:34:32.9443 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 13:34:32.9443 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 13:34:32.9608 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:34:32.9608 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 13:34:32.9776 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 13:34:46.3127 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 13:35:00.0130 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:35:00.0162 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 13:36:01.2922 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:36:01.2922 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:36:01.2922 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 13:36:01.2922 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 13:36:29.7505 | [GetWhyIINetproviders]YYY：Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization 
 _IOCompletionCallback.PerformIOCompletionCallback => AwaitableSocketAsyncEventArgs.OnCompleted => AwaitableSocketAsyncEventArgs.InvokeContinuation 

2025-08-13 13:36:29.7995 | [GetWhyIINetproviders]同步维护域网络组织机构数据发生异常：Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization 
 _IOCompletionCallback.PerformIOCompletionCallback => AwaitableSocketAsyncEventArgs.OnCompleted => AwaitableSocketAsyncEventArgs.InvokeContinuation 

2025-08-13 13:37:16.0811 | [GetWhyIINetproviders]YYY：Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization 
 _IOCompletionCallback.PerformIOCompletionCallback => AwaitableSocketAsyncEventArgs.OnCompleted => AwaitableSocketAsyncEventArgs.InvokeContinuation 

2025-08-13 13:37:16.1283 | [GetWhyIINetproviders]同步维护域网络组织机构数据发生异常：Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization 
 _IOCompletionCallback.PerformIOCompletionCallback => AwaitableSocketAsyncEventArgs.OnCompleted => AwaitableSocketAsyncEventArgs.InvokeContinuation 

2025-08-13 13:38:00.0288 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:38:00.0716 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:38:00.0716 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 13:38:00.0716 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 13:40:00.1291 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:40:00.1391 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:40:00.1391 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 13:40:00.1391 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:40:00.1775 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:40:00.1775 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:40:00.1775 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 13:40:00.1906 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 13:40:00.2052 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 13:40:00.2318 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 13:40:00.2362 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 13:42:00.0416 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:42:00.0416 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:42:00.0454 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 13:42:00.0454 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 13:44:00.0335 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:44:00.0703 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 13:44:00.0703 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:44:00.0703 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 13:45:00.0305 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:45:00.0305 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 13:46:00.0243 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:46:00.0548 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 13:46:00.0548 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 13:46:00.0548 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-13 14:47:18.7923 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 14:47:18.7923 | Job qmd_itr_jobgroup.syncDingtalkUserJob threw an unhandled Exception:  No service for type 'QMD.Job.Dingtalk.SyncDingtalkUserJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 14:47:18.7923 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an unhandled Exception:  No service for type 'QMD.Job.Nmosp.SyncNmospUserInfoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 14:47:18.7923 | Job qmd_qmo_jobgroup.syncNoticeTaskResultJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskResultJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 14:47:18.7923 | Job qmd_qmo_jobgroup.syncNoticeTaskTodoJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskTodoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 14:47:18.7923 | Job qmd_qa_jobgroup.syncQANetJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncQaInfoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 14:47:18.8008 | Job qmd_qa_jobgroup.syncQANetJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:47:18.8008 | Job qmd_itr_jobgroup.syncDingtalkUserJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:47:18.8008 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:47:18.8008 | Job qmd_qmo_jobgroup.syncNoticeTaskTodoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:47:18.8008 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:47:18.8008 | Job qmd_qmo_jobgroup.syncNoticeTaskResultJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:47:20.1330 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 14:48:55.1697 | Job qmd_qa_jobgroup.syncQANetJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncQaInfoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 14:48:55.1697 | Job qmd_qmo_jobgroup.syncNoticeTaskTodoJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskTodoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 14:48:55.1697 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 14:48:55.1697 | Job qmd_qmo_jobgroup.syncNoticeTaskResultJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskResultJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 14:48:55.1697 | Job qmd_itr_jobgroup.syncDingtalkUserJob threw an unhandled Exception:  No service for type 'QMD.Job.Dingtalk.SyncDingtalkUserJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 14:48:55.1697 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an unhandled Exception:  No service for type 'QMD.Job.Nmosp.SyncNmospUserInfoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 14:48:55.1697 | Job qmd_qmo_jobgroup.syncNoticeTaskResultJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:48:55.1697 | Job qmd_qmo_jobgroup.syncNoticeTaskTodoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:48:55.1697 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:48:55.1697 | Job qmd_qa_jobgroup.syncQANetJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:48:55.1697 | Job qmd_itr_jobgroup.syncDingtalkUserJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:48:55.1697 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:48:56.3931 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 14:51:11.1226 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 14:51:11.1288 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 14:51:11.1288 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:51:11.1288 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:51:11.4628 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 14:51:11.4628 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 14:51:11.4698 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:51:11.4698 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 14:51:11.4698 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:51:11.4835 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:51:18.6053 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 14:51:18.7304 | 从docx文件提取文本失败: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-传输网管-V3.0-20240802.docx A shared part is referenced by multiple source parts with a different relationship type. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__35.MoveNext => AIServices.ExtractTextFromDocx 

2025-08-13 14:52:18.0242 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 14:52:18.0242 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-13 14:52:18.0242 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:52:18.0346 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:53:39.8931 | 从docx文件提取文本失败: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-路由器交换机-V3.0-20240802 (1).docx A shared part is referenced by multiple source parts with a different relationship type. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__35.MoveNext => AIServices.ExtractTextFromDocx 

2025-08-13 14:53:39.9367 | 从docx文件提取文本失败: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-终端-V3.0-20240802 (1).docx A shared part is referenced by multiple source parts with a different relationship type. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__35.MoveNext => AIServices.ExtractTextFromDocx 

2025-08-13 14:53:39.9804 | 从docx文件提取文本失败: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-CDN-V3.0-20240802 (1).docx A shared part is referenced by multiple source parts with a different relationship type. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__35.MoveNext => AIServices.ExtractTextFromDocx 

2025-08-13 14:53:40.3044 | 从docx文件提取文本失败: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PON-V3.0-20240802.docx A shared part is referenced by multiple source parts with a different relationship type. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__35.MoveNext => AIServices.ExtractTextFromDocx 

2025-08-13 14:53:40.3485 | 从docx文件提取文本失败: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PTN&SPN-V3.0-20240802.docx A shared part is referenced by multiple source parts with a different relationship type. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__35.MoveNext => AIServices.ExtractTextFromDocx 

2025-08-13 14:53:40.3852 | 从docx文件提取文本失败: D:\Tempfiles\XX月XX日0：00-04：00XX网络XX操作网络变更操作技术方案（模板）-OTN-V3.0-20240802.docx A shared part is referenced by multiple source parts with a different relationship type. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__35.MoveNext => AIServices.ExtractTextFromDocx 

2025-08-13 14:53:40.4233 | 验证模板文件失败 Could not find a part of the path 'D:\Code\nmosphub\QMD.Web\bin\Debug\net5.0\AI\模板验证结果.txt'. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__4.MoveNext => AIServices.ValidateTemplateFilesAsync 

2025-08-13 14:54:00.0403 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 14:54:00.0403 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 14:54:00.0403 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:54:00.0403 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:55:00.0250 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 14:55:00.0250 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:55:49.4497 | 从docx文件提取文本失败: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-传输网管-V3.0-20240802.docx A shared part is referenced by multiple source parts with a different relationship type. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__35.MoveNext => AIServices.ExtractTextFromDocx 

2025-08-13 14:55:49.4871 | 从docx文件提取文本失败: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-服务器存储-V3.0-20240802.docx A shared part is referenced by multiple source parts with a different relationship type. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__35.MoveNext => AIServices.ExtractTextFromDocx 

2025-08-13 14:55:49.5263 | 从docx文件提取文本失败: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-路由器交换机-V3.0-20240802 (1).docx A shared part is referenced by multiple source parts with a different relationship type. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__35.MoveNext => AIServices.ExtractTextFromDocx 

2025-08-13 14:55:49.5635 | 从docx文件提取文本失败: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-终端-V3.0-20240802 (1).docx A shared part is referenced by multiple source parts with a different relationship type. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__35.MoveNext => AIServices.ExtractTextFromDocx 

2025-08-13 14:55:49.6046 | 从docx文件提取文本失败: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-CDN-V3.0-20240802 (1).docx A shared part is referenced by multiple source parts with a different relationship type. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__35.MoveNext => AIServices.ExtractTextFromDocx 

2025-08-13 14:55:49.7214 | 从docx文件提取文本失败: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PON-V3.0-20240802.docx A shared part is referenced by multiple source parts with a different relationship type. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__35.MoveNext => AIServices.ExtractTextFromDocx 

2025-08-13 14:55:49.7558 | 从docx文件提取文本失败: D:\Tempfiles\XX月XX日0：00-03：00XX网络XX操作网络变更操作技术方案（模板）-PTN&SPN-V3.0-20240802.docx A shared part is referenced by multiple source parts with a different relationship type. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__35.MoveNext => AIServices.ExtractTextFromDocx 

2025-08-13 14:55:49.7913 | 从docx文件提取文本失败: D:\Tempfiles\XX月XX日0：00-04：00XX网络XX操作网络变更操作技术方案（模板）-OTN-V3.0-20240802.docx A shared part is referenced by multiple source parts with a different relationship type. 
 AsyncMethodBuilderCore.Start => <ValidateTemplateFilesAsync>d__35.MoveNext => AIServices.ExtractTextFromDocx 

2025-08-13 14:56:00.0407 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 14:56:00.0407 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 14:56:00.0407 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:56:00.0407 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:58:00.0303 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 14:58:00.0786 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 14:58:00.0786 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 14:58:00.0786 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:00:00.0541 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:00:00.0716 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:00:00.1183 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an unhandled Exception:  No service for type 'QMD.Job.Nmosp.SyncNmospUserInfoJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:00:00.1183 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeRecordJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:00:00.1313 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:00:00.1313 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:00:00.1596 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:00:00.1596 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:00:00.1596 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:00:00.1596 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:00:00.1596 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:00:00.1596 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:00:00.1716 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:00:00.1716 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:00:00.2482 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-13 15:02:00.0336 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:02:00.0336 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:02:00.0336 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:02:00.0336 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:04:01.4428 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:04:01.4539 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-13 15:04:01.5025 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-13 15:04:01.5025 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

