2025-08-14 08:40:53.6797 | 🔄 开始文档分割 
2025-08-14 08:40:55.2052 | 开始分割文档: C:\Users\<USER>\AppData\Local\Temp\QMDAITemp\0071dad8-fc44-484b-80b0-7c3ef7434680.docx 
2025-08-14 08:40:55.3246 | 开始提取完整文档内容: C:\Users\<USER>\AppData\Local\Temp\QMDAITemp\0071dad8-fc44-484b-80b0-7c3ef7434680.docx 
2025-08-14 08:40:55.4902 | [2025-08-14 08:40:55]定时任务SyncReadEmailJob执行失败：False 
2025-08-14 08:40:55.5367 | 文档内容提取完成，总长度: 6267 
2025-08-14 08:41:10.4114 | 标题验证通过: '变更操作概述' (包含: 变更操作概述, 长度: 6) 
2025-08-14 08:41:10.4140 | 找到标题: 变更操作概述 -> '变更操作概述' (位置: 423) 
2025-08-14 08:41:10.4140 | 标题验证通过: '变更操作准备' (包含: 变更操作准备, 长度: 6) 
2025-08-14 08:41:10.4140 | 找到标题: 变更操作准备 -> '变更操作准备' (位置: 3762) 
2025-08-14 08:41:10.4140 | 标题验证通过: '操作实施步骤（重点）' (包含: 操作实施步骤, 长度: 10) 
2025-08-14 08:41:10.4140 | 找到标题: 操作实施步骤 -> '操作实施步骤（重点）' (位置: 4938) 
2025-08-14 08:41:10.4140 | 分割完成: 找到 4/3 个标识符 
2025-08-14 08:42:16.6223 | 文档分割完成，共提取 4 个部分 
2025-08-14 08:42:24.5022 | ✅ 文档分割完成，找到 4/3 个标识符，耗时: 88060.1045ms 
2025-08-14 08:42:28.2086 | 🔄 第一阶段开始：基础信息提取 
2025-08-14 08:42:37.4929 | 开始提取基础信息（省份、网络名称、工单类型、专业） 
2025-08-14 08:43:01.8738 | 文本优化：原始长度 5364，优化后长度 5306，节省 58 个字符 (1.08%) 
2025-08-14 08:43:02.4857 | 请求体: {"model":"qwen-plus","messages":[{"role":"system","content":"你是一个专业的QMO工单解析助手，擅长从文档中提取省份、网络名称和工单类型等基础信息。请严格按照标准列表进行匹配。"},{"role":"user","content":"请仔细阅读以下文档内容，并从中提取省份、网络名称和工单类型信息。 注意： 1. 省份必须严格从以下标准省份列表中匹配： [\"广东省\",\"湖北省\",\"湖南省\",\"西藏自治区\",\"黑龙江省\",\"甘肃省\",\"河南省\",\"北京市\",\"辽宁省\",\"广西壮族自治区\",\"江苏省\",\"山西省\",\"河北省\",\"吉林省\",\"智利\",\"四川省\",\"印度尼西亚\",\"山东省\",\"新疆维吾尔自治区\",\"云南省\",\"上海市\",\"重庆市\",\"浙江省\",\"安徽省\",\"内蒙古自治区\",\"江西省\",\"阿尔及利亚\",\"陕西省\",\"泰国\",\"贵州省\",\"肯尼亚\",\"越南\",\"青海省\",\"宁夏回族自治区\",\"巴西\",\"新加坡\",\"匈牙利\",\"印度\",\"罗马尼亚\",\"阿曼\",\"菲律宾\",\"西班牙\",\"葡萄牙\",\"天津市\",\"福建省\",\"马来西亚\",\"墨西哥\",\"伊拉克\",\"孟加拉国\",\"海南省\",\"约旦\",\"伊朗\",\"缅甸\",\"哥伦比亚\",\"亚美尼亚\",\"巴勒斯坦\",\"巴基斯坦\",\"厄瓜多尔\",\"也门\",\"南非\",\"摩洛哥\",\"GM\",\"阿根廷\",\"香港\",\"多米尼加\",\"波兰\",\"虚拟省\",\"WG\",\"土耳其\",\"蒙古\",\"乌克兰\",\"加拿大\",\"尼日利亚\",\"赤道几内亚\",\"塞浦路斯\",\"埃及\",\"乌干达\",\"津巴布韦\",\"老挝\"] 2. 地市必须严格从以下标准省份列表中匹配： [\"揭阳市\",\"武汉市\",\"长沙市\",\"拉萨市\",\"绥化市\",\"安阳市\",\"北京市\",\"珠海市\",\"江门市\",\"锦州市\",\"百色市\",\"南京市\",\"晋城市\",\"沧州市\",\"长春市\",\"哈尔滨市\",\"黄石市\",\"邵阳市\",\"圣地亚哥\",\"东莞市\",\"淮安市\",\"成都市\",\"潮州市\",\"Jakarta Raya\",\"鄂州市\",\"黑河市\",\"青岛市\",\"贺州地区\",\"德阳市\",\"兰州市\",\"常州市\",\"哈密地区\",\"梅州市\",\"徐州市\",\"唐山市\",\"曲靖市\",\"咸宁市\",\"上海市\",\"重庆市\",\"杭州市\",\"南通市\",\"盐城市\",\"十堰市\",\"南充市\",\"白银市\",\"淄博市\",\"平凉市\",\"济宁市\",\"石河子市\",\"佳木斯市\",\"太原市\",\"甘孜藏族自治州\",\"哈密市\",\"南宁市\",\"渝北区\",\"石家庄市\",\"孝感市\",\"合肥市\",\"平顶山市\",\"阳泉市\",\"西城区\",\"东爪哇\",\"抚顺市\",\"鸡西市\",\"黄山市\",\"包头市\",\"衡水市\",\"金昌市\",\"商丘市\",\"株洲市\",\"菏泽市\",\"塔城地区\",\"驻马店市\",\"运城市\",\"茂名市\",\"南昌市\",\"济源市\",\"阿尔及尔\",\"直辖行政单位\",\"眉山市\",\"西安市\",\"曼谷\",\"安顺市\",\"广州市\",\"延安市\",\"滁州市\",\"黄冈市\",\"洛阳市\",\"忻州市\",\"玉林市\",\"内罗毕\",\"新余市\",\"甘南藏族自治州\",\"河内\",\"西宁市\",\"清远市\",\"德州市\",\"邯郸市\",\"济南市\",\"日喀则地区\",\"比奥比奥大区\",\"郑州市\",\"张家口市\",\"图木舒克市\",\"漯河市\",\"银川市\",\"湖州市\",\"贵阳市\",\"荆门市\",\"肇庆市\",\"湘潭市\",\"丹东市\",\"克拉玛依市\",\"乐山市\",\"Minas Gerais\",\"Singapore\",\"张家界市\",\"汕尾市\",\"扬州市\",\"杰尔-莫松-肖普朗\",\"玉溪市\",\"盘锦市\",\"石嘴山市\",\"保定市\",\"襄阳市\",\"Maharashtra\",\"大庆市\",\"娄底市\",\"巴中市\",\"烟台市\",\"攀枝花市\",\"沈阳市\",\"呼和浩特市\",\"泰安市\",\"林芝地区\",\"宜昌市\",\"布加勒斯特\",\"昌都地区\",\"昆明市\",\"临沧市\",\"亳州市\",\"Rio de Janeiro\",\"Shamal al Batinah\",\"萍乡市\",\"鞍山市\",\"随州市\",\"朝阳市\",\"汕头市\",\"大雅加达首都特区\",\"宁波市\",\"湛江市\",\"西双版纳傣族自治州\",\"佛山市\",\"临夏回族自治州\",\"河源市\",\"信阳市\",\"Jawa Barat\",\"山南地区\",\"Cavite\",\"保山市\",\"巴塞罗那\",\"里斯本\",\"葫芦岛市\",\"郴州市\",\"安庆市\",\"资阳市\",\"临汾市\",\"NCR\",\"阳江市\",\"河东区\",\"铜陵市\",\"鄂尔多斯市\",\"六盘水市\",\"秦皇岛市\",\"乌鲁木齐市\",\"漳州市\",\"晋中市\",\"嘉兴市\",\"鹤壁市\",\"吉隆坡\",\"抚州市\",\"铜仁地区\",\"里约热内卢\",\"鹤岗市\",\"苏州市\",\"墨西哥城\",\"益阳市\",\"中山市\",\"深圳市\",\"福州市\",\"芜湖市\",\"桂林市\",\"仙桃市\",\"Al Anbar\",\"枣庄市\",\"内江市\",\"云浮市\",\"天门市\",\"廊坊市\",\"Baghdad\",\"绍兴市\",\"达卡\",\"雄安新区\",\"陇南市\",\"海口市\",\"马斯喀特省\",\"延边朝鲜族自治州\",\"金华市\",\"滨州市\",\"Region Metropolitana de Santiago\",\"梧州市\",\"七台河市\",\"新德里\",\"六安市\",\"雅加达\",\"许昌市\",\"固原市\",\"长治市\",\"喀什地区\",\"张掖市\",\"文山壮族苗族自治州\",\"牡丹江市\",\"伊春市\",\"本溪市\",\"Ma'an\",\"淮北市\",\"昌吉回族自治州\",\"河池地区\",\"达州市\",\"伊犁哈萨克自治州\",\"酒泉市\",\"National Capital Region\",\"丽江市\",\"白山市\",\"阜新市\",\"宣城市\",\"蚌埠市\",\"宜春市\",\"Tehran\",\"大兴安岭地区\",\"仰光省\",\"大连市\",\"聊城市\",\"阿里地区\",\"凉山彝族自治州\",\"三门峡市\",\"吕梁市\",\"双鸭山市\",\"昌平区\",\"泰州市\",\"天津市\",\"铁岭市\",\"泉州市\",\"玉树藏族自治州\",\"龙岩市\",\"波哥大首都区\",\"惠州市\",\"河池市\",\"埃里温市\",\"瓜达拉哈拉\",\"白城市\",\"宿州市\",\"那曲地区\",\"莱芜市\",\"沙捞越\",\"营口市\",\"神农架林区\",\"西岸\",\"文昌市\",\"濮阳市\",\"镇江市\",\"临沂市\",\"卡拉奇\",\"考卡\",\"金山区\",\"伊斯兰堡\",\"贵安新区\",\"皮钦查\",\"邢台市\",\"萨那\",\"荆州市\",\"赣州市\",\"咸阳市\",\"连云港市\",\"新乡市\",\"Karnataka\",\"果洛藏族自治州\",\"南阳市\",\"中卫市\",\"三明市\",\"东伦敦\",\"淮南市\",\"开封市\",\"乌兰察布市\",\"定西市\",\"阿坝藏族羌族自治州\",\"韶关市\",\"汉中市\",\"Rabat-Sale-Zemmour-Zaer\",\"怀化市\",\"Esfahan\",\"通化市\",\"嘉峪关市\",\"布宜诺斯艾利斯\",\"无锡市\",\"吐鲁番市\",\"普洱市\",\"塔拉帕卡大区\",\"岳阳市\",\"大理白族自治州\",\"九江市\",\"庆阳市\",\"吴忠市\",\"贺州市\",\"博尔塔拉蒙古自治州\",\"鹰潭市\",\"威海市\",\"雅安市\",\"乌海市\",\"呼伦贝尔市\",\"阿勒泰地区\",\"和田地区\",\"辽源市\",\"通辽市\",\"赤峰市\",\"马鞍山市\",\"铜川市\",\"丽水市\",\"承德市\",\"广元市\",\"潜江市\",\"安康市\",\"阿拉善盟\",\"安曼\",\"香港\",\"北碚区\",\"舟山市\",\"潍坊市\",\"怒江傈僳族自治州\",\"Distrito Nacional\",\"兴安盟\",\"Grand Casablanca\",\"钦州市\",\"华沙\",\"北海市\",\"朔州市\",\"厦门市\",\"萨拉戈萨\",\"绵阳市\",\"辽阳市\",\"自贡市\",\"日照市\",\"吉安市\",\"池州市\",\"虚拟市\",\"景德镇市\",\"阜阳市\",\"海南藏族自治州\",\"恩施土家族苗族自治州\",\"红河哈尼族彝族自治州\",\"上饶市\",\"大同市\",\"宝鸡市\",\"焦作市\",\"崇左市\",\"Moskva\",\"马拉加\",\"伊斯坦布尔\",\"来宾市\",\"东营市\",\"南戈壁\",\"湘西土家族苗族自治州\",\"遵义市\",\"海东地区\",\"黄南藏族自治州\",\"基辅\",\"Valle del Cauca\",\"麦德林\",\"Ontario\",\"柳州市\",\"黄浦区\",\"巴音郭楞蒙古自治州\",\"克孜勒苏柯尔克孜自治州\",\"瓜亚斯\",\"齐齐哈尔市\",\"商洛市\",\"Alberta\",\"阿比亚\",\"清迈\",\"衢州市\",\"防城港市\",\"天水市\",\"温州市\",\"Sindh\",\"台州市\",\"西青区\",\"南平市\",\"巴彦淖尔市\",\"Madrid, Comunidad de\",\"榆林市\",\"黔西南布依族苗族自治州\",\"渭南市\",\"泸州市\",\"Sao Paulo\",\"重庆高新技术产业开发\",\"Annobon\",\"锡林郭勒盟\",\"衡阳市\",\"四平市\",\"河南省直辖县级行政区划\",\"宜宾市\",\"宿迁市\",\"Lefkosia\",\"Masqat\",\"布达佩斯\",\"周口市\",\"塞阿拉\",\"广安市\",\"静安区\",\"开罗\",\"昭通市\",\"永州市\",\"常德市\",\"Sulawesi Selatan\",\"贵港市\",\"南开区\",\"基多\",\"沙巴\",\"浦东新区\",\"莫斯科\",\"坎帕拉\",\"黔南布依族苗族自治州\",\"宁河区\",\"百色地区\",\"海淀区\",\"Jawa Timur\",\"哈拉雷\",\"朝阳区\",\"毕节地区\",\"Punjab\",\"班加罗尔\",\"松原市\",\"黔东南苗族侗族自治州\",\"海西蒙古族藏族自治州\",\"门多萨\",\"阿克苏地区\",\"Abuja Federal Capital Territory\",\"Al 'Aqabah\",\"Buryatiya, Respublika\",\"铜梁区\",\"安卡拉\",\"万象\",\"江津区\",\"楚雄彝族自治州\",\"大兴区\",\"武威市\",\"毕节市\",\"卡萨布兰卡\"] 2. 工单类型必须严格从以下标准工单类型列表中匹配（每个工单类型包含id和name字段）： [{\"id\":1,\"name\":\"割接\"},{\"id\":4,\"name\":\"网络整改\"},{\"id\":5,\"name\":\"网络检查\"},{\"id\":7,\"name\":\"升级\"},{\"id\":8,\"name\":\"测试\"}] 举例说明： - 如果文档标题为\"XXX网络升级割接方案\"，且版本信息板块中明确提到目标版本为\"v1.0\"，则工单类型应识别为\"升级\"，因为满足最高优先级。 - 如果文档标题为\"XXX网络割接方案\"，且版本信息板块中没有明确提到目标版本，工单类型也应识别为\"割接\"。 - 如果文档标题为\"XXX网络变更操作技术方案\"，但操作简述中明确提到\"割接实施窗口时间\"，且版本信息板块中没有明确提到目标版本,工单类型应识别为\"割接\"。 3. 工单类型识别优先级： - 最高优先级：文档版本信息板块中明确的网管目标版本或设备的目标版本（表示工单类型为\"升级\"），否则是其他类型 - 次高优先级：文档标题中明确的工单类型（如\"XXX割接方案\"表示工单类型为\"割接\"） - 再次优先级：操作简述部分明确提到的工单类型 - 最低优先级：文档其他部分提到的工单类型 4. 网络名称应尽可能准确提取，主要从文件名、标题或其他总结性文字中提取，用于后续匹配标准网络名称,网络名称注意提取完整，尽量把整句话提取出来。 5. 专业必须严格从以下标准专业中匹配： [\"MSTP\",\"服务器存储\",\"OTN\",\"接入\",\"PTN\",\"IPRAN\",\"交换机路由器\",\"SPN\",\"智网\",\"终端\",\"其他\",\"CDN\",\"STN\",\"外购件（第三方）\",\"opticalFiber光纤\"] 特别说明： - 如果文档中出现PON或者OLT,属于接入专业。 文档内容： 网络变更操作技术方案-PON 漯河移动运营商PON网络 业务割接方案 河南省技服中心 时间：2025年7月10日 对3级及以上风险要求提供变更操作方案，4级风险由本地技服根据实情自行要求。 所有项目不允许裁剪，当本次操作不涉及时，可以描述为不涉及。 三级及以上风险网络变更需要到技服技术支撑部TAC中心审核，需提前24小时报备；二级风险网络变更需要到产出线工程部审核，需提前48小时报备；一级风险网络变更需要到研发LMT审核，需提前72小时报备。同时，设置有三级（紧急）、二级（紧急）、一级（紧急）三个风险等级，此等级报备无需时间限制。对于一、二、三级网络变更操作和特殊时期（例如：集采期、投标、重大保障期、高访、集团网络巡检等）所有等级操作均需报备销售认可。 销售审核意见： 销售审核人 审核意见 周浩天 通过 省技服审核意见 省技服人员 审核人 审核意见 网络负责人 何俊辉 通过 技术主管 张赢彪 通过 请按照以下JSON格式返回提取结果： { \"province\": \"省份名称（必须从标准列表中匹配）\", \"city\":\"地市名称（必须从标准列表中匹配）\", \"networkName\": \"网络名称\", \"orderType\": 工单类型ID（必须从标准列表中匹配）, \"orderTypeName\": \"工单类型名称（必须与标准列表中id对应的name完全一致）\", \"productSpe\": \"IPRAN\" }"}],"temperature":0.7,"stream":false} 
2025-08-14 08:43:02.5328 | 请求URL: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:43:02.5794 | Start processing HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:43:02.5866 | Sending HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:43:06.2980 | Received HTTP response headers after 3707.6927ms - 200 
2025-08-14 08:43:06.2980 | End processing HTTP request after 3729.0565ms - 200 
2025-08-14 08:43:26.0363 | API响应: {"choices":[{"message":{"role":"assistant","content":"```json\n{\n  \"province\": \"河南省\",\n  \"city\": \"漯河市\",\n  \"networkName\": \"漯河移动运营商PON网络\",\n  \"orderType\": 1,\n  \"orderTypeName\": \"割接\",\n  \"productSpe\": \"接入\"\n}\n```"},"finish_reason":"stop","index":0,"logprobs":null}],"object":"chat.completion","usage":{"prompt_tokens":2952,"completion_tokens":61,"total_tokens":3013,"prompt_tokens_details":{"cached_tokens":0}},"created":1755132186,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-b8eadbdb-e25f-9150-98a7-339b9c27d778"} 
2025-08-14 08:43:33.6049 | 提取基础信息完成，省份: 河南省，网络名称: 漯河移动运营商PON网络，工单类型: 割接 
2025-08-14 08:43:43.3522 | ✅ 第一阶段完成，耗时: 72909.1573ms 
2025-08-14 08:43:44.7838 | 🔄 第二阶段开始：网络名称匹配 
2025-08-14 08:43:47.2630 | 开始匹配标准网络名称: 漯河移动运营商PON网络 
2025-08-14 08:43:47.3503 | 文本优化：原始长度 1014，优化后长度 998，节省 16 个字符 (1.58%) 
2025-08-14 08:43:47.3541 | 请求体: {"model":"qwen-plus","messages":[{"role":"system","content":"你是一个专业的网络名称匹配专家，擅长将用户提供的网络名称与标准网络名称进行匹配。请尽可能找到最匹配的标准名称。"},{"role":"user","content":"请根据以下信息，匹配最合适的标准网络名称。 原始网络名称：漯河移动运营商PON网络 省份：河南省 地市：漯河市 专业：接入 标准网络列表（只包含本省份，包含网络名、运营商和专业信息）： [{\"NetName\":\"河南省漯河市联通接入本地网\",\"Province\":\"河南省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"接入\",\"Operator\":\"联通\"},{\"NetName\":\"河南省漯河市移动接入本地网\",\"Province\":\"河南省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"接入\",\"Operator\":\"移动\"},{\"NetName\":\"河南省漯河市联通OTN本地DWDM网\",\"Province\":\"河南省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"OTN\",\"Operator\":\"联通\"},{\"NetName\":\"河南省漯河市联通智能城域网本地网\",\"Province\":\"河南省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"智网\",\"Operator\":\"联通\"},{\"NetName\":\"河南省漯河市联通OTN本地网\",\"Province\":\"河南省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"OTN\",\"Operator\":\"联通\"},{\"NetName\":\"河南省漯河市联通MSTP本地网\",\"Province\":\"河南省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"MSTP\",\"Operator\":\"联通\"}] 可选专业列表： [\"接入\",\"OTN\",\"智网\",\"MSTP\"] 可选运营商列表： [\"联通\",\"移动\"] 匹配规则： 1. 只在指定省份和地市下查找。 2. 优先精确匹配网络名称。 3. 如果网络名称不匹配： - 如果网络名称中不包含专业信息，则使用传入的专业参数。 - 如果网络名称中包含专业信息，则使用网络名称中的专业，再结合标准网络中的专业、运营商和省市名进行辅助判断。 4. 返回最匹配的标准网络名称、运营商和专业信息，以及置信度（0-100）。 返回格式： { \"standardNetworkName\": \"\", \"matchConfidence\": 100 }"}],"temperature":0.7,"stream":false} 
2025-08-14 08:43:47.3541 | 请求URL: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:43:47.3541 | Start processing HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:43:47.3541 | Sending HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:43:48.3011 | Received HTTP response headers after 936.634ms - 200 
2025-08-14 08:43:48.3011 | End processing HTTP request after 943.1948ms - 200 
2025-08-14 08:43:48.3065 | API响应: {"choices":[{"message":{"role":"assistant","content":"{\n  \"standardNetworkName\": \"河南省漯河市移动接入本地网\",\n  \"matchConfidence\": 100\n}"},"finish_reason":"stop","index":0,"logprobs":null}],"object":"chat.completion","usage":{"prompt_tokens":465,"completion_tokens":29,"total_tokens":494,"prompt_tokens_details":{"cached_tokens":0}},"created":1755132228,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-1ddef08b-63b6-9cf8-a4ba-fc6124035d4a"} 
2025-08-14 08:43:57.4459 | AI匹配标准网络名称完成: 河南省漯河市移动接入本地网，匹配置信度: 100% 
2025-08-14 08:44:12.0257 | ✅ 第二阶段完成，耗时: 25283.6634ms 
2025-08-14 08:44:15.1558 | 🔄 第三阶段开始：4线程并发处理，工单类型: 割接 
2025-08-14 08:44:27.1989 | 🔄 线程1开始处理第一部分（字段6,7） 
2025-08-14 08:44:29.0264 | 文本优化：原始长度 1152，优化后长度 1076，节省 76 个字符 (6.60%) 
2025-08-14 08:44:29.0264 | 请求体: {"model":"qwen-plus","messages":[{"role":"system","content":"你是一个专业的QMO工单解析助手，擅长从文档中提取详细的工单信息，包括风险级别、计划时间、任务内容等。请按照指定的JSON格式返回结果。"},{"role":"user","content":"请根据以下文档内容，提取QMO工单报备所需的详细信息。 基础信息： - 工单类型：非升级 - 处理部分：第一部分（开头到变更操作概述） 请从文档中提取以下信息： 6. 工单主题 工单主题 -工单主题一般出现在内容开头部分，用来描述整个文档的具体内容。 -例如：7月11日0：00-04：00浙江绍兴网络2LN4、1LN4单盘升级操作网络变更操作技术方案-OTN-V3.1（此例不能作为最后的提取内容） 7. 抄送人 文档中无相关字段 - 直接赋值空数组 文档内容： 网络变更操作技术方案-PON 漯河移动运营商PON网络 业务割接方案 河南省技服中心 时间：2025年7月10日 对3级及以上风险要求提供变更操作方案，4级风险由本地技服根据实情自行要求。 所有项目不允许裁剪，当本次操作不涉及时，可以描述为不涉及。 三级及以上风险网络变更需要到技服技术支撑部TAC中心审核，需提前24小时报备；二级风险网络变更需要到产出线工程部审核，需提前48小时报备；一级风险网络变更需要到研发LMT审核，需提前72小时报备。同时，设置有三级（紧急）、二级（紧急）、一级（紧急）三个风险等级，此等级报备无需时间限制。对于一、二、三级网络变更操作和特殊时期（例如：集采期、投标、重大保障期、高访、集团网络巡检等）所有等级操作均需报备销售认可。 销售审核意见： 销售审核人 审核意见 周浩天 通过 省技服审核意见 省技服人员 审核人 审核意见 网络负责人 何俊辉 通过 技术主管 张赢彪 通过 ### 所有提取的内容不能自己编造，只能从文档中提取，宁缺勿错的原则！ ### 请严格按照以下JSON格式返回提取结果,JSON里的举例不能作为结果返回，如果没有置为空(注意，格式里的具体内容和文档无关，请你只提取文档里的内容)： { \"title\": \"工单主题\", // 6.工单主题 \"copyUsersList\":[ {\"id\":null,\"userType\":1,\"invUserName\":\"<EMAIL>\",\"invNickName\":\"肖绍谦\",\"invPhone\":\"18627827716\",\"invEmail\":\"<EMAIL>\",\"invPostCode\":4,\"invPostName\":\"软件工程师\",\"invDept\":\"武汉烽火技术服务有限公司-数字技术部\",\"invUserLevel\":null} ], // 7.抄送人列表（绝对不能使用示例数据或者捏造数据） }"}],"temperature":0.7,"stream":false} 
2025-08-14 08:44:29.0264 | 请求URL: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:44:29.0264 | Start processing HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:44:29.0398 | Sending HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:44:30.7415 | Received HTTP response headers after 1677.3326ms - 200 
2025-08-14 08:44:30.8073 | End processing HTTP request after 1769.2146ms - 200 
2025-08-14 08:44:30.8406 | 🔄 线程2开始处理第二部分（字段1,2,3,8,9,10,16） 
2025-08-14 08:44:30.8744 | API响应: {"choices":[{"message":{"role":"assistant","content":"```json\n{\n  \"title\": \"网络变更操作技术方案-PON 漯河移动运营商PON网络 业务割接方案\",\n  \"copyUsersList\": []\n}\n```"},"finish_reason":"stop","index":0,"logprobs":null}],"object":"chat.completion","usage":{"prompt_tokens":705,"completion_tokens":40,"total_tokens":745,"prompt_tokens_details":{"cached_tokens":0}},"created":1755132270,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-32447fda-0ac2-9697-b4d1-128fa6654cc6"} 
2025-08-14 08:44:31.2365 | 文本优化：原始长度 5017，优化后长度 4747，节省 270 个字符 (5.38%) 
2025-08-14 08:44:31.2424 | 请求体: {"model":"qwen-plus","messages":[{"role":"system","content":"你是一个专业的QMO工单解析助手，擅长从文档中提取详细的工单信息，包括风险级别、计划时间、任务内容等。请按照指定的JSON格式返回结果。"},{"role":"user","content":"请根据以下文档内容，提取QMO工单报备所需的详细信息。 基础信息： - 工单类型：非升级 - 处理部分：第二部分（变更操作概述） 请从文档中提取以下信息： 1. 风险级别 必须严格从以下标准省份列表中匹配：一级(紧急),一级,二级(紧急),二级,三级(紧急),三级,四级(紧急),四级 2. 是否重保 文档中无相关字段 - bool类型 - 直接赋值false 3. 产品线附加信息 8. 网管信息集合 据文档中的版本信息，网管版本信息表格提取 - 仅提取网管系列、网管版本和网管补丁。 - 网管系列应包含网管系统的名称，主版本即为网管版本，补丁信息为网管补丁。 **注意**：在提取时需关注相近词汇的识别，以确保信息的准确性。 9. 设备信息集合 据文档中的版本信息，设备版本信息表格提取 - 设备型号(或者类型) - 板卡名称 - 板卡物料号（注意：硬件版本即为板卡物料号） - 软件版本 - 软件编译日期（格式为YYYY-MM-DD） **注意**：有些表格中，软件版本和编译日期可能写在一起，需要进行分离，在提取时需关注相近词汇的识别，以确保信息的准确性。如果没有匹配的信息，就置为空 10. 变更工作量集合 据文档中的操作对象，进行提取 **实施对象**： - 即表格中的设备类型。 **本次实施数量**： - 即表格中的数量，数字格式。 **注意**：在提取时需关注相近词汇的识别，以确保信息的准确性。 16. 工单内容 根据文档中的内容，总结该文档的目的和原因 文档内容： 变更操作概述 操作简述 在此描述实施本次操作的类型、原因和主要内容简述，割接实施窗口时间。 操作类型：AN6000-17设备 操作原因：因功能需要升级设备版本 内容简述：升级以下51台6000设备。 网元名称 网元IP地址 漯河市临颍县公司一楼机房-OLT322-FW-AN6000-17 ************* 漯河市临颍盈翠半岛机房-OLT323-FW-AN6000-17 ************* 漯河市临颍南街村机房-OLT326-FW-AN6000-17 ************** 漯河市临颍老局机房-OLT327-FW-AN6000-17 ************** 漯河市临颍火车站机房-OLT328-FW-AN6000-17 ************** 漯河市临颍第二出口局机房-OLT331-FW-AN6000-17 ************** 漯河市临颍七里机房-OLT330-FW-AN6000-17 ************* 漯河市临颍黄龙机房-OLT332-FW-AN6000-17 ************* 漯河市临颍北街机房-OLT336-FW-AN6000-17 ************* 漯河市临颍源汇宾馆机房-OLT373-FW-AN6000-17 ************** 漯河市临颍北街路口机房-OLT374-FW-AN6000-17 ************** 漯河市临颍台陈2.5G机房-OLT344-FW-AN6000-17 ************** 漯河市临颍商桥2.5G机房-OLT375-FW-AN6000-17 ************* 漯河市临颍三家店2.5G机房-OLT349-FW-AN6000-17 ************** 漯河市临颍县瓦店2.5G机房-OLT346-FW-AN6000-17 172.24.202.154 漯河市临颍王孟2.5G机房-OLT350-FW-AN6000-17 *************6 漯河市临颍皇帝庙2.5G机房-OLT354-FW-AN6000-17 172.24.202.218 漯河市临颍巨陵2.5G机房-OLT347-FW-AN6000-17 172.24.202.162 漯河市临颍陈庄2.5G机房-OLT355-FW-AN6000-17 172.24.202.226 漯河市临颍固厢2.5G机房-OLT352-FW-AN6000-17 172.24.202.202 漯河市临颍大郭2.5G机房-OLT351-FW-AN6000-17 172.24.202.194 漯河市临颍石桥2.5G机房-OLT353-FW-AN6000-17 172.24.202.210 漯河市临颍杜曲机房-OLT371-FW-AN6000-17 172.24.203.98 漯河市临颍铁西机房-OLT372-FW-AN6000-17 172.24.203.106 漯河市临颍王岗2.5G机房-OLT345-FW-AN6000-17 172.24.202.146 漯河市临颍繁城2.5G机房-OLT343-FW-AN6000-17 172.24.202.130 漯河市临颍窝城2.5G机房-OLT348-FW-AN6000-17 172.24.202.170 漯河市临颍第二出口局机房-OLT387-FW-AN6000-17 172.24.203.218 漯河市舞阳吴城2.5G机房-OLT325-FW-AN6000-17 172.24.200.114 漯河市舞阳东湖花园2.5G机房-OLT321-FW-AN6000-17 172.24.202.2 漯河市舞阳舞北2.5G机房-OLT334-FW-AN6000-17 172.24.202.42 漯河市舞阳北舞渡2.5G机房-OLT329-FW-AN6000-17 172.24.201.98 漯河市舞阳太尉机房-OLT333-FW-AN6000-17 172.24.202.66 漯河市舞阳移动办公楼机房-OLT335-FW-AN6000-17 172.24.202.50 漯河市舞阳南京路机房-OLT370-FW-AN6000-17 172.24.203.90 漯河市舞阳上海路机房-OLT367-FW-AN6000-17 172.24.203.66 漯河市舞阳武装部机房-OLT366-FW-AN6000-17 172.24.203.58 漯河市舞阳县政府机房-OLT368-FW-AN6000-17 172.24.203.74 漯河市舞阳金都机房-OLT369-FW-AN6000-17 172.24.203.82 漯河市舞阳莲花2.5G机房-OLT356-FW-AN6000-17 172.24.202.234 漯河市舞阳孟寨2.5G机房-OLT358-FW-AN6000-17 172.24.202.250 漯河市舞阳保和2.5G机房-OLT360-FW-AN6000-17 172.24.203.10 漯河市舞阳太尉2.5G机房-OLT359-FW-AN6000-17 172.24.203.2 漯河市舞阳马村2.5G机房-OLT361-FW-AN6000-17 172.24.203.18 漯河市舞阳侯集2.5G机房-OLT362-FW-AN6000-17 172.24.203.26 漯河市舞阳姜店2.5G机房-OLT363-FW-AN6000-17 172.24.203.34 漯河市舞阳章化2.5G机房-OLT365-FW-AN6000-17 172.24.203.50 漯河市舞阳九街2.5G机房-OLT364-FW-AN6000-17 172.24.203.42 漯河市舞阳辛安2.5G机房-OLT357-FW-AN6000-17 ************** 漯河市舞阳老局-OLT376-FW-AN6000-17 ************** 漯河市舞阳老局机房-OLT385-FW-AN6000-17 ************** 割接时间：7月11、16日 1：00-06：00 影响评估 在此描述实施本次操作对客户的影响程度和风险操作级别。 风险等级：四级（参考风险分级标准确定，并和OA报备工单一致） 影响范围：本次变更操作，预期会影响各类业务多少量。详细影响范围如下： 业务类型 数量 宽带 18261 语音 2512 IPTV 12311 北向接口采集 不涉及 其他（专线等） 1580 …… 风险评估（重点） 在此描述本次操作预先识别、可能发生的风险，以及应对措施。以下为示范，根据现场情况填写： 序号 风险 可能性 影响程度 应对措施 1 宽带业务、语音业务和IPTV业务都会受影响 高 高 受影响时间约为20~40分钟 2 操作对象 在此描述本次操作的对象和数量。 本次操作涉及如下对象：以下为示范，不涉及的必须删除,未包含必须添加 设备类型 数量 AN6000-17 21 版本信息 在此描述本次操作对象的版本信息和目标版本信息，仅针对软件升级的场景。操作不涉及版本信息的可以直接填写不涉及即可。 软件版本信息 名称 升级前版本 升级后软件版本 AN6000-17 OLT V105R002SP0 V105R008SP0 HCWA RP1400 RP1600 GMOA RP1400 RP1600 GPOA RP1400 RP1400 网管版本信息 网管系统 主版本 补丁信息 补丁时间 UNM2000 V2R7 SPH0805 20240110 成功标准 在此描述如何验证本次操作是否成功。以下为示范，不涉及的必须删除,未包含必须添加 成功标准： 业务确认正常 操作达到预期目标 升级后软件版本为V105R008SP0 ### 所有提取的内容不能自己编造，只能从文档中提取，宁缺勿错的原则！ ### 请严格按照以下JSON格式返回提取结果,JSON里的举例不能作为结果返回，如果没有置为空(注意，格式里的具体内容和文档无关，请你只提取文档里的内容)： { \"riskLevel\": \"一级\", // 1.风险级别 \"isReinstated\": false, // 2.是否重保 \"lineRemark\": \"产品附加信息\", // 3.产品线附加信息 \"involveNmpinfos\": [{\"id\":null,\"orderKind\":3,\"nmpSeries\":\"网管系列（当前版本）\",\"nmpVersion\":\"网管版本（当前版本）\",\"nmpPatches\":\"网管补丁（当前版本）\",\"tarNmpSeries\":\"网管系列（目标版本）\",\"tarNmpVersion\":\"网管版本（目标版本）\",\"tarNmpPatch\":\"网管补丁（目标版本）\"}], // 8.网管信息集合（绝对不能使用示例数据或者捏造数据） \"involveDevices\": [{\"id\":null,\"orderKind\":3,\"invDeviceType\":\"对象-设备型号\",\"invBoardName\":\"对象-板卡名称\",\"invHardVersion\":\"对象-板卡物料号\",\"invSoftVersion\":\"软件版本-当前\",\"compileTime\":\"软件编译日期-当前版本\",\"tarInvSoftVersion\":\"软件版本-目标版本\",\"tarCompileTime\":\"软件编译日期-目标版本\"}], // 9.设备信息集合（绝对不能使用示例数据或者捏造数据） \"involveProducts\": [{\"id\":null,\"orderKind\":3,\"objectCount\":12,\"objectName\":\"实施对象\"}], // 10.变更工作量集合（绝对不能使用示例数据或者捏造数据） \"taskContent\": \"\", // 16.任务内容 }"}],"temperature":0.7,"stream":false} 
2025-08-14 08:44:31.2424 | 请求URL: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:44:31.2424 | Start processing HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:44:31.2589 | Sending HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:44:32.8080 | 🔄 线程3开始处理第三部分（字段11,12,13,14,15,17） 
2025-08-14 08:44:33.1368 | 文本优化：原始长度 2904，优化后长度 2676，节省 228 个字符 (7.85%) 
2025-08-14 08:44:33.1368 | 请求体: {"model":"qwen-plus","messages":[{"role":"system","content":"你是一个专业的QMO工单解析助手，擅长从文档中提取详细的工单信息，包括风险级别、计划时间、任务内容等。请按照指定的JSON格式返回结果。"},{"role":"user","content":"请根据以下文档内容，提取QMO工单报备所需的详细信息。 基础信息： - 工单类型：非升级 - 处理部分：第三部分（变更操作准备） 请从文档中提取以下信息： 11. 默认客户信息 **注意**：根据文档中的【变更操作准备】-【人员安排】-【客户变更小组名单】部分的表格进行提取（不要从客户审核的表格中查找）： - 查找包含\"姓名\"、\"电话\"、\"职责\"等列的客户人员表格 - 选取表格中的第一行客户数据（排除表头） - 提取该客户的姓名、电话、邮箱信息 - 如果表格中没有邮箱列或邮箱为空，则邮箱字段设为空字符串 - 如果电话（或者相近的字段，比如手机号，联系方式等）为空，则电话字段设为空字符串 - 绝对不能编造任何数据 - 返回格式：{\"email\":\"客户邮箱或空\",\"nickName\":\"客户姓名\",\"phoneNumber\":\"客户电话或空\"} 12. 默认客户邮箱 **注意**：从第11项提取的默认客户信息中获取邮箱字段，如果没有邮箱信息则返回空字符串（结果和11项中的email结果保持一致）。 13. 默认客户电话 **注意**：从第11项提取的默认客户信息中获取电话字段，如果没有电话信息则返回空字符串（结果和11项中的phoneNumber结果保持一致）。 14. 实施人信息（支持多个） 据文档中的操作对象，烽火现场实施小组名单中表格进行提取，当职责为实施负责人，负责本次操作实施 **姓名**： - 即实施负责人姓名。 **电话**： - 即实施负责人电话。 15. 审核人信息（支持多个） 据文档中的操作对象，烽火现场实施小组名单中表格进行提取，当职责为负责审核实施操作步骤过程 **姓名**： - 即审核人姓名。 **电话**： - 即审核人电话。 17. 所有客户信息 **注意**：根据文档中的 人员安排-客户变更小组名单 下方的第一个表， 这个表中所有的客户信息（客户姓名，客户邮箱，客户电话）。 - 是一个数组对象，格式如下[{\"invNickName\":\"\",\"invPhone\":,\"invEmail\":\"\"}] 文档内容： 变更操作准备 人员安排 在此描述参与本次操作的操作人员、责任人员名单和通信信息。 客户变更小组名单 姓名 电话 职责 王晓伟 13903950637 全业务班长 孟盼盼 19839501231 OLT负责人 付金美 13721320138 BRAS负责人 烽火现场小组名单【注：对外提供时，属性列去除】 姓名 电话 职责 何俊辉 13140650255 总负责人 张赢彪 18538922264 技术接口负责人，负责参加本次操作实施，对其成功负责。 李金波 15639591221 负责完成具体操作。 董振邦 13007627232 监督及保障人员。 烽火远程支撑保障名单 姓名 电话 职责 部门 张赢彪 18538922264 技术保障，后台支撑. 河南技服 时间窗口 在此给出客户许可的操作时间窗口，如果存在多天分段操作，用分段计划描述。其中，干扰识别，请描述操作时间段内，相关联的其他专业、省干、核心网是否有冲突的操作，需明确确认的对象和结果。比如：经和地市公司网络部、并通过地市公司向省网络部、地市无线中心确认，申请时间窗口内无其他专业操作动作。以下为示范： 授权操作时间窗口 时间 工作内容 技术责任人 00：01-06：00 先升级单盘，再升级备主控，然后主控 张赢彪 内部控制的操作时间窗口【注：对外该部分可以去除】 普通操作预留检查时间2小时，正常可操作时间0：00-4：00 失控风险控制时间2小时，出现不可控风险，启动应急预案时间为4：00 其他准备 需填写下面关键项的准备情况，且不限于这些内容，办事处根据实际情况新增 软件准备 软件类型 版本信息 HCWA RP1600 GMOA RP1600 GPOA RP1400 备份（检查结果上传系统） 涉及OLT变更的，需导出并备份OLT配置文件 涉及网管变更的，需完整备份网管文件 备件 网络变更涉及到的单盘均需有相应的备件 远程接入 提前搭建远程环境，便于远程支撑时 类型 说明 接入方案 方案1：机房现场客户端操作 账号检测 网管变更操作申请管理员权限账号 割接前设备运行情况检查（检查结果上传系统） 序号 检查项目 关键动作 检查标准 是否正常 1 设备各单盘与主控通信是否正常 Ping 各单盘的私有地址 无丢包 是 2 网管各服务状态正常 查看服务进程 服务均已启动 是 3 告警核查 导出告警 生成告警导出文件 是 4 OLT性能核查 查看主控盘和业务单盘CPU和内存利用率 内存占用率不得高于95%，CPU不得高于80% 是 5 配置保存检查 重新保存flash 无配置未保存告警 是 如存在影响割接的异常，需和用户沟通确定下一步计划； ### 所有提取的内容不能自己编造，只能从文档中提取，宁缺勿错的原则！ ### 请严格按照以下JSON格式返回提取结果,JSON里的举例不能作为结果返回，如果没有置为空(注意，格式里的具体内容和文档无关，请你只提取文档里的内容)： { \"customName\": {\"email\":\"<EMAIL>\",\"nickName\":\"xiaoshaoqian\",\"phoneNumber\":\"12324567131\"}, // 11.默认客户信息（数据不能捏造和使用示例数据） \"customEmail\": \"<EMAIL>\", // 12.客户邮箱（结果和11项中的email字段的结果完全保持一致） \"customPhone\": \"12324567131\", // 13.客户电话（结果和11项中的phoneNumber字段的结果完全保持一致） \"taskUsers\": [{\"nickName\":\"朱浩\",\"userPhone\":\"13787031546\"}], // 14.实施人信息（绝对不能使用示例数据或者捏造数据） \"checkUsers\": [{\"nickName\":\"肖绍谦\",\"userPhone\":\"18627827716\"}], // 15.审核人（绝对不能使用示例数据或者捏造数据） \"customUsers\":[{\"invNickName\":\"肖绍谦\",\"invPhone\":12324567131,\"invEmail\":\"<EMAIL>\"}] //17.所有客户信息（绝对不能使用示例数据或者捏造数据） }"}],"temperature":0.7,"stream":false} 
2025-08-14 08:44:33.1484 | 请求URL: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:44:33.1484 | Start processing HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:44:33.1484 | Sending HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:44:34.2381 | 🔄 线程4开始处理第四部分（字段4,5） 
2025-08-14 08:44:34.5518 | 文本优化：原始长度 1851，优化后长度 1728，节省 123 个字符 (6.65%) 
2025-08-14 08:44:34.5553 | 请求体: {"model":"qwen-plus","messages":[{"role":"system","content":"你是一个专业的QMO工单解析助手，擅长从文档中提取详细的工单信息，包括风险级别、计划时间、任务内容等。请按照指定的JSON格式返回结果。"},{"role":"user","content":"请根据以下文档内容，提取QMO工单报备所需的详细信息。 基础信息： - 工单类型：非升级 - 处理部分：第四部分（操作实施步骤） 请从文档中提取以下信息： 4. 计划开始时间 从文档中的【操作步骤】里进行提取： - 从操作步骤时间段中提取开始时间，并减去2个小时。 - 格式为YYYY-MM-DD HH:MM:SS。 5. 计划结束时间 从文档中的【操作步骤】里进行提取： - 从操作步骤时间段中提取结束时间，并加上2个小时。 - 格式为YYYY-MM-DD HH:MM:SS。 文档内容： 操作实施步骤（重点） 设备操作步骤 报备人按照下面整体时间控制节点补充详细的操作步骤 在此详细描述实施本次操作的具体步骤，要求包括操作内容和时间预估。 序号 时间段 操作内容 预期结果 风险应对 责任人 1 0:00-0:59 进入机房做操作前准备 李金波 2 0:59-4:00 开始进行相关操作 李金波 3 4:00-4:20 核查业务 正常运行 无风险 李金波 4 4:20-6:00 客户验证，宣告离场 李金波 升级操作 OLT升级步骤 保存OLT配置至flash; 导出OLT配置到网管，检查配置脚本是否完整，配置脚本不完整则需要补充完整; 记录下表中各项信息，以备在升级后进行用户业务比对和恢复： 序号 操作项目 说明 1 记录上联端口流量 记录工作的上联端口流量计数，用于系统复位后业务核对 2 记录ONU在线状态 用于升级后的状态核对 3 记录IAD注册信息 用于升级后的语音业务核对 4 记录槽位MAC表 用于升级后的数据业务核对 将升级软件包拷贝到网管服务器和本地便携电脑； 通过网管服务器将线卡盘软件到RP1600; 通过网管服务器将主控盘（先备盘，后主盘）软件升级到目标版本； 重启整个设备使升级文件生效; 查看线卡盘的软件版本号是否目标版本。如版本号不正常，请仔细核对升级文件，再次进行升级。 查看主控的软件版本是否为否目标版本。如版本号不正常，请仔细核对升级文件，再次进行升级操作。 检查ONU上配置是否获取正确，并仔细检查业务是否正常。 网管变更操作 变更后业务验证方案（每类业务核查情况全部上传系统） 在此描述操作实施后需要进行测试和验证的方案，包括内部测试、业务确认以及客户确认的对象、方法。 增删查改验证： 随机新增各类型业务1条，确认增加业务正常。 对现存业务随机选取进行修改操作，确认修改有效。 删除新增测试业务，确认删除业务正常。 北向接口功能确认： 自测各接口功能正常 状态查询： 对涉及变更的设备进行设备状态、性能、告警和业务查询，确认可正常查询 客户确认 与客户确认，确保所有功能业务均正常才能宣告离场 应急预案（重点） 在此描述意外情况下的应急预案。意外情况是指未能在规定时间内达成预期目标、出现不可控影响扩散等情况。应当包括应急预案的启动条件、采取应急预案的步骤。对于一二级风险，要求有双预案可选。 启动条件： 当满足以下任一条件时，要求必须启动应急预案： 在操作窗口结束前2小时，如X点整（请根据实际窗口时间确定），出现操作严重滞后，操作预计无法完成。 操作过程中，任何时候出现本次操作引发的、不在预期范围内的设备故障。 严重影响客户感知且客户要求必须启动应急预案。 预案： 硬件故障，可尝试更换；软件问题，可尝试回退至原有版本。如无法独立解决应及时联系总部保障人员处理。 步骤 操作内容 1 回退到原版本 2 回退到原配置 3 割接到其他节点 4...... 必选 倒回后测试：按照操作后测试进行测试验证网络各项功能恢复 必选 倒回后确认：按照确认方案进行准确确认 ### 所有提取的内容不能自己编造，只能从文档中提取，宁缺勿错的原则！ ### 请严格按照以下JSON格式返回提取结果,JSON里的举例不能作为结果返回，如果没有置为空(注意，格式里的具体内容和文档无关，请你只提取文档里的内容)： { \"planStartTime\": \"2025-07-14 10:46:10\", // 4.计划开始时间 \"planEndTime\": \"2025-08-13 10:46:14\", // 5.计划结束时间 }"}],"temperature":0.7,"stream":false} 
2025-08-14 08:44:34.5553 | 请求URL: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:44:34.5553 | Start processing HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:44:34.5553 | Sending HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:44:37.5338 | Received HTTP response headers after 2968.6847ms - 200 
2025-08-14 08:44:37.5338 | End processing HTTP request after 2974.7083ms - 200 
2025-08-14 08:44:37.5394 | API响应: {"choices":[{"message":{"role":"assistant","content":"```json\n{\n  \"planStartTime\": \"2025-07-14 22:59:00\",\n  \"planEndTime\": \"2025-07-15 08:00:00\"\n}\n```"},"finish_reason":"stop","index":0,"logprobs":null}],"object":"chat.completion","usage":{"prompt_tokens":1196,"completion_tokens":58,"total_tokens":1254,"prompt_tokens_details":{"cached_tokens":0}},"created":1755132277,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-205b7005-0bf6-983e-9b2b-5f42c52758f9"} 
2025-08-14 08:44:39.7523 | Received HTTP response headers after 6597.5972ms - 200 
2025-08-14 08:44:39.7523 | End processing HTTP request after 6604.9416ms - 200 
2025-08-14 08:44:39.7584 | API响应: {"choices":[{"message":{"role":"assistant","content":"```json\n{\n  \"customName\": {\n    \"email\": \"\",\n    \"nickName\": \"王晓伟\",\n    \"phoneNumber\": \"13903950637\"\n  },\n  \"customEmail\": \"\",\n  \"customPhone\": \"13903950637\",\n  \"taskUsers\": [\n    {\n      \"nickName\": \"张赢彪\",\n      \"userPhone\": \"18538922264\"\n    }\n  ],\n  \"checkUsers\": [\n    {\n      \"nickName\": \"李金波\",\n      \"userPhone\": \"15639591221\"\n    }\n  ],\n  \"customUsers\": [\n    {\n      \"invNickName\": \"王晓伟\",\n      \"invPhone\": 13903950637,\n      \"invEmail\": \"\"\n    },\n    {\n      \"invNickName\": \"孟盼盼\",\n      \"invPhone\": 19839501231,\n      \"invEmail\": \"\"\n    },\n    {\n      \"invNickName\": \"付金美\",\n      \"invPhone\": 13721320138,\n      \"invEmail\": \"\"\n    }\n  ]\n}\n```"},"finish_reason":"stop","index":0,"logprobs":null}],"object":"chat.completion","usage":{"prompt_tokens":1712,"completion_tokens":273,"total_tokens":1985,"prompt_tokens_details":{"cached_tokens":0}},"created":1755132280,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-23f62d7b-7fda-9a27-b5fd-aa816ac78f61"} 
2025-08-14 08:44:47.2381 | Received HTTP response headers after 15978.7367ms - 200 
2025-08-14 08:44:47.2422 | End processing HTTP request after 15985.4482ms - 200 
2025-08-14 08:44:47.2422 | API响应: {"choices":[{"message":{"role":"assistant","content":"```json\n{\n  \"riskLevel\": \"四级\",\n  \"isReinstated\": false,\n  \"lineRemark\": \"\",\n  \"involveNmpinfos\": [\n    {\n      \"id\": null,\n      \"orderKind\": 3,\n      \"nmpSeries\": \"UNM2000\",\n      \"nmpVersion\": \"V2R7\",\n      \"nmpPatches\": \"SPH0805\",\n      \"tarNmpSeries\": \"\",\n      \"tarNmpVersion\": \"\",\n      \"tarNmpPatch\": \"\"\n    }\n  ],\n  \"involveDevices\": [\n    {\n      \"id\": null,\n      \"orderKind\": 3,\n      \"invDeviceType\": \"AN6000-17\",\n      \"invBoardName\": \"\",\n      \"invHardVersion\": \"\",\n      \"invSoftVersion\": \"OLT V105R002SP0\",\n      \"compileTime\": \"\",\n      \"tarInvSoftVersion\": \"OLT V105R008SP0\",\n      \"tarCompileTime\": \"\"\n    },\n    {\n      \"id\": null,\n      \"orderKind\": 3,\n      \"invDeviceType\": \"HCWA\",\n      \"invBoardName\": \"\",\n      \"invHardVersion\": \"\",\n      \"invSoftVersion\": \"RP1400\",\n      \"compileTime\": \"\",\n      \"tarInvSoftVersion\": \"RP1600\",\n      \"tarCompileTime\": \"\"\n    },\n    {\n      \"id\": null,\n      \"orderKind\": 3,\n      \"invDeviceType\": \"GMOA\",\n      \"invBoardName\": \"\",\n      \"invHardVersion\": \"\",\n      \"invSoftVersion\": \"RP1400\",\n      \"compileTime\": \"\",\n      \"tarInvSoftVersion\": \"RP1600\",\n      \"tarCompileTime\": \"\"\n    },\n    {\n      \"id\": null,\n      \"orderKind\": 3,\n      \"invDeviceType\": \"GPOA\",\n      \"invBoardName\": \"\",\n      \"invHardVersion\": \"\",\n      \"invSoftVersion\": \"RP1400\",\n      \"compileTime\": \"\",\n      \"tarInvSoftVersion\": \"RP1400\",\n      \"tarCompileTime\": \"\"\n    }\n  ],\n  \"involveProducts\": [\n    {\n      \"id\": null,\n      \"orderKind\": 3,\n      \"objectCount\": 51,\n      \"objectName\": \"AN6000-17\"\n    }\n  ],\n  \"taskContent\": \"升级以下51台6000设备。\"\n}\n```"},"finish_reason":"stop","index":0,"logprobs":null}],"object":"chat.completion","usage":{"prompt_tokens":3690,"completion_tokens":549,"total_tokens":4239,"prompt_tokens_details":{"cached_tokens":0}},"created":1755132287,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-d6786cb6-32d1-9c8d-87f7-3f372910d130"} 
2025-08-14 08:45:01.0943 | ✅ 线程1完成，耗时: 2210.8034ms 
2025-08-14 08:45:04.9026 | ✅ 线程2完成，耗时: 16025.469ms 
2025-08-14 08:45:06.8184 | ✅ 线程3完成，耗时: 6627.7423ms 
2025-08-14 08:45:08.7528 | ✅ 线程4完成，耗时: 2994.9942ms 
2025-08-14 08:45:12.8096 | ✅ 第三阶段完成，最大耗时: 16025.469ms，总耗时: 55439.2528ms 
2025-08-14 08:45:14.4017 | 🔄 开始结果合并 
2025-08-14 08:45:39.7612 | 结果合并成功，合并了4/4个部分的结果 
2025-08-14 08:45:39.7612 | ✅ 结果合并完成，耗时: 24735.8591ms 
2025-08-14 08:45:39.7612 | ✅ AI智能填报完成，工单类型: 割接，总耗时: 55098596.6311ms 
2025-08-14 08:45:43.2233 | Request:/api/qmov2/ai/fillform 
2025-08-14 08:45:47.8643 | 接收到AI智能填报请求 
2025-08-14 08:45:50.3982 | 开始AI智能填报QMO工单，文件名: 2025年7月11日01：00-06：00漯河移动PON网络升级操作网络变更操作技术方案-PON-V3.0.docx 
2025-08-14 08:45:50.4262 | 🔄 开始文档分割 
2025-08-14 08:45:50.4376 | 开始分割文档: C:\Users\<USER>\AppData\Local\Temp\QMDAITemp\497b81ca-ec11-4e45-ba18-b1950ddbf320.docx 
2025-08-14 08:45:50.4376 | 开始提取完整文档内容: C:\Users\<USER>\AppData\Local\Temp\QMDAITemp\497b81ca-ec11-4e45-ba18-b1950ddbf320.docx 
2025-08-14 08:45:50.5573 | 文档内容提取完成，总长度: 6267 
2025-08-14 08:45:53.0824 | 标题验证通过: '变更操作概述' (包含: 变更操作概述, 长度: 6) 
2025-08-14 08:45:53.0857 | 找到标题: 变更操作概述 -> '变更操作概述' (位置: 423) 
2025-08-14 08:45:53.0857 | 标题验证通过: '变更操作准备' (包含: 变更操作准备, 长度: 6) 
2025-08-14 08:45:53.0857 | 找到标题: 变更操作准备 -> '变更操作准备' (位置: 3762) 
2025-08-14 08:45:53.0857 | 标题验证通过: '操作实施步骤（重点）' (包含: 操作实施步骤, 长度: 10) 
2025-08-14 08:45:53.0857 | 找到标题: 操作实施步骤 -> '操作实施步骤（重点）' (位置: 4938) 
2025-08-14 08:45:53.0857 | 分割完成: 找到 4/3 个标识符 
2025-08-14 08:45:53.1024 | 文档分割完成，共提取 4 个部分 
2025-08-14 08:45:53.1024 | ✅ 文档分割完成，找到 4/3 个标识符，耗时: 2667.9312ms 
2025-08-14 08:45:53.1024 | 🔄 第一阶段开始：基础信息提取 
2025-08-14 08:45:53.1024 | 开始提取基础信息（省份、网络名称、工单类型、专业） 
2025-08-14 08:45:53.1226 | 文本优化：原始长度 5364，优化后长度 5306，节省 58 个字符 (1.08%) 
2025-08-14 08:45:53.1226 | 请求体: {"model":"qwen-plus","messages":[{"role":"system","content":"你是一个专业的QMO工单解析助手，擅长从文档中提取省份、网络名称和工单类型等基础信息。请严格按照标准列表进行匹配。"},{"role":"user","content":"请仔细阅读以下文档内容，并从中提取省份、网络名称和工单类型信息。 注意： 1. 省份必须严格从以下标准省份列表中匹配： [\"广东省\",\"湖北省\",\"湖南省\",\"西藏自治区\",\"黑龙江省\",\"甘肃省\",\"河南省\",\"北京市\",\"辽宁省\",\"广西壮族自治区\",\"江苏省\",\"山西省\",\"河北省\",\"吉林省\",\"智利\",\"四川省\",\"印度尼西亚\",\"山东省\",\"新疆维吾尔自治区\",\"云南省\",\"上海市\",\"重庆市\",\"浙江省\",\"安徽省\",\"内蒙古自治区\",\"江西省\",\"阿尔及利亚\",\"陕西省\",\"泰国\",\"贵州省\",\"肯尼亚\",\"越南\",\"青海省\",\"宁夏回族自治区\",\"巴西\",\"新加坡\",\"匈牙利\",\"印度\",\"罗马尼亚\",\"阿曼\",\"菲律宾\",\"西班牙\",\"葡萄牙\",\"天津市\",\"福建省\",\"马来西亚\",\"墨西哥\",\"伊拉克\",\"孟加拉国\",\"海南省\",\"约旦\",\"伊朗\",\"缅甸\",\"哥伦比亚\",\"亚美尼亚\",\"巴勒斯坦\",\"巴基斯坦\",\"厄瓜多尔\",\"也门\",\"南非\",\"摩洛哥\",\"GM\",\"阿根廷\",\"香港\",\"多米尼加\",\"波兰\",\"虚拟省\",\"WG\",\"土耳其\",\"蒙古\",\"乌克兰\",\"加拿大\",\"尼日利亚\",\"赤道几内亚\",\"塞浦路斯\",\"埃及\",\"乌干达\",\"津巴布韦\",\"老挝\"] 2. 地市必须严格从以下标准省份列表中匹配： [\"揭阳市\",\"武汉市\",\"长沙市\",\"拉萨市\",\"绥化市\",\"安阳市\",\"北京市\",\"珠海市\",\"江门市\",\"锦州市\",\"百色市\",\"南京市\",\"晋城市\",\"沧州市\",\"长春市\",\"哈尔滨市\",\"黄石市\",\"邵阳市\",\"圣地亚哥\",\"东莞市\",\"淮安市\",\"成都市\",\"潮州市\",\"Jakarta Raya\",\"鄂州市\",\"黑河市\",\"青岛市\",\"贺州地区\",\"德阳市\",\"兰州市\",\"常州市\",\"哈密地区\",\"梅州市\",\"徐州市\",\"唐山市\",\"曲靖市\",\"咸宁市\",\"上海市\",\"重庆市\",\"杭州市\",\"南通市\",\"盐城市\",\"十堰市\",\"南充市\",\"白银市\",\"淄博市\",\"平凉市\",\"济宁市\",\"石河子市\",\"佳木斯市\",\"太原市\",\"甘孜藏族自治州\",\"哈密市\",\"南宁市\",\"渝北区\",\"石家庄市\",\"孝感市\",\"合肥市\",\"平顶山市\",\"阳泉市\",\"西城区\",\"东爪哇\",\"抚顺市\",\"鸡西市\",\"黄山市\",\"包头市\",\"衡水市\",\"金昌市\",\"商丘市\",\"株洲市\",\"菏泽市\",\"塔城地区\",\"驻马店市\",\"运城市\",\"茂名市\",\"南昌市\",\"济源市\",\"阿尔及尔\",\"直辖行政单位\",\"眉山市\",\"西安市\",\"曼谷\",\"安顺市\",\"广州市\",\"延安市\",\"滁州市\",\"黄冈市\",\"洛阳市\",\"忻州市\",\"玉林市\",\"内罗毕\",\"新余市\",\"甘南藏族自治州\",\"河内\",\"西宁市\",\"清远市\",\"德州市\",\"邯郸市\",\"济南市\",\"日喀则地区\",\"比奥比奥大区\",\"郑州市\",\"张家口市\",\"图木舒克市\",\"漯河市\",\"银川市\",\"湖州市\",\"贵阳市\",\"荆门市\",\"肇庆市\",\"湘潭市\",\"丹东市\",\"克拉玛依市\",\"乐山市\",\"Minas Gerais\",\"Singapore\",\"张家界市\",\"汕尾市\",\"扬州市\",\"杰尔-莫松-肖普朗\",\"玉溪市\",\"盘锦市\",\"石嘴山市\",\"保定市\",\"襄阳市\",\"Maharashtra\",\"大庆市\",\"娄底市\",\"巴中市\",\"烟台市\",\"攀枝花市\",\"沈阳市\",\"呼和浩特市\",\"泰安市\",\"林芝地区\",\"宜昌市\",\"布加勒斯特\",\"昌都地区\",\"昆明市\",\"临沧市\",\"亳州市\",\"Rio de Janeiro\",\"Shamal al Batinah\",\"萍乡市\",\"鞍山市\",\"随州市\",\"朝阳市\",\"汕头市\",\"大雅加达首都特区\",\"宁波市\",\"湛江市\",\"西双版纳傣族自治州\",\"佛山市\",\"临夏回族自治州\",\"河源市\",\"信阳市\",\"Jawa Barat\",\"山南地区\",\"Cavite\",\"保山市\",\"巴塞罗那\",\"里斯本\",\"葫芦岛市\",\"郴州市\",\"安庆市\",\"资阳市\",\"临汾市\",\"NCR\",\"阳江市\",\"河东区\",\"铜陵市\",\"鄂尔多斯市\",\"六盘水市\",\"秦皇岛市\",\"乌鲁木齐市\",\"漳州市\",\"晋中市\",\"嘉兴市\",\"鹤壁市\",\"吉隆坡\",\"抚州市\",\"铜仁地区\",\"里约热内卢\",\"鹤岗市\",\"苏州市\",\"墨西哥城\",\"益阳市\",\"中山市\",\"深圳市\",\"福州市\",\"芜湖市\",\"桂林市\",\"仙桃市\",\"Al Anbar\",\"枣庄市\",\"内江市\",\"云浮市\",\"天门市\",\"廊坊市\",\"Baghdad\",\"绍兴市\",\"达卡\",\"雄安新区\",\"陇南市\",\"海口市\",\"马斯喀特省\",\"延边朝鲜族自治州\",\"金华市\",\"滨州市\",\"Region Metropolitana de Santiago\",\"梧州市\",\"七台河市\",\"新德里\",\"六安市\",\"雅加达\",\"许昌市\",\"固原市\",\"长治市\",\"喀什地区\",\"张掖市\",\"文山壮族苗族自治州\",\"牡丹江市\",\"伊春市\",\"本溪市\",\"Ma'an\",\"淮北市\",\"昌吉回族自治州\",\"河池地区\",\"达州市\",\"伊犁哈萨克自治州\",\"酒泉市\",\"National Capital Region\",\"丽江市\",\"白山市\",\"阜新市\",\"宣城市\",\"蚌埠市\",\"宜春市\",\"Tehran\",\"大兴安岭地区\",\"仰光省\",\"大连市\",\"聊城市\",\"阿里地区\",\"凉山彝族自治州\",\"三门峡市\",\"吕梁市\",\"双鸭山市\",\"昌平区\",\"泰州市\",\"天津市\",\"铁岭市\",\"泉州市\",\"玉树藏族自治州\",\"龙岩市\",\"波哥大首都区\",\"惠州市\",\"河池市\",\"埃里温市\",\"瓜达拉哈拉\",\"白城市\",\"宿州市\",\"那曲地区\",\"莱芜市\",\"沙捞越\",\"营口市\",\"神农架林区\",\"西岸\",\"文昌市\",\"濮阳市\",\"镇江市\",\"临沂市\",\"卡拉奇\",\"考卡\",\"金山区\",\"伊斯兰堡\",\"贵安新区\",\"皮钦查\",\"邢台市\",\"萨那\",\"荆州市\",\"赣州市\",\"咸阳市\",\"连云港市\",\"新乡市\",\"Karnataka\",\"果洛藏族自治州\",\"南阳市\",\"中卫市\",\"三明市\",\"东伦敦\",\"淮南市\",\"开封市\",\"乌兰察布市\",\"定西市\",\"阿坝藏族羌族自治州\",\"韶关市\",\"汉中市\",\"Rabat-Sale-Zemmour-Zaer\",\"怀化市\",\"Esfahan\",\"通化市\",\"嘉峪关市\",\"布宜诺斯艾利斯\",\"无锡市\",\"吐鲁番市\",\"普洱市\",\"塔拉帕卡大区\",\"岳阳市\",\"大理白族自治州\",\"九江市\",\"庆阳市\",\"吴忠市\",\"贺州市\",\"博尔塔拉蒙古自治州\",\"鹰潭市\",\"威海市\",\"雅安市\",\"乌海市\",\"呼伦贝尔市\",\"阿勒泰地区\",\"和田地区\",\"辽源市\",\"通辽市\",\"赤峰市\",\"马鞍山市\",\"铜川市\",\"丽水市\",\"承德市\",\"广元市\",\"潜江市\",\"安康市\",\"阿拉善盟\",\"安曼\",\"香港\",\"北碚区\",\"舟山市\",\"潍坊市\",\"怒江傈僳族自治州\",\"Distrito Nacional\",\"兴安盟\",\"Grand Casablanca\",\"钦州市\",\"华沙\",\"北海市\",\"朔州市\",\"厦门市\",\"萨拉戈萨\",\"绵阳市\",\"辽阳市\",\"自贡市\",\"日照市\",\"吉安市\",\"池州市\",\"虚拟市\",\"景德镇市\",\"阜阳市\",\"海南藏族自治州\",\"恩施土家族苗族自治州\",\"红河哈尼族彝族自治州\",\"上饶市\",\"大同市\",\"宝鸡市\",\"焦作市\",\"崇左市\",\"Moskva\",\"马拉加\",\"伊斯坦布尔\",\"来宾市\",\"东营市\",\"南戈壁\",\"湘西土家族苗族自治州\",\"遵义市\",\"海东地区\",\"黄南藏族自治州\",\"基辅\",\"Valle del Cauca\",\"麦德林\",\"Ontario\",\"柳州市\",\"黄浦区\",\"巴音郭楞蒙古自治州\",\"克孜勒苏柯尔克孜自治州\",\"瓜亚斯\",\"齐齐哈尔市\",\"商洛市\",\"Alberta\",\"阿比亚\",\"清迈\",\"衢州市\",\"防城港市\",\"天水市\",\"温州市\",\"Sindh\",\"台州市\",\"西青区\",\"南平市\",\"巴彦淖尔市\",\"Madrid, Comunidad de\",\"榆林市\",\"黔西南布依族苗族自治州\",\"渭南市\",\"泸州市\",\"Sao Paulo\",\"重庆高新技术产业开发\",\"Annobon\",\"锡林郭勒盟\",\"衡阳市\",\"四平市\",\"河南省直辖县级行政区划\",\"宜宾市\",\"宿迁市\",\"Lefkosia\",\"Masqat\",\"布达佩斯\",\"周口市\",\"塞阿拉\",\"广安市\",\"静安区\",\"开罗\",\"昭通市\",\"永州市\",\"常德市\",\"Sulawesi Selatan\",\"贵港市\",\"南开区\",\"基多\",\"沙巴\",\"浦东新区\",\"莫斯科\",\"坎帕拉\",\"黔南布依族苗族自治州\",\"宁河区\",\"百色地区\",\"海淀区\",\"Jawa Timur\",\"哈拉雷\",\"朝阳区\",\"毕节地区\",\"Punjab\",\"班加罗尔\",\"松原市\",\"黔东南苗族侗族自治州\",\"海西蒙古族藏族自治州\",\"门多萨\",\"阿克苏地区\",\"Abuja Federal Capital Territory\",\"Al 'Aqabah\",\"Buryatiya, Respublika\",\"铜梁区\",\"安卡拉\",\"万象\",\"江津区\",\"楚雄彝族自治州\",\"大兴区\",\"武威市\",\"毕节市\",\"卡萨布兰卡\"] 2. 工单类型必须严格从以下标准工单类型列表中匹配（每个工单类型包含id和name字段）： [{\"id\":1,\"name\":\"割接\"},{\"id\":4,\"name\":\"网络整改\"},{\"id\":5,\"name\":\"网络检查\"},{\"id\":7,\"name\":\"升级\"},{\"id\":8,\"name\":\"测试\"}] 举例说明： - 如果文档标题为\"XXX网络升级割接方案\"，且版本信息板块中明确提到目标版本为\"v1.0\"，则工单类型应识别为\"升级\"，因为满足最高优先级。 - 如果文档标题为\"XXX网络割接方案\"，且版本信息板块中没有明确提到目标版本，工单类型也应识别为\"割接\"。 - 如果文档标题为\"XXX网络变更操作技术方案\"，但操作简述中明确提到\"割接实施窗口时间\"，且版本信息板块中没有明确提到目标版本,工单类型应识别为\"割接\"。 3. 工单类型识别优先级： - 最高优先级：文档版本信息板块中明确的网管目标版本或设备的目标版本（表示工单类型为\"升级\"），否则是其他类型 - 次高优先级：文档标题中明确的工单类型（如\"XXX割接方案\"表示工单类型为\"割接\"） - 再次优先级：操作简述部分明确提到的工单类型 - 最低优先级：文档其他部分提到的工单类型 4. 网络名称应尽可能准确提取，主要从文件名、标题或其他总结性文字中提取，用于后续匹配标准网络名称,网络名称注意提取完整，尽量把整句话提取出来。 5. 专业必须严格从以下标准专业中匹配： [\"MSTP\",\"服务器存储\",\"OTN\",\"接入\",\"PTN\",\"IPRAN\",\"交换机路由器\",\"SPN\",\"智网\",\"终端\",\"其他\",\"CDN\",\"STN\",\"外购件（第三方）\",\"opticalFiber光纤\"] 特别说明： - 如果文档中出现PON或者OLT,属于接入专业。 文档内容： 网络变更操作技术方案-PON 漯河移动运营商PON网络 业务割接方案 河南省技服中心 时间：2025年7月10日 对3级及以上风险要求提供变更操作方案，4级风险由本地技服根据实情自行要求。 所有项目不允许裁剪，当本次操作不涉及时，可以描述为不涉及。 三级及以上风险网络变更需要到技服技术支撑部TAC中心审核，需提前24小时报备；二级风险网络变更需要到产出线工程部审核，需提前48小时报备；一级风险网络变更需要到研发LMT审核，需提前72小时报备。同时，设置有三级（紧急）、二级（紧急）、一级（紧急）三个风险等级，此等级报备无需时间限制。对于一、二、三级网络变更操作和特殊时期（例如：集采期、投标、重大保障期、高访、集团网络巡检等）所有等级操作均需报备销售认可。 销售审核意见： 销售审核人 审核意见 周浩天 通过 省技服审核意见 省技服人员 审核人 审核意见 网络负责人 何俊辉 通过 技术主管 张赢彪 通过 请按照以下JSON格式返回提取结果： { \"province\": \"省份名称（必须从标准列表中匹配）\", \"city\":\"地市名称（必须从标准列表中匹配）\", \"networkName\": \"网络名称\", \"orderType\": 工单类型ID（必须从标准列表中匹配）, \"orderTypeName\": \"工单类型名称（必须与标准列表中id对应的name完全一致）\", \"productSpe\": \"IPRAN\" }"}],"temperature":0.7,"stream":false} 
2025-08-14 08:45:53.1413 | 请求URL: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:45:53.1413 | Start processing HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:45:53.1413 | Sending HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:45:55.7057 | Received HTTP response headers after 2559.0682ms - 200 
2025-08-14 08:45:55.7057 | End processing HTTP request after 2564.3303ms - 200 
2025-08-14 08:45:55.7057 | API响应: {"choices":[{"message":{"role":"assistant","content":"```json\n{\n  \"province\": \"河南省\",\n  \"city\": \"漯河市\",\n  \"networkName\": \"漯河移动运营商PON网络\",\n  \"orderType\": 1,\n  \"orderTypeName\": \"割接\",\n  \"productSpe\": \"接入\"\n}\n```"},"finish_reason":"stop","index":0,"logprobs":null}],"object":"chat.completion","usage":{"prompt_tokens":2952,"completion_tokens":61,"total_tokens":3013,"prompt_tokens_details":{"cached_tokens":0}},"created":1755132356,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-1d1c2f11-276f-9ffc-a1a5-a7ea4a84c3e8"} 
2025-08-14 08:45:55.7137 | 提取基础信息完成，省份: 河南省，网络名称: 漯河移动运营商PON网络，工单类型: 割接 
2025-08-14 08:45:55.7137 | ✅ 第一阶段完成，耗时: 2612.0885ms 
2025-08-14 08:45:55.7137 | 🔄 第二阶段开始：网络名称匹配 
2025-08-14 08:45:55.7277 | 开始匹配标准网络名称: 漯河移动运营商PON网络 
2025-08-14 08:45:55.7646 | 文本优化：原始长度 1014，优化后长度 998，节省 16 个字符 (1.58%) 
2025-08-14 08:45:55.7646 | 请求体: {"model":"qwen-plus","messages":[{"role":"system","content":"你是一个专业的网络名称匹配专家，擅长将用户提供的网络名称与标准网络名称进行匹配。请尽可能找到最匹配的标准名称。"},{"role":"user","content":"请根据以下信息，匹配最合适的标准网络名称。 原始网络名称：漯河移动运营商PON网络 省份：河南省 地市：漯河市 专业：接入 标准网络列表（只包含本省份，包含网络名、运营商和专业信息）： [{\"NetName\":\"河南省漯河市联通接入本地网\",\"Province\":\"河南省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"接入\",\"Operator\":\"联通\"},{\"NetName\":\"河南省漯河市移动接入本地网\",\"Province\":\"河南省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"接入\",\"Operator\":\"移动\"},{\"NetName\":\"河南省漯河市联通OTN本地DWDM网\",\"Province\":\"河南省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"OTN\",\"Operator\":\"联通\"},{\"NetName\":\"河南省漯河市联通智能城域网本地网\",\"Province\":\"河南省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"智网\",\"Operator\":\"联通\"},{\"NetName\":\"河南省漯河市联通OTN本地网\",\"Province\":\"河南省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"OTN\",\"Operator\":\"联通\"},{\"NetName\":\"河南省漯河市联通MSTP本地网\",\"Province\":\"河南省\",\"NetProperties\":\"本地\",\"ProductSpe\":\"MSTP\",\"Operator\":\"联通\"}] 可选专业列表： [\"接入\",\"OTN\",\"智网\",\"MSTP\"] 可选运营商列表： [\"联通\",\"移动\"] 匹配规则： 1. 只在指定省份和地市下查找。 2. 优先精确匹配网络名称。 3. 如果网络名称不匹配： - 如果网络名称中不包含专业信息，则使用传入的专业参数。 - 如果网络名称中包含专业信息，则使用网络名称中的专业，再结合标准网络中的专业、运营商和省市名进行辅助判断。 4. 返回最匹配的标准网络名称、运营商和专业信息，以及置信度（0-100）。 返回格式： { \"standardNetworkName\": \"\", \"matchConfidence\": 100 }"}],"temperature":0.7,"stream":false} 
2025-08-14 08:45:55.7736 | 请求URL: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:45:55.7736 | Start processing HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:45:55.7736 | Sending HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:45:57.3814 | Received HTTP response headers after 1600.6954ms - 200 
2025-08-14 08:45:57.3839 | End processing HTTP request after 1607.246ms - 200 
2025-08-14 08:45:57.3839 | API响应: {"choices":[{"message":{"role":"assistant","content":"{\n  \"standardNetworkName\": \"河南省漯河市移动接入本地网\",\n  \"matchConfidence\": 100\n}"},"finish_reason":"stop","index":0,"logprobs":null}],"object":"chat.completion","usage":{"prompt_tokens":465,"completion_tokens":29,"total_tokens":494,"prompt_tokens_details":{"cached_tokens":0}},"created":1755132357,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-bdd7e6f7-3e5a-9565-b9fc-cbd13a6687da"} 
2025-08-14 08:46:00.3433 | AI匹配标准网络名称完成: 河南省漯河市移动接入本地网，匹配置信度: 100% 
2025-08-14 08:46:00.3882 | ✅ 第二阶段完成，耗时: 4660.2601ms 
2025-08-14 08:46:00.3882 | 🔄 第三阶段开始：4线程并发处理，工单类型: 割接 
2025-08-14 08:46:00.3882 | 🔄 线程1开始处理第一部分（字段6,7） 
2025-08-14 08:46:00.3882 | 文本优化：原始长度 1152，优化后长度 1076，节省 76 个字符 (6.60%) 
2025-08-14 08:46:00.3999 | 请求体: {"model":"qwen-plus","messages":[{"role":"system","content":"你是一个专业的QMO工单解析助手，擅长从文档中提取详细的工单信息，包括风险级别、计划时间、任务内容等。请按照指定的JSON格式返回结果。"},{"role":"user","content":"请根据以下文档内容，提取QMO工单报备所需的详细信息。 基础信息： - 工单类型：非升级 - 处理部分：第一部分（开头到变更操作概述） 请从文档中提取以下信息： 6. 工单主题 工单主题 -工单主题一般出现在内容开头部分，用来描述整个文档的具体内容。 -例如：7月11日0：00-04：00浙江绍兴网络2LN4、1LN4单盘升级操作网络变更操作技术方案-OTN-V3.1（此例不能作为最后的提取内容） 7. 抄送人 文档中无相关字段 - 直接赋值空数组 文档内容： 网络变更操作技术方案-PON 漯河移动运营商PON网络 业务割接方案 河南省技服中心 时间：2025年7月10日 对3级及以上风险要求提供变更操作方案，4级风险由本地技服根据实情自行要求。 所有项目不允许裁剪，当本次操作不涉及时，可以描述为不涉及。 三级及以上风险网络变更需要到技服技术支撑部TAC中心审核，需提前24小时报备；二级风险网络变更需要到产出线工程部审核，需提前48小时报备；一级风险网络变更需要到研发LMT审核，需提前72小时报备。同时，设置有三级（紧急）、二级（紧急）、一级（紧急）三个风险等级，此等级报备无需时间限制。对于一、二、三级网络变更操作和特殊时期（例如：集采期、投标、重大保障期、高访、集团网络巡检等）所有等级操作均需报备销售认可。 销售审核意见： 销售审核人 审核意见 周浩天 通过 省技服审核意见 省技服人员 审核人 审核意见 网络负责人 何俊辉 通过 技术主管 张赢彪 通过 ### 所有提取的内容不能自己编造，只能从文档中提取，宁缺勿错的原则！ ### 请严格按照以下JSON格式返回提取结果,JSON里的举例不能作为结果返回，如果没有置为空(注意，格式里的具体内容和文档无关，请你只提取文档里的内容)： { \"title\": \"工单主题\", // 6.工单主题 \"copyUsersList\":[ {\"id\":null,\"userType\":1,\"invUserName\":\"<EMAIL>\",\"invNickName\":\"肖绍谦\",\"invPhone\":\"18627827716\",\"invEmail\":\"<EMAIL>\",\"invPostCode\":4,\"invPostName\":\"软件工程师\",\"invDept\":\"武汉烽火技术服务有限公司-数字技术部\",\"invUserLevel\":null} ], // 7.抄送人列表（绝对不能使用示例数据或者捏造数据） }"}],"temperature":0.7,"stream":false} 
2025-08-14 08:46:00.3999 | 请求URL: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:46:00.3999 | Start processing HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:46:00.3999 | Sending HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:46:00.4354 | 🔄 线程2开始处理第二部分（字段1,2,3,8,9,10,16） 
2025-08-14 08:46:00.4354 | 文本优化：原始长度 5017，优化后长度 4747，节省 270 个字符 (5.38%) 
2025-08-14 08:46:00.4354 | 请求体: {"model":"qwen-plus","messages":[{"role":"system","content":"你是一个专业的QMO工单解析助手，擅长从文档中提取详细的工单信息，包括风险级别、计划时间、任务内容等。请按照指定的JSON格式返回结果。"},{"role":"user","content":"请根据以下文档内容，提取QMO工单报备所需的详细信息。 基础信息： - 工单类型：非升级 - 处理部分：第二部分（变更操作概述） 请从文档中提取以下信息： 1. 风险级别 必须严格从以下标准省份列表中匹配：一级(紧急),一级,二级(紧急),二级,三级(紧急),三级,四级(紧急),四级 2. 是否重保 文档中无相关字段 - bool类型 - 直接赋值false 3. 产品线附加信息 8. 网管信息集合 据文档中的版本信息，网管版本信息表格提取 - 仅提取网管系列、网管版本和网管补丁。 - 网管系列应包含网管系统的名称，主版本即为网管版本，补丁信息为网管补丁。 **注意**：在提取时需关注相近词汇的识别，以确保信息的准确性。 9. 设备信息集合 据文档中的版本信息，设备版本信息表格提取 - 设备型号(或者类型) - 板卡名称 - 板卡物料号（注意：硬件版本即为板卡物料号） - 软件版本 - 软件编译日期（格式为YYYY-MM-DD） **注意**：有些表格中，软件版本和编译日期可能写在一起，需要进行分离，在提取时需关注相近词汇的识别，以确保信息的准确性。如果没有匹配的信息，就置为空 10. 变更工作量集合 据文档中的操作对象，进行提取 **实施对象**： - 即表格中的设备类型。 **本次实施数量**： - 即表格中的数量，数字格式。 **注意**：在提取时需关注相近词汇的识别，以确保信息的准确性。 16. 工单内容 根据文档中的内容，总结该文档的目的和原因 文档内容： 变更操作概述 操作简述 在此描述实施本次操作的类型、原因和主要内容简述，割接实施窗口时间。 操作类型：AN6000-17设备 操作原因：因功能需要升级设备版本 内容简述：升级以下51台6000设备。 网元名称 网元IP地址 漯河市临颍县公司一楼机房-OLT322-FW-AN6000-17 ************* 漯河市临颍盈翠半岛机房-OLT323-FW-AN6000-17 ************* 漯河市临颍南街村机房-OLT326-FW-AN6000-17 ************** 漯河市临颍老局机房-OLT327-FW-AN6000-17 ************** 漯河市临颍火车站机房-OLT328-FW-AN6000-17 ************** 漯河市临颍第二出口局机房-OLT331-FW-AN6000-17 ************** 漯河市临颍七里机房-OLT330-FW-AN6000-17 ************* 漯河市临颍黄龙机房-OLT332-FW-AN6000-17 ************* 漯河市临颍北街机房-OLT336-FW-AN6000-17 ************* 漯河市临颍源汇宾馆机房-OLT373-FW-AN6000-17 ************** 漯河市临颍北街路口机房-OLT374-FW-AN6000-17 ************** 漯河市临颍台陈2.5G机房-OLT344-FW-AN6000-17 ************** 漯河市临颍商桥2.5G机房-OLT375-FW-AN6000-17 ************* 漯河市临颍三家店2.5G机房-OLT349-FW-AN6000-17 ************** 漯河市临颍县瓦店2.5G机房-OLT346-FW-AN6000-17 172.24.202.154 漯河市临颍王孟2.5G机房-OLT350-FW-AN6000-17 *************6 漯河市临颍皇帝庙2.5G机房-OLT354-FW-AN6000-17 172.24.202.218 漯河市临颍巨陵2.5G机房-OLT347-FW-AN6000-17 172.24.202.162 漯河市临颍陈庄2.5G机房-OLT355-FW-AN6000-17 172.24.202.226 漯河市临颍固厢2.5G机房-OLT352-FW-AN6000-17 172.24.202.202 漯河市临颍大郭2.5G机房-OLT351-FW-AN6000-17 172.24.202.194 漯河市临颍石桥2.5G机房-OLT353-FW-AN6000-17 172.24.202.210 漯河市临颍杜曲机房-OLT371-FW-AN6000-17 172.24.203.98 漯河市临颍铁西机房-OLT372-FW-AN6000-17 172.24.203.106 漯河市临颍王岗2.5G机房-OLT345-FW-AN6000-17 172.24.202.146 漯河市临颍繁城2.5G机房-OLT343-FW-AN6000-17 172.24.202.130 漯河市临颍窝城2.5G机房-OLT348-FW-AN6000-17 172.24.202.170 漯河市临颍第二出口局机房-OLT387-FW-AN6000-17 172.24.203.218 漯河市舞阳吴城2.5G机房-OLT325-FW-AN6000-17 172.24.200.114 漯河市舞阳东湖花园2.5G机房-OLT321-FW-AN6000-17 172.24.202.2 漯河市舞阳舞北2.5G机房-OLT334-FW-AN6000-17 172.24.202.42 漯河市舞阳北舞渡2.5G机房-OLT329-FW-AN6000-17 172.24.201.98 漯河市舞阳太尉机房-OLT333-FW-AN6000-17 172.24.202.66 漯河市舞阳移动办公楼机房-OLT335-FW-AN6000-17 172.24.202.50 漯河市舞阳南京路机房-OLT370-FW-AN6000-17 172.24.203.90 漯河市舞阳上海路机房-OLT367-FW-AN6000-17 172.24.203.66 漯河市舞阳武装部机房-OLT366-FW-AN6000-17 172.24.203.58 漯河市舞阳县政府机房-OLT368-FW-AN6000-17 172.24.203.74 漯河市舞阳金都机房-OLT369-FW-AN6000-17 172.24.203.82 漯河市舞阳莲花2.5G机房-OLT356-FW-AN6000-17 172.24.202.234 漯河市舞阳孟寨2.5G机房-OLT358-FW-AN6000-17 172.24.202.250 漯河市舞阳保和2.5G机房-OLT360-FW-AN6000-17 172.24.203.10 漯河市舞阳太尉2.5G机房-OLT359-FW-AN6000-17 172.24.203.2 漯河市舞阳马村2.5G机房-OLT361-FW-AN6000-17 172.24.203.18 漯河市舞阳侯集2.5G机房-OLT362-FW-AN6000-17 172.24.203.26 漯河市舞阳姜店2.5G机房-OLT363-FW-AN6000-17 172.24.203.34 漯河市舞阳章化2.5G机房-OLT365-FW-AN6000-17 172.24.203.50 漯河市舞阳九街2.5G机房-OLT364-FW-AN6000-17 172.24.203.42 漯河市舞阳辛安2.5G机房-OLT357-FW-AN6000-17 ************** 漯河市舞阳老局-OLT376-FW-AN6000-17 ************** 漯河市舞阳老局机房-OLT385-FW-AN6000-17 ************** 割接时间：7月11、16日 1：00-06：00 影响评估 在此描述实施本次操作对客户的影响程度和风险操作级别。 风险等级：四级（参考风险分级标准确定，并和OA报备工单一致） 影响范围：本次变更操作，预期会影响各类业务多少量。详细影响范围如下： 业务类型 数量 宽带 18261 语音 2512 IPTV 12311 北向接口采集 不涉及 其他（专线等） 1580 …… 风险评估（重点） 在此描述本次操作预先识别、可能发生的风险，以及应对措施。以下为示范，根据现场情况填写： 序号 风险 可能性 影响程度 应对措施 1 宽带业务、语音业务和IPTV业务都会受影响 高 高 受影响时间约为20~40分钟 2 操作对象 在此描述本次操作的对象和数量。 本次操作涉及如下对象：以下为示范，不涉及的必须删除,未包含必须添加 设备类型 数量 AN6000-17 21 版本信息 在此描述本次操作对象的版本信息和目标版本信息，仅针对软件升级的场景。操作不涉及版本信息的可以直接填写不涉及即可。 软件版本信息 名称 升级前版本 升级后软件版本 AN6000-17 OLT V105R002SP0 V105R008SP0 HCWA RP1400 RP1600 GMOA RP1400 RP1600 GPOA RP1400 RP1400 网管版本信息 网管系统 主版本 补丁信息 补丁时间 UNM2000 V2R7 SPH0805 20240110 成功标准 在此描述如何验证本次操作是否成功。以下为示范，不涉及的必须删除,未包含必须添加 成功标准： 业务确认正常 操作达到预期目标 升级后软件版本为V105R008SP0 ### 所有提取的内容不能自己编造，只能从文档中提取，宁缺勿错的原则！ ### 请严格按照以下JSON格式返回提取结果,JSON里的举例不能作为结果返回，如果没有置为空(注意，格式里的具体内容和文档无关，请你只提取文档里的内容)： { \"riskLevel\": \"一级\", // 1.风险级别 \"isReinstated\": false, // 2.是否重保 \"lineRemark\": \"产品附加信息\", // 3.产品线附加信息 \"involveNmpinfos\": [{\"id\":null,\"orderKind\":3,\"nmpSeries\":\"网管系列（当前版本）\",\"nmpVersion\":\"网管版本（当前版本）\",\"nmpPatches\":\"网管补丁（当前版本）\",\"tarNmpSeries\":\"网管系列（目标版本）\",\"tarNmpVersion\":\"网管版本（目标版本）\",\"tarNmpPatch\":\"网管补丁（目标版本）\"}], // 8.网管信息集合（绝对不能使用示例数据或者捏造数据） \"involveDevices\": [{\"id\":null,\"orderKind\":3,\"invDeviceType\":\"对象-设备型号\",\"invBoardName\":\"对象-板卡名称\",\"invHardVersion\":\"对象-板卡物料号\",\"invSoftVersion\":\"软件版本-当前\",\"compileTime\":\"软件编译日期-当前版本\",\"tarInvSoftVersion\":\"软件版本-目标版本\",\"tarCompileTime\":\"软件编译日期-目标版本\"}], // 9.设备信息集合（绝对不能使用示例数据或者捏造数据） \"involveProducts\": [{\"id\":null,\"orderKind\":3,\"objectCount\":12,\"objectName\":\"实施对象\"}], // 10.变更工作量集合（绝对不能使用示例数据或者捏造数据） \"taskContent\": \"\", // 16.任务内容 }"}],"temperature":0.7,"stream":false} 
2025-08-14 08:46:00.4567 | 请求URL: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:46:00.4605 | Start processing HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:46:00.4605 | Sending HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:46:00.4943 | 🔄 线程3开始处理第三部分（字段11,12,13,14,15,17） 
2025-08-14 08:46:00.4943 | 文本优化：原始长度 2904，优化后长度 2676，节省 228 个字符 (7.85%) 
2025-08-14 08:46:00.4943 | 请求体: {"model":"qwen-plus","messages":[{"role":"system","content":"你是一个专业的QMO工单解析助手，擅长从文档中提取详细的工单信息，包括风险级别、计划时间、任务内容等。请按照指定的JSON格式返回结果。"},{"role":"user","content":"请根据以下文档内容，提取QMO工单报备所需的详细信息。 基础信息： - 工单类型：非升级 - 处理部分：第三部分（变更操作准备） 请从文档中提取以下信息： 11. 默认客户信息 **注意**：根据文档中的【变更操作准备】-【人员安排】-【客户变更小组名单】部分的表格进行提取（不要从客户审核的表格中查找）： - 查找包含\"姓名\"、\"电话\"、\"职责\"等列的客户人员表格 - 选取表格中的第一行客户数据（排除表头） - 提取该客户的姓名、电话、邮箱信息 - 如果表格中没有邮箱列或邮箱为空，则邮箱字段设为空字符串 - 如果电话（或者相近的字段，比如手机号，联系方式等）为空，则电话字段设为空字符串 - 绝对不能编造任何数据 - 返回格式：{\"email\":\"客户邮箱或空\",\"nickName\":\"客户姓名\",\"phoneNumber\":\"客户电话或空\"} 12. 默认客户邮箱 **注意**：从第11项提取的默认客户信息中获取邮箱字段，如果没有邮箱信息则返回空字符串（结果和11项中的email结果保持一致）。 13. 默认客户电话 **注意**：从第11项提取的默认客户信息中获取电话字段，如果没有电话信息则返回空字符串（结果和11项中的phoneNumber结果保持一致）。 14. 实施人信息（支持多个） 据文档中的操作对象，烽火现场实施小组名单中表格进行提取，当职责为实施负责人，负责本次操作实施 **姓名**： - 即实施负责人姓名。 **电话**： - 即实施负责人电话。 15. 审核人信息（支持多个） 据文档中的操作对象，烽火现场实施小组名单中表格进行提取，当职责为负责审核实施操作步骤过程 **姓名**： - 即审核人姓名。 **电话**： - 即审核人电话。 17. 所有客户信息 **注意**：根据文档中的 人员安排-客户变更小组名单 下方的第一个表， 这个表中所有的客户信息（客户姓名，客户邮箱，客户电话）。 - 是一个数组对象，格式如下[{\"invNickName\":\"\",\"invPhone\":,\"invEmail\":\"\"}] 文档内容： 变更操作准备 人员安排 在此描述参与本次操作的操作人员、责任人员名单和通信信息。 客户变更小组名单 姓名 电话 职责 王晓伟 13903950637 全业务班长 孟盼盼 19839501231 OLT负责人 付金美 13721320138 BRAS负责人 烽火现场小组名单【注：对外提供时，属性列去除】 姓名 电话 职责 何俊辉 13140650255 总负责人 张赢彪 18538922264 技术接口负责人，负责参加本次操作实施，对其成功负责。 李金波 15639591221 负责完成具体操作。 董振邦 13007627232 监督及保障人员。 烽火远程支撑保障名单 姓名 电话 职责 部门 张赢彪 18538922264 技术保障，后台支撑. 河南技服 时间窗口 在此给出客户许可的操作时间窗口，如果存在多天分段操作，用分段计划描述。其中，干扰识别，请描述操作时间段内，相关联的其他专业、省干、核心网是否有冲突的操作，需明确确认的对象和结果。比如：经和地市公司网络部、并通过地市公司向省网络部、地市无线中心确认，申请时间窗口内无其他专业操作动作。以下为示范： 授权操作时间窗口 时间 工作内容 技术责任人 00：01-06：00 先升级单盘，再升级备主控，然后主控 张赢彪 内部控制的操作时间窗口【注：对外该部分可以去除】 普通操作预留检查时间2小时，正常可操作时间0：00-4：00 失控风险控制时间2小时，出现不可控风险，启动应急预案时间为4：00 其他准备 需填写下面关键项的准备情况，且不限于这些内容，办事处根据实际情况新增 软件准备 软件类型 版本信息 HCWA RP1600 GMOA RP1600 GPOA RP1400 备份（检查结果上传系统） 涉及OLT变更的，需导出并备份OLT配置文件 涉及网管变更的，需完整备份网管文件 备件 网络变更涉及到的单盘均需有相应的备件 远程接入 提前搭建远程环境，便于远程支撑时 类型 说明 接入方案 方案1：机房现场客户端操作 账号检测 网管变更操作申请管理员权限账号 割接前设备运行情况检查（检查结果上传系统） 序号 检查项目 关键动作 检查标准 是否正常 1 设备各单盘与主控通信是否正常 Ping 各单盘的私有地址 无丢包 是 2 网管各服务状态正常 查看服务进程 服务均已启动 是 3 告警核查 导出告警 生成告警导出文件 是 4 OLT性能核查 查看主控盘和业务单盘CPU和内存利用率 内存占用率不得高于95%，CPU不得高于80% 是 5 配置保存检查 重新保存flash 无配置未保存告警 是 如存在影响割接的异常，需和用户沟通确定下一步计划； ### 所有提取的内容不能自己编造，只能从文档中提取，宁缺勿错的原则！ ### 请严格按照以下JSON格式返回提取结果,JSON里的举例不能作为结果返回，如果没有置为空(注意，格式里的具体内容和文档无关，请你只提取文档里的内容)： { \"customName\": {\"email\":\"<EMAIL>\",\"nickName\":\"xiaoshaoqian\",\"phoneNumber\":\"12324567131\"}, // 11.默认客户信息（数据不能捏造和使用示例数据） \"customEmail\": \"<EMAIL>\", // 12.客户邮箱（结果和11项中的email字段的结果完全保持一致） \"customPhone\": \"12324567131\", // 13.客户电话（结果和11项中的phoneNumber字段的结果完全保持一致） \"taskUsers\": [{\"nickName\":\"朱浩\",\"userPhone\":\"13787031546\"}], // 14.实施人信息（绝对不能使用示例数据或者捏造数据） \"checkUsers\": [{\"nickName\":\"肖绍谦\",\"userPhone\":\"18627827716\"}], // 15.审核人（绝对不能使用示例数据或者捏造数据） \"customUsers\":[{\"invNickName\":\"肖绍谦\",\"invPhone\":12324567131,\"invEmail\":\"<EMAIL>\"}] //17.所有客户信息（绝对不能使用示例数据或者捏造数据） }"}],"temperature":0.7,"stream":false} 
2025-08-14 08:46:00.5094 | 请求URL: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:46:00.5094 | Start processing HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:46:00.5094 | Sending HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:46:00.5409 | 🔄 线程4开始处理第四部分（字段4,5） 
2025-08-14 08:46:00.5409 | 文本优化：原始长度 1851，优化后长度 1728，节省 123 个字符 (6.65%) 
2025-08-14 08:46:00.5409 | 请求体: {"model":"qwen-plus","messages":[{"role":"system","content":"你是一个专业的QMO工单解析助手，擅长从文档中提取详细的工单信息，包括风险级别、计划时间、任务内容等。请按照指定的JSON格式返回结果。"},{"role":"user","content":"请根据以下文档内容，提取QMO工单报备所需的详细信息。 基础信息： - 工单类型：非升级 - 处理部分：第四部分（操作实施步骤） 请从文档中提取以下信息： 4. 计划开始时间 从文档中的【操作步骤】里进行提取： - 从操作步骤时间段中提取开始时间，并减去2个小时。 - 格式为YYYY-MM-DD HH:MM:SS。 5. 计划结束时间 从文档中的【操作步骤】里进行提取： - 从操作步骤时间段中提取结束时间，并加上2个小时。 - 格式为YYYY-MM-DD HH:MM:SS。 文档内容： 操作实施步骤（重点） 设备操作步骤 报备人按照下面整体时间控制节点补充详细的操作步骤 在此详细描述实施本次操作的具体步骤，要求包括操作内容和时间预估。 序号 时间段 操作内容 预期结果 风险应对 责任人 1 0:00-0:59 进入机房做操作前准备 李金波 2 0:59-4:00 开始进行相关操作 李金波 3 4:00-4:20 核查业务 正常运行 无风险 李金波 4 4:20-6:00 客户验证，宣告离场 李金波 升级操作 OLT升级步骤 保存OLT配置至flash; 导出OLT配置到网管，检查配置脚本是否完整，配置脚本不完整则需要补充完整; 记录下表中各项信息，以备在升级后进行用户业务比对和恢复： 序号 操作项目 说明 1 记录上联端口流量 记录工作的上联端口流量计数，用于系统复位后业务核对 2 记录ONU在线状态 用于升级后的状态核对 3 记录IAD注册信息 用于升级后的语音业务核对 4 记录槽位MAC表 用于升级后的数据业务核对 将升级软件包拷贝到网管服务器和本地便携电脑； 通过网管服务器将线卡盘软件到RP1600; 通过网管服务器将主控盘（先备盘，后主盘）软件升级到目标版本； 重启整个设备使升级文件生效; 查看线卡盘的软件版本号是否目标版本。如版本号不正常，请仔细核对升级文件，再次进行升级。 查看主控的软件版本是否为否目标版本。如版本号不正常，请仔细核对升级文件，再次进行升级操作。 检查ONU上配置是否获取正确，并仔细检查业务是否正常。 网管变更操作 变更后业务验证方案（每类业务核查情况全部上传系统） 在此描述操作实施后需要进行测试和验证的方案，包括内部测试、业务确认以及客户确认的对象、方法。 增删查改验证： 随机新增各类型业务1条，确认增加业务正常。 对现存业务随机选取进行修改操作，确认修改有效。 删除新增测试业务，确认删除业务正常。 北向接口功能确认： 自测各接口功能正常 状态查询： 对涉及变更的设备进行设备状态、性能、告警和业务查询，确认可正常查询 客户确认 与客户确认，确保所有功能业务均正常才能宣告离场 应急预案（重点） 在此描述意外情况下的应急预案。意外情况是指未能在规定时间内达成预期目标、出现不可控影响扩散等情况。应当包括应急预案的启动条件、采取应急预案的步骤。对于一二级风险，要求有双预案可选。 启动条件： 当满足以下任一条件时，要求必须启动应急预案： 在操作窗口结束前2小时，如X点整（请根据实际窗口时间确定），出现操作严重滞后，操作预计无法完成。 操作过程中，任何时候出现本次操作引发的、不在预期范围内的设备故障。 严重影响客户感知且客户要求必须启动应急预案。 预案： 硬件故障，可尝试更换；软件问题，可尝试回退至原有版本。如无法独立解决应及时联系总部保障人员处理。 步骤 操作内容 1 回退到原版本 2 回退到原配置 3 割接到其他节点 4...... 必选 倒回后测试：按照操作后测试进行测试验证网络各项功能恢复 必选 倒回后确认：按照确认方案进行准确确认 ### 所有提取的内容不能自己编造，只能从文档中提取，宁缺勿错的原则！ ### 请严格按照以下JSON格式返回提取结果,JSON里的举例不能作为结果返回，如果没有置为空(注意，格式里的具体内容和文档无关，请你只提取文档里的内容)： { \"planStartTime\": \"2025-07-14 10:46:10\", // 4.计划开始时间 \"planEndTime\": \"2025-08-13 10:46:14\", // 5.计划结束时间 }"}],"temperature":0.7,"stream":false} 
2025-08-14 08:46:00.5409 | 请求URL: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:46:00.5562 | Start processing HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:46:00.5562 | Sending HTTP request POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions 
2025-08-14 08:46:02.0422 | Received HTTP response headers after 1632.6601ms - 200 
2025-08-14 08:46:02.0422 | End processing HTTP request after 1637.9353ms - 200 
2025-08-14 08:46:02.0422 | API响应: {"choices":[{"message":{"role":"assistant","content":"```json\n{\n  \"title\": \"网络变更操作技术方案-PON 漯河移动运营商PON网络 业务割接方案\",\n  \"copyUsersList\": []\n}\n```"},"finish_reason":"stop","index":0,"logprobs":null}],"object":"chat.completion","usage":{"prompt_tokens":705,"completion_tokens":40,"total_tokens":745,"prompt_tokens_details":{"cached_tokens":0}},"created":1755132362,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-c4ea8f65-75a5-995f-8902-cec4143e8549"} 
2025-08-14 08:46:02.7530 | Received HTTP response headers after 2193.0282ms - 200 
2025-08-14 08:46:02.7530 | End processing HTTP request after 2199.8944ms - 200 
2025-08-14 08:46:02.7587 | API响应: {"choices":[{"message":{"role":"assistant","content":"```json\n{\n  \"planStartTime\": \"2025-07-14 22:59:00\",\n  \"planEndTime\": \"2025-07-15 08:00:00\"\n}\n```"},"finish_reason":"stop","index":0,"logprobs":null}],"object":"chat.completion","usage":{"prompt_tokens":1196,"completion_tokens":58,"total_tokens":1254,"prompt_tokens_details":{"cached_tokens":0}},"created":1755132363,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-2df82dff-2276-9338-ba16-d7d6164ed3a2"} 
2025-08-14 08:46:07.0604 | Received HTTP response headers after 6544.0317ms - 200 
2025-08-14 08:46:07.0604 | End processing HTTP request after 6550.1564ms - 200 
2025-08-14 08:46:07.0604 | API响应: {"choices":[{"message":{"role":"assistant","content":"```json\n{\n  \"customName\": {\n    \"email\": \"\",\n    \"nickName\": \"王晓伟\",\n    \"phoneNumber\": \"13903950637\"\n  },\n  \"customEmail\": \"\",\n  \"customPhone\": \"13903950637\",\n  \"taskUsers\": [\n    {\n      \"nickName\": \"张赢彪\",\n      \"userPhone\": \"18538922264\"\n    }\n  ],\n  \"checkUsers\": [\n    {\n      \"nickName\": \"何俊辉\",\n      \"userPhone\": \"13140650255\"\n    }\n  ],\n  \"customUsers\": [\n    {\n      \"invNickName\": \"王晓伟\",\n      \"invPhone\": 13903950637,\n      \"invEmail\": \"\"\n    },\n    {\n      \"invNickName\": \"孟盼盼\",\n      \"invPhone\": 19839501231,\n      \"invEmail\": \"\"\n    },\n    {\n      \"invNickName\": \"付金美\",\n      \"invPhone\": 13721320138,\n      \"invEmail\": \"\"\n    }\n  ]\n}\n```"},"finish_reason":"stop","index":0,"logprobs":null}],"object":"chat.completion","usage":{"prompt_tokens":1712,"completion_tokens":273,"total_tokens":1985,"prompt_tokens_details":{"cached_tokens":0}},"created":1755132367,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-889641d6-0ec9-9853-9785-f0ddf6c37ecb"} 
2025-08-14 08:46:13.7117 | Received HTTP response headers after 13248.2433ms - 200 
2025-08-14 08:46:13.7117 | End processing HTTP request after 13254.5751ms - 200 
2025-08-14 08:46:13.7117 | API响应: {"choices":[{"message":{"role":"assistant","content":"```json\n{\n  \"riskLevel\": \"四级\",\n  \"isReinstated\": false,\n  \"lineRemark\": \"\",\n  \"involveNmpinfos\": [\n    {\n      \"id\": null,\n      \"orderKind\": 3,\n      \"nmpSeries\": \"UNM2000\",\n      \"nmpVersion\": \"V2R7\",\n      \"nmpPatches\": \"SPH0805\",\n      \"tarNmpSeries\": \"\",\n      \"tarNmpVersion\": \"\",\n      \"tarNmpPatch\": \"\"\n    }\n  ],\n  \"involveDevices\": [\n    {\n      \"id\": null,\n      \"orderKind\": 3,\n      \"invDeviceType\": \"AN6000-17\",\n      \"invBoardName\": \"\",\n      \"invHardVersion\": \"\",\n      \"invSoftVersion\": \"OLT V105R002SP0\",\n      \"compileTime\": \"\",\n      \"tarInvSoftVersion\": \"V105R008SP0\",\n      \"tarCompileTime\": \"\"\n    },\n    {\n      \"id\": null,\n      \"orderKind\": 3,\n      \"invDeviceType\": \"HCWA\",\n      \"invBoardName\": \"\",\n      \"invHardVersion\": \"\",\n      \"invSoftVersion\": \"RP1400\",\n      \"compileTime\": \"\",\n      \"tarInvSoftVersion\": \"RP1600\",\n      \"tarCompileTime\": \"\"\n    },\n    {\n      \"id\": null,\n      \"orderKind\": 3,\n      \"invDeviceType\": \"GMOA\",\n      \"invBoardName\": \"\",\n      \"invHardVersion\": \"\",\n      \"invSoftVersion\": \"RP1400\",\n      \"compileTime\": \"\",\n      \"tarInvSoftVersion\": \"RP1600\",\n      \"tarCompileTime\": \"\"\n    },\n    {\n      \"id\": null,\n      \"orderKind\": 3,\n      \"invDeviceType\": \"GPOA\",\n      \"invBoardName\": \"\",\n      \"invHardVersion\": \"\",\n      \"invSoftVersion\": \"RP1400\",\n      \"compileTime\": \"\",\n      \"tarInvSoftVersion\": \"RP1400\",\n      \"tarCompileTime\": \"\"\n    }\n  ],\n  \"involveProducts\": [\n    {\n      \"id\": null,\n      \"orderKind\": 3,\n      \"objectCount\": 51,\n      \"objectName\": \"AN6000-17\"\n    }\n  ],\n  \"taskContent\": \"因功能需要升级设备版本，升级以下51台AN6000设备。\"\n}\n```"},"finish_reason":"stop","index":0,"logprobs":null}],"object":"chat.completion","usage":{"prompt_tokens":3690,"completion_tokens":556,"total_tokens":4246,"prompt_tokens_details":{"cached_tokens":0}},"created":1755132374,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-8faef8e8-2c39-9492-a3c7-2324cae4724b"} 
2025-08-14 08:46:13.7117 | ✅ 线程1完成，耗时: 1653.6716ms 
2025-08-14 08:46:13.7117 | ✅ 线程2完成，耗时: 13283.1317ms 
2025-08-14 08:46:13.7275 | ✅ 线程3完成，耗时: 6572.002ms 
2025-08-14 08:46:13.7275 | ✅ 线程4完成，耗时: 2217.4426ms 
2025-08-14 08:46:13.7275 | ✅ 第三阶段完成，最大耗时: 13283.1317ms，总耗时: 13338.8903ms 
2025-08-14 08:46:13.7275 | 🔄 开始结果合并 
2025-08-14 08:46:23.9693 | 结果合并成功，合并了4/4个部分的结果 
2025-08-14 08:46:23.9693 | ✅ 结果合并完成，耗时: 10237.5451ms 
2025-08-14 08:46:23.9779 | ✅ AI智能填报完成，工单类型: 割接，总耗时: 33552.2148ms 
2025-08-14 08:50:00.2066 | [2025-08-14 08:50:00]定时任务SyncReadEmailJob执行失败：False 
2025-08-14 08:59:48.4967 | Request:/api/qmov2/ai/fillform 
2025-08-14 08:59:48.5595 | 接收到AI智能填报请求 
2025-08-14 08:59:48.5595 | 开始AI智能填报QMO工单，文件名: 2025年7月11日01：00-06：00漯河移动PON网络升级操作网络变更操作技术方案-PON-V3.0.docx 
2025-08-14 08:59:48.5595 | 🔄 开始文档分割 
2025-08-14 08:59:48.5707 | 开始分割文档: C:\Users\<USER>\AppData\Local\Temp\QMDAITemp\cdc93126-3457-4368-9570-a6b0b2ad5289.docx 
2025-08-14 08:59:48.5707 | 开始提取完整文档内容: C:\Users\<USER>\AppData\Local\Temp\QMDAITemp\cdc93126-3457-4368-9570-a6b0b2ad5289.docx 
