2025-08-14 08:40:53.6228 | Job qmd_itr_jobgroup.syncNoticeRecord<PERSON>ob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeRecordJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:40:53.6228 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an exception. <PERSON> threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 08:40:53.6797 | Job qmd_itr_jobgroup.syncItrOrder<PERSON><PERSON><PERSON><PERSON> threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:40:53.7258 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:40:53.7652 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:40:53.7652 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an unhandled Exception:  No service for type 'QMD.Job.Nmosp.SyncNmospUserInfoJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:40:53.7652 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:40:53.7652 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 08:40:53.7652 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:40:53.7652 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 08:40:53.8027 | Job qmd_qmo_jobgroup.syncNoticeTaskTodoJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskTodoJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:40:54.3582 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 08:40:54.3582 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 08:40:54.3896 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 08:40:54.3896 | Job qmd_itr_jobgroup.syncItrOrderInfoJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderInfoJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:40:54.3896 | Job qmd_why_jobgroup.syncNetproviderJob threw an unhandled Exception:  No service for type 'QMD.Job.Why.SyncNetproviderJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:40:55.1430 | Job qmd_why_jobgroup.syncNetproviderJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 08:40:55.1745 | Job qmd_itr_jobgroup.syncItrOrderInfoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 08:40:55.1745 | Job qmd_qmo_jobgroup.syncNoticeTaskTodoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 08:40:55.2052 | Job qmd_qmo_jobgroup.syncExpireOrderJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireOrderJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:40:55.2519 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 08:40:55.2835 | Job qmd_qa_jobgroup.syncQANetJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncQaInfoJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:40:55.2835 | Job qmd_itr_jobgroup.syncItrProductInfoJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncItrProductInfoJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:40:55.2907 | Job qmd_qmo_jobgroup.syncExpireOrderJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 08:40:55.2907 | Job cycle_group.cycleRecordJob threw an unhandled Exception:  No service for type 'QMD.Job.Cycle.CycleRecordJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:40:55.3246 | Job qmd_itr_jobgroup.syncItrSolutionJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrSolutionJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:40:55.3246 | Job qmd_qa_jobgroup.syncQANetJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 08:40:55.3246 | Job qmd_itr_jobgroup.syncItrProductInfoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 08:40:55.3582 | Job cycle_group.cycleRecordJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 08:40:55.3582 | Job qmd_itr_jobgroup.syncItrSolutionJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 08:40:55.3582 | Job qmd_itr_jobgroup.syncDingtalkUserJob threw an unhandled Exception:  No service for type 'QMD.Job.Dingtalk.SyncDingtalkUserJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:40:55.3582 | Job qmd_qa_jobgroup.SyncQaDashBoardRedisJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncQaDashBoardRedisJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:40:55.3582 | Job qmd_qmo_jobgroup.syncNoticeDisOrderJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeDisOrderJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:40:55.3582 | Job qmd_qa_jobgroup.SyncQaDashBoardRedisJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 08:40:55.3729 | Job qmd_itr_jobgroup.syncDingtalkUserJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 08:40:55.3729 | Job qmd_qmo_jobgroup.syncNoticeTaskResultJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskResultJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:40:55.3729 | Job qmd_qmo_jobgroup.syncNoticeDisOrderJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 08:40:55.3729 | Job qmd_qmo_jobgroup.syncNoticeTaskResultJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 08:40:55.4643 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-14 08:42:16.6223 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:42:16.6223 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:42:16.6223 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 08:42:16.6223 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 08:44:08.7710 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:44:08.7843 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 08:44:08.8352 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:44:09.4833 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 08:45:01.0088 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:45:01.0088 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 08:45:39.7147 | [GetWhyIINetproviders]YYY：Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization 
 _IOCompletionCallback.PerformIOCompletionCallback => AwaitableSocketAsyncEventArgs.OnCompleted => AwaitableSocketAsyncEventArgs.InvokeContinuation 

2025-08-14 08:45:39.7612 | [GetWhyIINetproviders]同步维护域网络组织机构数据发生异常：Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization 
 _IOCompletionCallback.PerformIOCompletionCallback => AwaitableSocketAsyncEventArgs.OnCompleted => AwaitableSocketAsyncEventArgs.InvokeContinuation 

2025-08-14 08:46:00.3882 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:46:00.3882 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:46:00.3882 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 08:46:00.3882 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 08:46:23.9172 | [GetWhyIINetproviders]YYY：Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization 
 _IOCompletionCallback.PerformIOCompletionCallback => AwaitableSocketAsyncEventArgs.OnCompleted => AwaitableSocketAsyncEventArgs.InvokeContinuation 

2025-08-14 08:46:23.9693 | [GetWhyIINetproviders]同步维护域网络组织机构数据发生异常：Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization 
 _IOCompletionCallback.PerformIOCompletionCallback => AwaitableSocketAsyncEventArgs.OnCompleted => AwaitableSocketAsyncEventArgs.InvokeContinuation 

2025-08-14 08:48:00.0416 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:48:00.0416 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:48:00.0416 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 08:48:00.0416 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 08:50:00.0255 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:50:00.0255 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 08:50:00.0946 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:50:00.0946 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:50:00.0946 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:50:00.0946 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 08:50:00.1051 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 08:50:00.1051 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:50:00.1051 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 08:50:00.1051 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 08:50:00.1707 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-14 08:52:00.0344 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:52:00.0344 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:52:00.0344 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 08:52:00.0344 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 08:54:00.0291 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:54:00.0384 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 08:54:00.0703 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:54:00.0703 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 08:55:00.0299 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:55:00.0299 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 08:56:00.0313 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:56:00.0313 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:56:00.0313 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 08:56:00.0313 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 08:58:00.0357 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:58:00.0725 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 08:58:00.0725 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 08:58:00.0725 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 09:01:21.1938 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an unhandled Exception:  No service for type 'QMD.Job.Nmosp.SyncNmospUserInfoJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 09:01:21.1938 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeRecordJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 09:01:21.1938 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 09:01:21.2017 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 09:01:21.2017 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 09:01:21.2462 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 09:01:21.2462 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 09:01:21.2462 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 09:01:21.2610 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 09:01:21.2610 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 09:01:21.2978 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 09:01:21.3236 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 09:01:21.3236 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 09:01:21.3236 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 09:01:21.3702 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-14 09:02:03.9098 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 09:02:03.9098 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 09:02:03.9098 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 09:02:03.9098 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 09:03:05.4723 | [GetWhyIINetproviders]YYY：Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization 
 _IOCompletionCallback.PerformIOCompletionCallback => AwaitableSocketAsyncEventArgs.OnCompleted => AwaitableSocketAsyncEventArgs.InvokeContinuation 

2025-08-14 09:03:05.6201 | [GetWhyIINetproviders]同步维护域网络组织机构数据发生异常：Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization Call failed with status code 502 (Bad Gateway): POST http://120.202.24.195:30080/api2/thirdSystem/qmo/getNetOrganization 
 _IOCompletionCallback.PerformIOCompletionCallback => AwaitableSocketAsyncEventArgs.OnCompleted => AwaitableSocketAsyncEventArgs.InvokeContinuation 

2025-08-14 09:04:01.5918 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 09:04:01.6329 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 09:04:01.6329 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 09:04:01.6659 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 09:05:00.0191 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 09:05:00.0191 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 09:06:00.0324 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 09:06:00.0683 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 09:06:00.0683 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 09:06:00.0683 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 09:08:00.0264 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 09:08:00.0389 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 09:08:00.0389 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 09:08:00.0389 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 09:10:00.0351 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 09:10:00.0720 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 09:10:00.0987 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 09:10:00.1298 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 09:10:00.1298 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 09:10:00.1298 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 09:10:00.1298 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 09:10:00.1298 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 09:10:00.1298 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 09:10:00.1298 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 09:10:00.2097 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-14 11:24:10.4977 | Job qmd_itr_jobgroup.syncDingtalkUserJob threw an unhandled Exception:  No service for type 'QMD.Job.Dingtalk.SyncDingtalkUserJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-14 11:24:10.4977 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an unhandled Exception:  No service for type 'QMD.Job.Nmosp.SyncNmospUserInfoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-14 11:24:10.4977 | Job qmd_qa_jobgroup.syncQANetJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncQaInfoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-14 11:24:10.4977 | Job qmd_qmo_jobgroup.syncNoticeTaskResultJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskResultJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-14 11:24:10.4977 | Job qmd_qmo_jobgroup.syncNoticeTaskTodoJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskTodoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-14 11:24:10.4977 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-14 11:24:10.4977 | Job qmd_qa_jobgroup.syncQANetJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:24:10.4977 | Job qmd_qmo_jobgroup.syncNoticeTaskTodoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:24:10.4977 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:24:10.4977 | Job qmd_qmo_jobgroup.syncNoticeTaskResultJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:24:10.4977 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:24:10.4977 | Job qmd_itr_jobgroup.syncDingtalkUserJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:24:11.9143 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-14 11:25:00.0273 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-14 11:25:00.0273 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:26:02.6041 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-14 11:26:02.6041 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-14 11:26:03.2009 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:26:03.2009 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:26:32.4018 | 提取基础信息失败 Cannot access a disposed context instance. A common cause of this error is disposing a context instance that was resolved from dependency injection and then later trying to use the same context instance elsewhere in your application. This may occur if you are calling 'Dispose' on the context instance, or wrapping it in a using statement. If you are using dependency injection, you should let the dependency injection container take care of disposing context instances.
Object name: 'QmdDbContext'. 
 AsyncStateMachineBox`1.ExecutionContextCallback => <ProcessAIFillFormInBackground>d__67.MoveNext => AIServices.ExtractBasicInfoAsync 

2025-08-14 11:28:00.0467 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:28:00.0467 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:28:00.0467 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:28:00.0467 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:30:00.0217 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:30:00.0593 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:30:00.0768 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeRecordJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:30:00.0884 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:30:00.1381 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:30:00.1692 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:30:00.1692 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:30:00.1692 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:30:00.1692 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:30:00.1692 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:30:00.1692 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:30:00.1692 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:30:00.2627 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-14 11:32:00.0290 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:32:00.0290 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:32:00.0658 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:32:00.0868 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:34:00.0504 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:34:00.0504 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:34:00.0504 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:34:00.0590 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:35:00.0333 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:35:00.0333 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:36:00.0325 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:36:00.0325 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:36:00.0325 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:36:00.0325 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:38:00.0386 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:38:00.0455 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:38:00.0455 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:38:00.0455 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:40:00.0359 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:40:00.0737 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:40:00.1153 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:40:00.1214 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:40:00.1712 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:40:00.1712 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:40:00.1712 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:40:00.1874 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:40:00.1874 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:40:00.1874 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:40:00.2592 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-14 11:42:00.0243 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:42:00.0243 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:42:00.0243 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:42:00.0243 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:44:00.0292 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:44:00.0292 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:44:00.0420 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:44:00.0420 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 11:45:00.0253 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:45:00.0253 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 11:46:00.0211 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:46:00.0313 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 11:46:00.0313 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:46:00.0313 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 11:48:00.0407 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:48:00.0407 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 11:48:00.0407 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 11:48:00.0407 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:32:45.3713 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:32:45.3713 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:32:45.3713 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:32:45.3713 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:32:45.3713 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an unhandled Exception:  No service for type 'QMD.Job.Nmosp.SyncNmospUserInfoJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:32:45.3713 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:32:46.6192 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:32:46.6192 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:32:46.6534 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:32:46.6534 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeRecordJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:32:46.6534 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:32:46.6534 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:32:46.6534 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:32:47.8556 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:33:04.1760 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-14 13:34:06.8814 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:34:06.8814 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:34:06.9089 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:34:07.3070 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:34:55.0016 | 提取基础信息失败 Cannot access a disposed context instance. A common cause of this error is disposing a context instance that was resolved from dependency injection and then later trying to use the same context instance elsewhere in your application. This may occur if you are calling 'Dispose' on the context instance, or wrapping it in a using statement. If you are using dependency injection, you should let the dependency injection container take care of disposing context instances.
Object name: 'QmdDbContext'. 
 AsyncStateMachineBox`1.ExecutionContextCallback => <ProcessAIFillFormInBackground>d__67.MoveNext => AIServices.ExtractBasicInfoAsync 

2025-08-14 13:35:05.6908 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:35:06.0291 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:35:48.3166 | 后台处理AI解析docx文档任务失败: 680b5f8e-6e6c-4a93-83a0-f12bfc9af11d Cannot access a disposed object.
Object name: 'FileBufferingReadStream'. 
 Task`1.InnerInvoke => <>c__DisplayClass66_1.<StartParseDocxTaskAsync>b__0 => AIServices.ProcessAIFillFormInBackground 

2025-08-14 13:36:00.0365 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:36:00.0365 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:36:00.0365 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:36:00.0365 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:36:15.4539 | 后台处理AI解析docx文档任务失败: 708d54f9-4c9e-47ef-81e3-1b3cc4741454 Cannot access a disposed object.
Object name: 'FileBufferingReadStream'. 
 Task`1.InnerInvoke => <>c__DisplayClass66_1.<StartParseDocxTaskAsync>b__0 => AIServices.ProcessAIFillFormInBackground 

2025-08-14 13:38:00.0312 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:38:00.0312 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:38:00.0312 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:38:00.0312 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:40:00.0257 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:40:00.0639 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:40:00.0758 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:40:00.0758 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:40:00.1008 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:40:00.1055 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:40:00.1055 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:40:00.1055 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:40:00.1055 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:40:00.1055 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:40:00.1697 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-14 13:42:00.0422 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:42:00.0701 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:42:00.0709 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:42:00.0709 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:44:00.0373 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:44:00.0373 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:44:00.0390 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:44:00.0390 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:45:00.0170 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:45:00.0170 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:46:00.0343 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:46:00.0343 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:46:00.0343 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:46:00.0343 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:48:00.0416 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:48:00.0416 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:48:00.0416 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:48:00.0416 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:50:00.0298 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:50:00.0719 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:50:00.1246 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:50:00.1246 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:50:00.1324 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:50:00.1574 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:50:00.1574 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:50:00.1574 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:50:00.1636 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:50:00.1636 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:50:00.2403 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-14 13:52:00.0476 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:52:00.0476 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:52:00.0476 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:52:00.0476 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:54:00.0448 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:54:00.0863 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:54:00.0863 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:54:00.0863 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:55:00.0229 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:55:00.0255 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:56:00.0308 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:56:00.0619 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:56:00.0619 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:56:00.0619 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:58:00.0310 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:58:00.0417 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:58:00.0417 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 13:58:00.0417 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 13:59:31.0710 | 后台处理AI解析docx文档任务失败: d67d76dd-5055-45e5-a808-ab59e11295c5 Cannot access a disposed object.
Object name: 'FileBufferingReadStream'. 
 Task`1.InnerInvoke => <>c__DisplayClass66_1.<StartParseDocxTaskAsync>b__0 => AIServices.ProcessAIFillFormInBackground 

2025-08-14 13:59:34.5201 | 后台处理AI解析docx文档任务失败: 5ce008af-d9de-48b3-85f9-8f8e5b249b96 Cannot access a disposed object.
Object name: 'FileBufferingReadStream'. 
 Task`1.InnerInvoke => <>c__DisplayClass66_1.<StartParseDocxTaskAsync>b__0 => AIServices.ProcessAIFillFormInBackground 

2025-08-14 13:59:35.2894 | 后台处理AI解析docx文档任务失败: c5b5edc9-dec3-45e5-85ec-e690c87dc0f7 Cannot access a disposed object.
Object name: 'FileBufferingReadStream'. 
 Task`1.InnerInvoke => <>c__DisplayClass66_1.<StartParseDocxTaskAsync>b__0 => AIServices.ProcessAIFillFormInBackground 

2025-08-14 13:59:35.7817 | 后台处理AI解析docx文档任务失败: 1cef34ca-f58f-4a8a-ad89-49b6dfdaccad Cannot access a disposed object.
Object name: 'FileBufferingReadStream'. 
 Task`1.InnerInvoke => <>c__DisplayClass66_1.<StartParseDocxTaskAsync>b__0 => AIServices.ProcessAIFillFormInBackground 

2025-08-14 13:59:35.9882 | 后台处理AI解析docx文档任务失败: 540fd8a6-deb5-4038-b423-a73b0598370e Cannot access a disposed object.
Object name: 'FileBufferingReadStream'. 
 Task`1.InnerInvoke => <>c__DisplayClass66_1.<StartParseDocxTaskAsync>b__0 => AIServices.ProcessAIFillFormInBackground 

2025-08-14 13:59:36.3096 | 后台处理AI解析docx文档任务失败: 8bc6ee5b-9ec0-4f74-807b-dba0bf7fcb19 Cannot access a disposed object.
Object name: 'FileBufferingReadStream'. 
 Task`1.InnerInvoke => <>c__DisplayClass66_1.<StartParseDocxTaskAsync>b__0 => AIServices.ProcessAIFillFormInBackground 

2025-08-14 13:59:56.1264 | 后台处理AI解析docx文档任务失败: e300f278-1888-4ea5-9e53-e69631a74deb Cannot access a disposed object.
Object name: 'FileBufferingReadStream'. 
 Task`1.InnerInvoke => <>c__DisplayClass66_1.<StartParseDocxTaskAsync>b__0 => AIServices.ProcessAIFillFormInBackground 

2025-08-14 14:00:00.0608 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.ITR.SyncItrOrderTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 14:00:00.0725 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeRecordJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 14:00:00.0725 | Job qmd_itr_jobgroup.syncItrOrderTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 14:00:00.0725 | Job qmd_itr_jobgroup.syncNoticeRecordJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 14:00:00.0725 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 14:00:00.0725 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an unhandled Exception:  No service for type 'QMD.Job.Nmosp.SyncNmospUserInfoJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 14:00:00.1382 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 14:00:00.1382 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 14:00:00.1382 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 14:00:00.1560 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 14:00:00.1788 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 14:00:00.1885 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskAdventJob' has been registered. 
 AsyncMethodBuilderCore.Start => <Run>d__10.MoveNext => NLogLogger.Log 

2025-08-14 14:00:00.1885 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 14:00:00.1885 | Job qmd_qmo_jobgroup.syncNoticeTaskAdventJob threw an exception. Job threw an unhandled exception. 
 <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError => NLogLogger.Log 

2025-08-14 14:00:00.2388 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-14 14:01:55.0489 | Job qmd_qa_jobgroup.syncQANetJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncQaInfoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-14 14:01:55.0489 | Job qmd_qmo_jobgroup.syncNoticeTaskTodoJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskTodoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-14 14:01:55.0489 | Job qmd_itr_jobgroup.syncDingtalkUserJob threw an unhandled Exception:  No service for type 'QMD.Job.Dingtalk.SyncDingtalkUserJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-14 14:01:55.0489 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an unhandled Exception:  No service for type 'QMD.Job.Nmosp.SyncNmospUserInfoJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-14 14:01:55.0489 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an unhandled Exception:  No service for type 'QMD.Job.Evaluation.SyncInpectItemJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-14 14:01:55.0489 | Job qmd_qmo_jobgroup.syncNoticeTaskResultJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncNoticeTaskResultJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-14 14:01:55.0489 | Job qmd_itr_jobgroup.syncNmospUserInfoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 14:01:55.0489 | Job qmd_qa_jobgroup.syncQANetJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 14:01:55.0489 | Job qmd_qmo_jobgroup.syncNoticeTaskTodoJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 14:01:55.0489 | Job qmd_itr_jobgroup.syncDingtalkUserJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 14:01:55.0489 | Job qmd_qa_jobgroup.syncNetInspectItemJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 14:01:55.0489 | Job qmd_qmo_jobgroup.syncNoticeTaskResultJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 14:01:56.4695 | 获取客户回复邮件信息异常：Unknown column 'e.CustomAuthType' in 'field list' Unknown column 'e.CustomAuthType' in 'field list' 
 AsyncMethodBuilderCore.Start => <Execute>d__3.MoveNext => EmtJobService.CustomerConfirmForRepAndTask 

2025-08-14 14:02:00.0316 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-14 14:02:00.0316 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-14 14:02:00.0316 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 14:02:00.0316 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 14:02:50.6504 | 提取基础信息失败 Cannot access a disposed context instance. A common cause of this error is disposing a context instance that was resolved from dependency injection and then later trying to use the same context instance elsewhere in your application. This may occur if you are calling 'Dispose' on the context instance, or wrapping it in a using statement. If you are using dependency injection, you should let the dependency injection container take care of disposing context instances.
Object name: 'QmdDbContext'. 
 AsyncMethodBuilderCore.Start => <ProcessAIFillFormInBackground>d__67.MoveNext => AIServices.ExtractBasicInfoAsync 

2025-08-14 14:04:00.0533 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.EscalatedTaskRemindJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-14 14:04:00.0872 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an unhandled Exception:  No service for type 'QMD.Job.QMOV2Job.SyncExpireTaskJob' has been registered. 
 <Run>d__10.MoveNext => LoggerExecutionWrapper.Log => NLogLogger.Log 

2025-08-14 14:04:00.0872 | Job qmd_qmo_jobgroup.escalatedTaskRemindJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

2025-08-14 14:04:00.0872 | Job qmd_qmo_jobgroup.syncExpireTaskJob threw an exception. Job threw an unhandled exception. 
 AsyncMethodBuilderCore.Start => <NotifySchedulerListenersError>d__151.MoveNext => ErrorLogger.SchedulerError 

